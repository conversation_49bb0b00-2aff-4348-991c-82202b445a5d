# 事件合约交易历史管理器使用指南

## 📋 概述

`EventContractTradeHistoryManager` 是一个完整的交易历史管理系统，提供从信号生成到结算跟踪的全生命周期管理功能。

## 🎯 核心功能

### 1. 交易记录管理
- **完整生命周期跟踪**: 从信号生成→决策制定→交易执行→结算完成
- **SQLite数据库存储**: 持久化存储，支持复杂查询
- **数据完整性保证**: 事务性操作，确保数据一致性

### 2. 绩效分析
- **实时统计计算**: 胜率、盈亏、连胜连败等指标
- **多维度分析**: 按时间、方向、风险等级分析
- **风险指标**: 夏普比率、最大回撤、波动率等

### 3. 数据导出
- **CSV导出**: 适合Excel分析
- **JSON导出**: 包含完整元数据
- **绩效报告**: 自动生成详细报告

## 🚀 快速开始

### 基本使用

```python
from quant.strategies.event_contract_trade_history_manager import EventContractTradeHistoryManager

# 初始化管理器
manager = EventContractTradeHistoryManager(db_path="data/trade_history.db")

# 创建交易记录
trade_record = manager.create_trade_record(signal_result, decision)

# 更新执行信息
manager.update_execution(trade_record.trade_id, order_id, predicted_price, entry_price)

# 更新结算信息
manager.update_settlement(trade_record.trade_id, final_price, result, pnl)
```

### 完整流程示例

```python
import asyncio
from datetime import datetime
from quant.strategies.event_contract_trade_history_manager import EventContractTradeHistoryManager
from quant.strategies.event_contract_decision_engine import TradingDecision, MarketCondition, RiskLevel
from quant.strategies.event_contract_signal_generator_simple import SignalResult

async def complete_trading_flow():
    # 1. 初始化管理器
    manager = EventContractTradeHistoryManager()
    
    # 2. 创建信号和决策
    signal_result = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=85.0,
        technical_score=88.0
    )
    
    decision = TradingDecision(
        should_trade=True,
        direction="UP",
        bet_amount=20.0,
        confidence=85.0,
        risk_level=RiskLevel.MEDIUM,
        market_condition=MarketCondition.TRENDING,
        reason="技术指标显示上涨趋势",
        timestamp=datetime.now(),
        max_loss_allowed=1000.0,
        current_daily_loss=0.0,
        position_size_ratio=0.1,
        market_score=88.0,
        signal_strength=85.0,
        entry_timing="良好"
    )
    
    # 3. 创建交易记录
    trade_record = manager.create_trade_record(signal_result, decision)
    
    # 4. 更新执行信息
    order_id = "ORDER_001"
    manager.update_execution(trade_record.trade_id, order_id, 100.0, 99.95)
    
    # 5. 模拟交易时间
    await asyncio.sleep(1)
    
    # 6. 更新结算信息
    manager.update_settlement(trade_record.trade_id, 102.5, "win", 16.0)
    
    # 7. 获取统计信息
    metrics = manager.calculate_performance_metrics()
    print(f"胜率: {metrics.win_rate:.1%}, 总盈亏: {metrics.total_pnl:+.2f} USDT")
```

## 📊 数据结构

### TradeRecord 交易记录

```python
@dataclass
class TradeRecord:
    # 基础信息
    trade_id: str                      # 交易ID
    order_id: str                      # 订单ID
    symbol: str                        # 交易对
    direction: str                     # 方向 (UP/DOWN)
    
    # 信号相关
    signal_timestamp: datetime         # 信号生成时间
    signal_confidence: float           # 信号信心度
    signal_strength: float             # 信号强度
    technical_score: float             # 技术分析得分
    market_condition: str              # 市场条件
    
    # 决策相关
    decision_timestamp: datetime       # 决策时间
    should_trade: bool                 # 是否交易
    bet_amount: float                  # 投注金额
    risk_level: str                    # 风险等级
    decision_reason: str               # 决策原因
    
    # 执行相关
    execution_timestamp: Optional[datetime] = None  # 执行时间
    predicted_price: Optional[float] = None         # 预测价格
    entry_price: Optional[float] = None             # 入场价格
    
    # 结算相关
    settlement_timestamp: Optional[datetime] = None  # 结算时间
    final_price: Optional[float] = None              # 最终价格
    result: Optional[str] = None                     # 结果 (win/loss/tie)
    pnl: Optional[float] = None                      # 盈亏
    return_rate: Optional[float] = None              # 收益率
    
    # 状态
    status: str = "pending"            # 状态 (pending/executed/settled/cancelled)
```

### TradingPerformanceMetrics 绩效指标

```python
@dataclass
class TradingPerformanceMetrics:
    # 基础统计
    total_trades: int = 0
    completed_trades: int = 0
    wins: int = 0
    losses: int = 0
    ties: int = 0
    win_rate: float = 0.0
    
    # 盈亏统计
    total_pnl: float = 0.0
    total_invested: float = 0.0
    avg_bet_amount: float = 0.0
    avg_win_amount: float = 0.0
    avg_loss_amount: float = 0.0
    max_win: float = 0.0
    max_loss: float = 0.0
    
    # 连胜连败
    current_streak: int = 0
    max_win_streak: int = 0
    max_loss_streak: int = 0
    
    # 风险指标
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    volatility: float = 0.0
    
    # 按方向统计
    up_trades: int = 0
    down_trades: int = 0
    up_win_rate: float = 0.0
    down_win_rate: float = 0.0
```

## 🔧 核心方法

### 交易记录管理

```python
# 创建交易记录
trade_record = manager.create_trade_record(signal_result, decision, trade_id=None)

# 更新执行信息
success = manager.update_execution(trade_id, order_id, predicted_price, entry_price)

# 更新结算信息
success = manager.update_settlement(trade_id, final_price, result, pnl)

# 从合约记录更新
success = manager.update_from_contract_record(contract_record)
```

### 查询功能

```python
# 获取单个交易记录
trade_record = manager.get_trade_record(trade_id)

# 获取交易记录列表
records = manager.get_trade_records(
    status="settled",           # 状态过滤
    result="win",              # 结果过滤
    start_date=date(2025, 1, 1), # 开始日期
    end_date=date(2025, 12, 31), # 结束日期
    limit=100                   # 数量限制
)
```

### 统计分析

```python
# 计算绩效指标
metrics = manager.calculate_performance_metrics(
    start_date=date(2025, 1, 1),
    end_date=date(2025, 12, 31)
)

# 获取每日统计
daily_stats = manager.get_daily_stats(target_date=date.today())

# 获取每周统计
weekly_stats = manager.get_weekly_stats(target_date=date.today())

# 获取每月统计
monthly_stats = manager.get_monthly_stats(target_date=date.today())
```

### 数据导出

```python
# 导出CSV
csv_path = manager.export_to_csv(
    filepath="exports/trades.csv",
    start_date=date(2025, 1, 1),
    end_date=date(2025, 12, 31)
)

# 导出JSON
json_path = manager.export_to_json(
    filepath="exports/trades.json",
    start_date=date(2025, 1, 1),
    end_date=date(2025, 12, 31)
)

# 生成绩效报告
report = manager.generate_performance_report(
    start_date=date(2025, 1, 1),
    end_date=date(2025, 12, 31),
    save_to_file=True
)
```

## 📈 绩效报告示例

```
============================================================
📊 事件合约交易绩效报告
============================================================
📅 报告时间: 2025-07-12 10:57:29
📅 统计期间: 全部历史数据

🎯 交易概览
------------------------------
总交易数: 5
已完成交易: 5
待处理交易: 0
已取消交易: 0

💰 盈亏统计
------------------------------
胜利次数: 2
失败次数: 2
平局次数: 1
胜率: 40.00%
总盈亏: -8.00 USDT
总投入: 100.00 USDT
投资回报率: -8.00%

📈 交易分析
------------------------------
平均投注金额: 20.00 USDT
平均盈利金额: 16.00 USDT
平均亏损金额: -20.00 USDT
最大单笔盈利: 16.00 USDT
最大单笔亏损: -20.00 USDT

🔥 连胜连败分析
------------------------------
当前连败: 2 次
最大连胜: 2 次
最大连败: 2 次

📊 方向分析
------------------------------
看涨交易: 3 次，胜率: 33.33%
看跌交易: 2 次，胜率: 50.00%

⚠️ 风险指标
------------------------------
夏普比率: -0.2500
最大回撤: 40.00 USDT
波动率: 16.00

⏰ 时间分析
------------------------------
平均交易持续时间: 10.0 分钟
总交易时间: 0.8 小时

📅 今日表现
------------------------------
今日交易: 5 次
今日胜率: 40.00%
今日盈亏: -8.00 USDT
今日投资回报率: -8.00%
```

## 🔄 与其他组件集成

### 与结算检查器集成

```python
from quant.strategies.event_contract_settlement_checker import EventContractSettlementChecker

# 创建回调函数
def settlement_callback(contract_record):
    """结算回调函数"""
    # 更新交易历史
    manager.update_from_contract_record(contract_record)
    
    # 获取最新统计
    metrics = manager.calculate_performance_metrics()
    print(f"最新胜率: {metrics.win_rate:.1%}")

# 添加回调
settlement_checker = EventContractSettlementChecker()
settlement_checker.add_settlement_callback(settlement_callback)
```

### 与钉钉通知集成

```python
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier

# 发送统计报告
notifier = EventContractDingtalkNotifier()
daily_stats = manager.get_daily_stats()
metrics = manager.calculate_performance_metrics()

# 构建统计数据
stats = {
    'total_trades': metrics.total_trades,
    'wins': metrics.wins,
    'losses': metrics.losses,
    'ties': metrics.ties,
    'win_rate': metrics.win_rate,
    'total_pnl': metrics.total_pnl,
    'avg_bet': metrics.avg_bet_amount,
    'current_streak': metrics.current_streak
}

risk_summary = {
    'daily_loss': abs(min(0, daily_stats.pnl)),
    'daily_loss_ratio': abs(daily_stats.pnl / daily_stats.invested) if daily_stats.invested > 0 else 0,
    'remaining_soft_limit': 1000.0 - abs(min(0, daily_stats.pnl))
}

# 发送每日总结
notifier.send_daily_summary(stats, risk_summary)
```

## 🛠️ 维护功能

### 数据清理

```python
# 清理90天前的旧记录
manager.cleanup_old_records(days_to_keep=90)

# 重置统计数据
manager.reset_statistics()
```

### 状态监控

```python
# 获取系统状态
status = manager.get_status()
print(f"总交易数: {status['total_trades']}")
print(f"数据库路径: {status['database_path']}")
print(f"最新交易: {status['latest_trade']}")
print(f"状态分布: {status['status_distribution']}")
```

## ⚠️ 注意事项

1. **数据库路径**: 确保数据库目录存在且有写权限
2. **并发安全**: SQLite支持多读单写，注意并发访问
3. **内存使用**: 大量数据时考虑分页查询
4. **备份策略**: 定期备份数据库文件
5. **性能优化**: 适当创建索引提升查询性能

## 🔮 扩展功能

### 自定义统计指标

```python
# 继承并扩展绩效指标
@dataclass
class CustomPerformanceMetrics(TradingPerformanceMetrics):
    custom_metric: float = 0.0
    
    def calculate_custom_metric(self, records):
        # 自定义计算逻辑
        pass
```

### 自定义导出格式

```python
# 自定义导出函数
def export_to_excel(manager, filepath):
    records = manager.get_trade_records()
    # 使用pandas导出到Excel
    import pandas as pd
    df = pd.DataFrame([asdict(record) for record in records])
    df.to_excel(filepath, index=False)
```

---

**更新时间**: 2025-07-12  
**版本**: v1.0.0  
**状态**: ✅ 完成 