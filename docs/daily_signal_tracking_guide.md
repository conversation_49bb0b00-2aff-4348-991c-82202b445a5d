# 每日信号跟踪功能使用指南

## 📊 功能概述

该功能为币安事件合约自动化交易系统添加了**按15分钟K线顺序排序**的钉钉信号跟踪功能，让用户能够清晰地了解每日信号发送情况。

## 🎯 核心特性

### 1. **K线序号标识**
- 每个信号都标记对应的15分钟K线序号（1-96）
- 一天共96根15分钟K线（24小时 × 4）
- 序号计算：`(小时 × 4) + (分钟 ÷ 15) + 1`

### 2. **钉钉消息增强**
- 信号消息顶部显示K线序号和时间
- 包含今日信号进度条
- 显示当前K线在全天的位置

### 3. **历史查询功能**
- 按时间顺序排序显示所有信号
- 统计看涨/看跌信号数量
- 分析不同时间段的信号分布

## 🔧 使用方法

### 启动策略（自动跟踪）
```bash
# 启动主策略，自动开始K线跟踪
python scripts/run_event_contract_strategy.py
```

### 查询今日信号历史
```bash
# 独立查询工具，不需要策略运行
python scripts/query_daily_signals.py
```

## 📱 钉钉消息格式

### 增强后的信号通知格式：
```
🎯 交易信号通知 📉

📊 K线序号: 第54根15分钟K线 (13:30)
🔢 信号序号: 今日第127个信号
==============================

> 交易方向: DOWN 📉
> 投注金额: 19.92 USDT
> 信心度: 49.0%
> 技术分析: 100.0分
> 风险等级: HIGH
> 市场条件: NORMAL
> 决策原因: 满足交易条件：信心度49.0%，风险medium
> 信号时间: 2025-07-12 13:36:42

📈 今日进度: 54/96 (15分钟K线)

🎉 祝交易顺利！📉小火箭📉起飞！
```

## 📋 查询结果示例

### 控制台输出格式：
```
================================================================================
📊 今日钉钉信号发送历史 - 2025-07-12
================================================================================
📈 信号发送统计:
   📡 总信号数: 25
   🚀 看涨信号: 8
   📉 看跌信号: 17
   📊 当前K线: 第54/96根 (15分钟)

📋 信号详情（按15分钟K线顺序排序）:
序号 K线序号    K线时间    方向     钉钉发送时间 置信度
--------------------------------------------------------------------------------
1    第32根     08:00     📉 DOWN  08:01:15    52.3%
2    第33根     08:15     🚀 UP    08:16:22    48.7%
3    第34根     08:30     📉 DOWN  08:31:45    51.1%
...

📊 K线时间分布分析:
   08:00-08:59 -> 🚀2 📉6 (共8个)
   09:00-09:59 -> 🚀1 📉4 (共5个)
   10:00-10:59 -> 🚀3 📉2 (共5个)
   11:00-11:59 -> 🚀2 📉5 (共7个)
```

## 🔍 技术实现

### 1. **K线序号计算**
```python
# 计算当前15分钟K线序号
current_15m_slot = (current_time.hour * 4) + (current_time.minute // 15) + 1

# 示例：
# 08:00 -> (8 * 4) + (0 // 15) + 1 = 33
# 08:15 -> (8 * 4) + (15 // 15) + 1 = 34
# 13:30 -> (13 * 4) + (30 // 15) + 1 = 54
```

### 2. **数据结构**
```python
daily_kline_tracker = {
    'date': '2025-07-12',
    'kline_15m_count': 54,      # 当前K线序号
    'signal_count': 127,        # 今日信号总数
    'last_kline_time': datetime,
    'kline_sequence': [...]     # K线序列历史
}
```

### 3. **通知增强**
- 主策略中添加`_update_kline_tracker()`方法
- 钉钉通知器中修改`_build_signal_message()`方法
- 每个信号都携带序号和进度信息

## 📊 数据统计

### 自动统计项目：
- **总信号数**：今日发送的所有信号
- **方向分布**：看涨vs看跌信号数量
- **时间分布**：各小时段的信号分布
- **K线进度**：当前进度/总进度（x/96）

### 小时报告增强：
每小时自动发送的报告中包含：
- 今日信号发送统计
- 当前K线进度
- 看涨/看跌信号比例

## 🚀 使用场景

### 1. **实时监控**
- 策略运行时自动跟踪K线序号
- 钉钉消息中显示当前进度
- 便于了解信号在全天的分布

### 2. **历史分析**
- 查看某日的完整信号历史
- 分析不同时间段的信号特征
- 统计策略的信号生成规律

### 3. **性能评估**
- 按K线顺序评估信号质量
- 分析时间段与信号准确性的关系
- 优化策略参数

## ⚠️ 注意事项

1. **时区设置**：确保系统时间正确，影响K线序号计算
2. **数据存储**：通知历史存储在内存中，重启后清空
3. **序号重置**：每日00:00自动重置K线序号计数
4. **网络延迟**：钉钉发送时间可能略晚于信号生成时间

## 📈 后续优化

- [ ] 添加数据库持久化存储
- [ ] 支持历史多日查询
- [ ] 增加信号质量评分
- [ ] 添加图表可视化展示
- [ ] 支持导出Excel报告

---

**版本**: v1.0  
**更新时间**: 2025-07-12  
**作者**: AI Assistant 