# 事件合约结算检查器使用指南

## 📋 概述

事件合约结算检查器是币安事件合约自动化交易系统的核心组件之一，负责自动监控和结算到期的合约，统计交易胜率和盈亏情况。

## 🎯 核心功能

### 1. 自动合约监控
- 跟踪所有下单的事件合约
- 自动检测10分钟到期的合约
- 实时监控合约状态

### 2. 自动结算处理
- 获取合约到期时的最终价格
- 自动判断合约结果（盈利/亏损）
- 计算准确的盈亏金额

### 3. 统计数据管理
- 实时统计交易胜率
- 跟踪总盈亏和投入金额
- 记录连胜/连败情况
- 按日期统计交易数据

### 4. 通知集成
- 自动发送结算通知到钉钉
- 支持自定义回调函数
- 实时推送交易结果

### 5. 数据导出
- 导出完整交易历史
- 生成统计报告
- 支持JSON格式数据

## 🔧 基本用法

### 初始化结算检查器

```python
from quant.strategies.event_contract_settlement_checker import EventContractSettlementChecker
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier

# 创建钉钉通知器
dingtalk_notifier = EventContractDingtalkNotifier()

# 创建结算检查器
settlement_checker = EventContractSettlementChecker(
    dingtalk_notifier=dingtalk_notifier,
    check_interval=30  # 30秒检查一次
)
```

### 添加合约记录

```python
# 添加待结算合约
settlement_checker.add_contract(
    order_id="ORDER_001",
    symbol="BTCUSDT",
    direction="UP",
    bet_amount=20.0,
    predicted_price=67500.0,
    decision=trading_decision,
    expiry_minutes=10  # 10分钟到期
)
```

### 启动监控

```python
# 开始监控
settlement_checker.start_monitoring()

# 停止监控
settlement_checker.stop_monitoring()
```

## 📊 统计功能

### 获取实时统计

```python
# 获取总体统计
stats = settlement_checker.get_statistics()
print(f"总交易数: {stats.total_trades}")
print(f"胜率: {stats.win_rate:.1%}")
print(f"总盈亏: {stats.total_pnl:.2f} USDT")
print(f"当前连胜/连败: {stats.current_streak}")

# 获取每日统计
daily_stats = settlement_checker.get_daily_stats()
print(f"今日交易数: {daily_stats['trades']}")
print(f"今日胜率: {daily_stats['win_rate']:.1%}")
print(f"今日盈亏: {daily_stats['pnl']:.2f} USDT")
```

### 查看合约记录

```python
# 获取待结算合约
pending_contracts = settlement_checker.get_pending_contracts()
for order_id, contract in pending_contracts.items():
    print(f"{order_id}: {contract.direction} - {contract.time_to_expiry():.0f}秒后到期")

# 获取已结算合约
settled_contracts = settlement_checker.get_settled_contracts(limit=10)
for contract in settled_contracts:
    print(f"{contract.order_id}: {contract.result} - PnL: {contract.pnl:.2f} USDT")
```

## 📤 数据导出

### 导出交易历史

```python
# 导出到默认文件
export_path = settlement_checker.export_trading_history()
print(f"交易历史已导出到: {export_path}")

# 导出到指定文件
export_path = settlement_checker.export_trading_history("my_trading_history.json")
```

### 导出数据格式

```json
{
  "statistics": {
    "total_trades": 10,
    "wins": 6,
    "losses": 4,
    "win_rate": 0.6,
    "total_pnl": 32.0,
    "total_invested": 200.0,
    "avg_bet_amount": 20.0,
    "current_streak": 2,
    "daily_stats": {
      "2025-01-31": {
        "trades": 5,
        "wins": 3,
        "losses": 2,
        "win_rate": 0.6,
        "pnl": 16.0,
        "invested": 100.0
      }
    }
  },
  "settled_contracts": [
    {
      "order_id": "ORDER_001",
      "symbol": "BTCUSDT",
      "direction": "UP",
      "bet_amount": 20.0,
      "predicted_price": 67500.0,
      "final_price": 67650.0,
      "result": "win",
      "pnl": 8.0,
      "return_rate": 0.4,
      "entry_time": "2025-01-31T10:00:00",
      "settlement_time": "2025-01-31T10:10:00",
      "confidence": 85.0,
      "market_condition": "trending"
    }
  ]
}
```

## 🔄 回调系统

### 添加结算回调

```python
def on_settlement_completed(contract):
    """结算完成回调"""
    print(f"合约结算: {contract.order_id} - {contract.result}")
    # 可以在这里添加自定义逻辑
    # 例如：更新策略参数、发送额外通知等

# 添加回调函数
settlement_checker.add_settlement_callback(on_settlement_completed)
```

### 异步回调

```python
async def async_settlement_callback(contract):
    """异步结算回调"""
    print(f"异步处理: {contract.order_id}")
    # 可以执行异步操作
    await some_async_operation()

settlement_checker.add_settlement_callback(async_settlement_callback)
```

## 🎮 完整示例

```python
import asyncio
from quant.strategies.event_contract_settlement_checker import EventContractSettlementChecker
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier

async def main():
    # 创建通知器和检查器
    dingtalk_notifier = EventContractDingtalkNotifier()
    settlement_checker = EventContractSettlementChecker(
        dingtalk_notifier=dingtalk_notifier,
        check_interval=30
    )
    
    # 添加回调
    def on_settlement(contract):
        print(f"结算完成: {contract.order_id} - {contract.result}")
    
    settlement_checker.add_settlement_callback(on_settlement)
    
    # 开始监控
    settlement_checker.start_monitoring()
    
    # 模拟添加合约
    settlement_checker.add_contract(
        order_id="TEST_001",
        symbol="BTCUSDT",
        direction="UP",
        bet_amount=20.0,
        predicted_price=67500.0,
        decision=trading_decision,
        expiry_minutes=1  # 1分钟到期
    )
    
    # 等待结算
    await asyncio.sleep(90)  # 等待1.5分钟
    
    # 显示统计
    stats = settlement_checker.get_statistics()
    print(f"总交易数: {stats.total_trades}")
    print(f"胜率: {stats.win_rate:.1%}")
    print(f"总盈亏: {stats.total_pnl:.2f} USDT")
    
    # 导出数据
    export_path = settlement_checker.export_trading_history()
    print(f"数据已导出到: {export_path}")
    
    # 停止监控
    settlement_checker.stop_monitoring()

if __name__ == "__main__":
    asyncio.run(main())
```

## 📈 性能特点

### 监控性能
- 异步事件循环，不阻塞主线程
- 可配置检查间隔，平衡性能和实时性
- 自动清理过期数据，防止内存泄漏

### 数据精度
- 毫秒级时间戳记录
- 精确的价格比较和盈亏计算
- 完整的交易生命周期跟踪

### 扩展性
- 支持自定义回调函数
- 可集成第三方通知系统
- 支持多种数据导出格式

## 🔧 高级配置

### 自定义价格获取

```python
class CustomPriceProvider:
    async def get_current_price(self, symbol):
        # 自定义价格获取逻辑
        return {"price": 67500.0}

settlement_checker = EventContractSettlementChecker(
    api_client=CustomPriceProvider(),
    check_interval=30
)
```

### 自定义盈亏计算

```python
class CustomSettlementChecker(EventContractSettlementChecker):
    def _calculate_pnl(self, contract, result):
        # 自定义盈亏计算逻辑
        if result == "win":
            return contract.bet_amount * 0.5  # 50%收益
        else:
            return -contract.bet_amount
```

## 🛠️ 故障排除

### 常见问题

1. **合约没有自动结算**
   - 检查是否调用了 `start_monitoring()`
   - 确认合约是否已过期
   - 查看日志输出排查错误

2. **通知发送失败**
   - 检查钉钉配置是否正确
   - 确认网络连接正常
   - 查看错误日志

3. **统计数据不准确**
   - 确认合约记录完整
   - 检查时间戳是否正确
   - 验证价格获取逻辑

### 日志调试

```python
import logging

# 启用调试日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger('quant.strategies.event_contract_settlement_checker')
logger.setLevel(logging.DEBUG)
```

## 🎯 最佳实践

1. **及时启动监控**: 在系统启动时立即开始监控
2. **合理设置检查间隔**: 平衡实时性和系统性能
3. **定期导出数据**: 防止数据丢失
4. **监控系统状态**: 定期检查运行状态
5. **异常处理**: 妥善处理网络异常和API错误

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 查看系统日志输出
2. 检查配置文件设置
3. 验证网络连接状态
4. 确认API密钥有效性

---

*更新时间: 2025-01-31*
*版本: 1.0.0* 