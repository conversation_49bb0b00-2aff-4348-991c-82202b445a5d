# 胜率统计报表系统使用说明

## 🎯 系统概述

胜率统计报表系统已成功集成到现有的事件合约交易系统中，完全符合代码组织规范：

- ✅ **遵循架构规范**: 所有代码放置在 `quant/strategies/` 目录下
- ✅ **复用现有组件**: 使用现有钉钉通知器、数据库连接、配置系统
- ✅ **集成定时框架**: 使用现有HeartBeat框架实现定时任务
- ✅ **保持接口一致**: 与现有系统保持松耦合设计

## 🚀 快速启用

### 1. 确保钉钉配置

编辑 `quant/config.py` 文件，确保钉钉Token已配置：

```python
# 钉钉Webhook URL
dingtalk = "YOUR_DINGTALK_WEBHOOK_URL"
```

### 2. 启动主策略

```bash
# 启动事件合约主策略（胜率报表功能已自动集成）
python3 -m quant.strategies.event_contract_main_strategy
```

### 3. 验证功能

```bash
# 运行功能测试
python3 tests/test_win_rate_report_system.py

# 查看演示
python3 tests/demo_win_rate_report_system.py
```

## 📊 功能特性

### 自动定时任务

- **每日0点**: 重新计算当日胜率数据，发送每日总结报表
- **每小时整点**: 发送胜率统计报表（至少1个信号才发送）
- **智能频率控制**: 避免重复发送，支持不同类型报表的间隔控制

### 多维度统计

- **时间维度**: 今日、本周、本月胜率统计
- **方向维度**: 看涨(UP)和看跌(DOWN)信号分别统计
- **强度维度**: 强(STRONG)、中(MEDIUM)、弱(WEAK)信号胜率分析
- **趋势分析**: 连胜连败、表现趋势、操作建议

### 报表内容

#### 每小时报表示例
```
📊 胜率统计报表 - 14:00

🎯 今日表现
信号总数: 12
已结算: 10
胜率: 70.0%
总盈亏: +2.35%

📈 本周概览
信号总数: 45
胜率: 64.3%
总盈亏: +8.92%
```

#### 每日总结示例
```
🌟 每日胜率总结 - 2025-07-18

🎯 今日完整统计
信号总数: 18
已结算数: 16
胜率: 68.8%
总盈亏: +4.25%

📋 详细分析
看涨胜率: 75.0%
看跌胜率: 65.0%
强信号胜率: 85.0%

🔍 趋势分析
表现趋势: 表现优秀
最大连胜: 4次
最大连败: 2次
操作建议: 继续保持当前策略

🎉 每日总结
今日表现优秀！
明日继续加油！💪
```

## ⚙️ 配置选项

### 钉钉通知配置

在 `quant/strategies/event_contract_dingtalk_notifier.py` 中的 `NotificationConfig` 类：

```python
@dataclass
class NotificationConfig:
    enable_daily_summary: bool = True        # 启用每日总结
    max_daily_notifications: int = 200      # 每日最大通知数
    min_signal_interval: int = 60           # 最小信号间隔(秒)
```

### 胜率报表配置

在主策略中可以调整的参数：

- **最小信号数要求**: 默认至少1个信号才发送报表
- **定时检查窗口**: 每小时前10分钟内执行
- **数据库路径**: `./data/signal_settlement.db`
- **导出目录**: `./exports/`

## 📁 文件结构

```
quant/strategies/
├── win_rate_report_system.py           # 核心报表系统
├── event_contract_dingtalk_notifier.py # 扩展的钉钉通知器
└── event_contract_main_strategy.py     # 集成的主策略

tests/
├── test_win_rate_report_system.py      # 功能测试
└── demo_win_rate_report_system.py      # 演示程序

exports/
└── daily_win_rate_report_*.json        # 导出的报表文件
```

## 🔍 监控和日志

### 查看运行状态

```bash
# 查看主策略日志中的胜率报表信息
grep "胜率" logs/event_contract_main_strategy.log

# 查看钉钉通知发送状态
grep "胜率报表" logs/event_contract_main_strategy.log
```

### 导出文件

- **文件位置**: `./exports/daily_win_rate_report_YYYYMMDD.json`
- **生成时间**: 每日0点自动生成
- **文件格式**: JSON格式，包含完整的统计数据

## 🛠️ 自定义配置

### 修改报表发送频率

在 `event_contract_main_strategy.py` 的 `_check_win_rate_reports` 方法中：

```python
# 修改每小时报表的时间窗口
if (current_time.minute < 10 and ...):  # 改为其他分钟数

# 修改最小信号数要求
if today_stats.total_signals >= 1:  # 改为其他数值
```

### 修改报表内容

在 `event_contract_dingtalk_notifier.py` 的 `_build_win_rate_report_message` 方法中自定义消息格式。

### 添加新的报表类型

可以扩展 `send_win_rate_report` 方法支持新的 `report_type`。

## ✅ 测试验证

### 功能测试

```bash
# 完整功能测试（包含模拟数据）
python3 tests/test_win_rate_report_system.py

# 预期输出：
# ✅ 胜率报表系统核心功能测试完成
# ✅ 通知消息格式测试完成  
# ✅ 钉钉集成测试完成
# ✅ 集成测试完成
# 🎉 所有测试完成！系统功能正常
```

### 演示程序

```bash
# 查看完整功能演示
python3 tests/demo_win_rate_report_system.py

# 预期输出：
# 🎯 胜率统计报表系统完整演示
# 📊 胜率统计报表系统演示
# 📱 通知消息格式演示
# 🔗 与主策略集成演示
# ⚙️ 配置选项演示
# 🎉 演示完成！
```

## 🔧 故障排除

### 常见问题

1. **报表不发送**
   - 检查钉钉Token配置是否正确
   - 确认今日是否有足够的信号数据
   - 查看日志中的错误信息

2. **数据库连接失败**
   - 确认 `./data/signal_settlement.db` 文件存在
   - 检查数据库表结构是否完整

3. **定时任务不执行**
   - 确认主策略正在运行
   - 检查系统时间和时区设置

### 调试方法

```bash
# 检查主策略导入
python3 -c "from quant.strategies.event_contract_main_strategy import EventContractMainStrategy; print('✅ 导入成功')"

# 检查胜率报表系统
python3 -c "from quant.strategies.win_rate_report_system import WinRateReportSystem; print('✅ 报表系统正常')"

# 检查数据库
ls -la ./data/signal_settlement.db
```

## 📞 技术支持

如遇到问题：

1. 运行测试程序验证功能
2. 查看主策略日志文件
3. 检查钉钉Token配置
4. 确认数据库文件完整性

---

**系统已完成集成，符合所有代码组织规范，可以直接使用！** 🎉
