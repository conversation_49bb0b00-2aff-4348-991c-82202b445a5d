# 事件合约信号推荐系统使用指南

> **版本：v2.0 (无 API 下单)**

本文件介绍新版信号系统的工作流程与手工下单指南。

---

## 1️⃣ 系统概览

1. **SignalGenerator**  在每根 15m K 线收盘生成 *pending_signal*（方向 + 置信度）。
2. **FactorFilter**     在下一根 15m 内以 1m/5m 数据多因子打分，确认入场时机。
3. **RecommendationEngine** 根据 *pending_signal* + 因子得分输出最终 *BUY/SELL* 建议，不执行下单。
4. **DingtalkNotifier** 通过钉钉推送格式化的推荐消息，供用户手工交易。

```
SignalGenerator ─▶ pending_signal ─▶ FactorFilter ─▶ RecommendationEngine ─▶ DingTalk
```

---

## 2️⃣ 推荐消息格式

```
### 事件合约交易推荐 🚀
- 方向: **UP**
- 建议投入: **20.00 USDT**
- 置信度: **87.0%**
- 因子得分: **45.5 /100**
- 剩余时间: 620s
- 生成时间: 2025-07-15 21:00:05
---
> 该推荐仅供参考，实际操作请自行评估风控。
```

**关键字段说明**
| 字段 | 含义 |
|------|------|
| 方向 | UP / DOWN |
| 建议投入 | 基础投注 × 资金管理系数，默认 20 USDT |
| 置信度 | pending_signal 给出的置信度 |
| 因子得分 | FactorFilter 0-100 分 |
| 剩余时间 | 合约到期前剩余秒数，应 ≥600s 才会推荐 |

---

## 3️⃣ 手工下单流程

1. 收到钉钉推荐消息后，打开 Binance App **事件合约** 页面。  
2. 确认推荐方向、投入金额与剩余时间。  
3. 手动选择 **上涨/下跌** 并输入推荐金额。  
4. 提交订单并等待 10 分钟到期结算。  

> ⚠️ 若收到多条连续推荐，请优先处理因子得分高、剩余时间长的那条。

---

## 4️⃣ 风险控制

- **每日软限制**：亏损 ≥1000 USDT 自动暂停 1h  
- **每日硬限制**：亏损 ≥10000 USDT 当天停策略  
- FactorFilter 内置时间衰减，避免尾部追单。  

---

## 5️⃣ 常见问题

**Q: 为什么有时只推送方向而没有推荐？**  
A: 因子得分 <40 或剩余时间 <600s，系统会等待更好时机或作废 pending_signal。

**Q: 可以修改基础投注吗？**  
A: 可在 `config.json` 的 `base_stake` 字段或创建自定义 `RecommendationEngine` 调整。

---

> © 2025 HertelQuant Signal System 