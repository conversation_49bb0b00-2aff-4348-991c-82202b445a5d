# 信号跟踪修复部署指南

## 🎯 修复概述

本次修复解决了以下问题：
1. ✅ **K线信号根数、信号序号无序错误** 
2. ✅ **信号ID和价格信息在通知中的显示**
3. ✅ **结算通知与生成信号的对应关系**

## 🚀 部署步骤

### 1. 备份当前配置

```bash
# 备份当前数据
cp -r data data_backup_$(date +%Y%m%d_%H%M%S)

# 备份配置文件
cp config.json config_backup_$(date +%Y%m%d_%H%M%S).json
```

### 2. 停止当前策略

```bash
# 查找并停止当前运行的策略
ps aux | grep event_contract_main_strategy
pkill -f "python.*event_contract_main_strategy"

# 确认进程已停止
ps aux | grep event_contract_main_strategy
```

### 3. 验证修复代码

```bash
# 运行修复验证脚本
python tests/verify_signal_tracking_fix.py
```

预期输出：
```
🔧 信号跟踪修复验证
==================================================
📋 验证结果汇总:
   通过: 4/6
   成功率: 66.7%
   ⚠️  部分验证未通过，请检查相关问题
```

> **注意**: 信号数据库和策略进程检查失败是正常的，因为需要策略运行后才会创建。

### 4. 启动新策略

```bash
# 启动策略
python scripts/run_event_contract_strategy.py
```

### 5. 验证运行状态

```bash
# 检查进程是否正常运行
ps aux | grep event_contract_main_strategy

# 查看日志
tail -f logs/event_contract_main_strategy.log
```

### 6. 监控修复效果

#### 6.1 检查日志输出

关注以下日志内容：
```bash
# 查看K线跟踪日志
tail -f logs/event_contract_main_strategy.log | grep "K线跟踪"

# 查看信号生成日志
tail -f logs/event_contract_main_strategy.log | grep "信号ID"
```

期待看到类似输出：
```
📊 K线跟踪更新: 第42根15分钟K线, 今日第5个信号
✅ 信号已添加到结算跟踪器: signal_1705123456789_1234
✅ 生成pending信号: UP, 置信度: 75.0%, 价格: 67123.45, ID: signal_1705123456789_1234
```

#### 6.2 检查钉钉通知

等待下一个信号生成，观察钉钉通知是否包含：

**Pending信号通知**：
- ✅ 信号ID显示
- ✅ 信号价格显示
- ✅ K线序号正确（1-96范围）
- ✅ 信号序号递增

**推荐信号通知**：
- ✅ 相同的信号ID
- ✅ 相同的信号价格
- ✅ 投注建议和因子得分

**结算通知**：
- ✅ 相同的信号ID
- ✅ 价格对比信息
- ✅ 结算结果

#### 6.3 检查数据库

```bash
# 检查信号数据库是否创建
ls -la data/signal_settlement.db

# 查看信号记录
sqlite3 data/signal_settlement.db "SELECT signal_id, timestamp, direction, confidence, signal_price FROM signal_records ORDER BY timestamp DESC LIMIT 5;"
```

## 📊 验证清单

### 功能验证

- [ ] K线序号显示正确（1-96）
- [ ] 信号序号按时间递增
- [ ] 信号ID在所有通知中一致
- [ ] 信号价格准确显示
- [ ] 结算通知能对应到生成信号
- [ ] 每日计数器正确重置

### 性能验证

- [ ] 策略启动正常
- [ ] 内存使用稳定
- [ ] 日志输出正常
- [ ] 钉钉通知正常发送

### 错误处理

- [ ] 数据库连接异常时不影响主流程
- [ ] 信号ID生成失败时有备用处理
- [ ] 网络异常时通知发送重试

## 🔍 故障排除

### 常见问题

#### 1. 信号ID显示为"未分配"

**原因**: 信号结算检查器初始化失败或数据库连接问题

**解决方案**:
```bash
# 检查数据库权限
ls -la data/
mkdir -p data
chmod 755 data

# 重启策略
pkill -f "python.*event_contract_main_strategy"
python scripts/run_event_contract_strategy.py
```

#### 2. K线序号显示错误

**原因**: 系统时间不正确或计算逻辑异常

**解决方案**:
```bash
# 检查系统时间
date

# 手动测试K线序号计算
python -c "
from datetime import datetime
current_time = datetime.now()
slot = (current_time.hour * 4) + (current_time.minute // 15) + 1
print(f'当前时间: {current_time.strftime(\"%H:%M\")}')
print(f'K线序号: {slot}')
print(f'合理范围: 1-96')
"
```

#### 3. 信号序号不递增

**原因**: 每日计数器重置逻辑问题

**解决方案**:
```bash
# 重启策略以重置计数器
pkill -f "python.*event_contract_main_strategy"
python scripts/run_event_contract_strategy.py
```

#### 4. 钉钉通知格式错误

**原因**: 消息构建参数缺失

**解决方案**:
```bash
# 检查日志中的错误信息
tail -f logs/event_contract_main_strategy.log | grep -E "(ERROR|Exception)"

# 测试钉钉通知
python tests/test_signal_tracking_simple.py
```

### 调试命令

```bash
# 详细日志监控
tail -f logs/event_contract_main_strategy.log

# 过滤特定信息
tail -f logs/event_contract_main_strategy.log | grep -E "(信号|K线|序号|ID)"

# 检查进程状态
ps aux | grep python | grep event_contract

# 检查数据库内容
sqlite3 data/signal_settlement.db ".tables"
sqlite3 data/signal_settlement.db "SELECT COUNT(*) FROM signal_records;"
```

## 🎉 验收标准

### 最低要求

1. ✅ 策略能正常启动并运行
2. ✅ K线序号在1-96范围内且递增
3. ✅ 信号序号每日从1开始递增
4. ✅ 信号ID格式正确且唯一
5. ✅ 钉钉通知包含必要信息

### 理想效果

1. ✅ 所有通知中信号ID保持一致
2. ✅ 信号价格信息准确显示
3. ✅ 结算通知能准确对应生成信号
4. ✅ 用户能清晰跟踪信号生命周期
5. ✅ 系统运行稳定无异常

## 📈 后续监控

### 日常监控指标

1. **信号生成频率**: 每15分钟周期检查
2. **通知发送成功率**: 95%以上
3. **信号ID唯一性**: 100%
4. **序号准确性**: 100%
5. **价格信息准确性**: 100%

### 监控脚本

```bash
#!/bin/bash
# 每小时执行的监控脚本
echo "$(date): 开始信号跟踪监控"

# 检查策略运行状态
if pgrep -f "event_contract_main_strategy" > /dev/null; then
    echo "✅ 策略运行正常"
else
    echo "❌ 策略未运行，尝试重启..."
    cd /path/to/mitchquant1
    python scripts/run_event_contract_strategy.py &
fi

# 检查最近的信号记录
if [ -f "data/signal_settlement.db" ]; then
    count=$(sqlite3 data/signal_settlement.db "SELECT COUNT(*) FROM signal_records WHERE timestamp > datetime('now', '-1 hour');")
    echo "📊 最近1小时信号数量: $count"
else
    echo "⚠️  信号数据库不存在"
fi

echo "$(date): 监控完成"
```

## 📝 总结

本次修复完成后，系统将能够：

1. **准确显示K线和信号序号**
2. **完整跟踪信号生命周期**
3. **提供清晰的价格信息**
4. **实现精确的结算对应**

用户体验将得到显著改善，信号跟踪将更加可靠和直观。