# 币安事件合约自动化交易系统部署指南

## 📋 系统概述

本系统是一个完整的币安事件合约自动化交易解决方案，包含信号生成、决策引擎、自动下单、风险管理、钉钉通知等功能。

## 🎯 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    主策略控制器                              │
│               EventContractMainStrategy                     │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│  信号生成器  │ │  决策引擎   │ │  API客户端  │
│ SignalGen   │ │ DecisionEng │ │ BinanceAPI  │
└─────────────┘ └─────────────┘ └─────────────┘
        │             │             │
        └─────────────┼─────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│  钉钉通知   │ │  结算检查   │ │  历史管理   │
│ DingTalk    │ │ Settlement  │ │ TradeHist   │
└─────────────┘ └─────────────┘ └─────────────┘
```

## 🚀 快速部署

### 1. 环境要求

- **Python**: 3.8+
- **操作系统**: Linux/macOS/Windows
- **内存**: 最少2GB，推荐4GB+
- **磁盘空间**: 最少1GB
- **网络**: 稳定的互联网连接

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository_url>
cd mitchquant1

# 安装Python依赖
pip install -r requirements.txt

# 或者使用conda
conda env create -f environment.yml
conda activate mitchquant1
```

### 3. 配置文件设置

创建或编辑 `config.json`：

```json
{
    "LOG": {
        "level": "info",
        "path": "./logs",
        "name": "error.log",
        "console": true,
        "backup_count": 5,
        "clear": false
    },
    "PLATFORMS": {
        "binance": {
            "access_key": "your_binance_api_key",
            "secret_key": "your_binance_secret_key"
        }
    },
    "DINGTALK": "https://oapi.dingtalk.com/robot/send?access_token=your_token",
    "PROXY": null,
    "TRADING": {
        "base_amount": 20,
        "max_daily_loss": 1000,
        "max_daily_loss_hard": 10000,
        "signal_threshold": 0.5
    }
}
```

### 4. 目录结构创建

```bash
# 创建必要目录
mkdir -p data logs docs/reports exports
```

## 🔧 系统配置

### 1. 币安API配置

1. 登录币安账户
2. 进入API管理页面
3. 创建新的API密钥
4. 设置权限（仅需要交易权限）
5. 将密钥添加到配置文件

**安全建议**：
- 使用环境变量存储敏感信息
- 定期更换API密钥
- 限制API访问IP

### 2. 钉钉机器人配置

1. 创建钉钉群组
2. 添加自定义机器人
3. 获取webhook URL
4. 配置安全设置（关键词：交易、小火箭）

### 3. 风险参数配置

```json
{
    "RISK_MANAGEMENT": {
        "soft_limit": 1000,      // 软限制：单日亏损1000 USDT
        "hard_limit": 10000,     // 硬限制：单日亏损10000 USDT
        "base_amount": 20,       // 基础投注金额
        "max_position": 5,       // 最大同时持仓数
        "cooldown_minutes": 30   // 连续亏损后冷却时间
    }
}
```

## 📊 系统启动

### 1. 启动方式

#### 方式1：使用启动脚本（推荐）
```bash
cd scripts
python3 run_event_contract_strategy.py
```

#### 方式2：直接运行主策略
```bash
python3 -m quant.strategies.event_contract_main_strategy
```

#### 方式3：后台运行
```bash
nohup python3 scripts/run_event_contract_strategy.py > strategy.log 2>&1 &
```

### 2. 启动检查清单

- [ ] 配置文件正确设置
- [ ] API密钥有效且权限正确
- [ ] 钉钉机器人配置正常
- [ ] 网络连接稳定
- [ ] 系统资源充足
- [ ] 日志目录可写
- [ ] 数据库文件权限正确

## 🔍 系统监控

### 1. 实时监控

```bash
# 查看实时日志
tail -f logs/error.log

# 监控系统资源
python3 tools/performance_monitor.py
```

### 2. 状态检查

```python
# 获取系统状态
from quant.strategies.event_contract_main_strategy import EventContractMainStrategy
strategy = EventContractMainStrategy()
status = await strategy.get_status()
print(status)
```

### 3. 性能指标

- **信号生成时间**: < 100ms
- **决策执行时间**: < 50ms
- **订单执行时间**: < 500ms
- **通知发送时间**: < 200ms
- **总响应时间**: < 1000ms

## 🛡️ 安全与风险控制

### 1. 资金安全

- 设置合理的单笔投注金额
- 配置日亏损限制
- 定期检查账户余额
- 避免过度杠杆

### 2. 系统安全

- 定期备份交易数据
- 监控异常行为
- 及时更新系统
- 使用安全的网络环境

### 3. 风险提醒

系统会在以下情况发送风险警告：
- 单日亏损达到软限制（1000 USDT）
- 连续亏损次数过多
- 系统异常或API错误
- 网络连接不稳定

## 📈 性能优化

### 1. 参数调优

```bash
# 运行参数优化工具
python3 tools/strategy_optimizer.py
```

### 2. 系统优化

- 使用SSD存储提高数据库性能
- 配置足够的内存避免交换
- 使用稳定的网络连接
- 定期清理日志文件

### 3. 监控优化

- 设置合理的监控间隔
- 优化通知频率
- 使用异步处理提高效率

## 🧪 测试与验证

### 1. 系统集成测试

```bash
# 运行完整测试套件
python3 tests/test_system_integration.py
```

### 2. 组件测试

```bash
# 测试主策略
python3 tests/test_main_strategy_simple.py

# 测试交易历史管理
python3 tests/test_trade_history_manager.py
```

### 3. 模拟交易测试

在实际投入资金前，建议：
- 使用小额资金测试
- 观察系统运行稳定性
- 验证通知功能正常
- 检查风险控制有效性

## 📋 运维管理

### 1. 日常维护

- 检查系统运行状态
- 监控资金变化
- 查看交易统计
- 备份重要数据

### 2. 故障处理

#### 常见问题及解决方案

**问题1：API连接失败**
```bash
# 检查网络连接
ping api.binance.com

# 检查API密钥
python3 -c "from quant.platform.enhanced_binance_event_contracts import *; print('API OK')"
```

**问题2：钉钉通知失败**
```bash
# 测试钉钉连接
curl -X POST "your_webhook_url" -H "Content-Type: application/json" -d '{"msgtype": "text", "text": {"content": "测试消息"}}'
```

**问题3：数据库错误**
```bash
# 检查数据库文件
sqlite3 data/trade_history.db ".tables"
```

### 3. 系统升级

1. 停止当前运行的策略
2. 备份配置文件和数据
3. 更新代码
4. 运行测试验证
5. 重新启动系统

## 📊 报告和分析

### 1. 交易报告

```python
# 生成交易报告
from quant.strategies.event_contract_trade_history_manager import EventContractTradeHistoryManager
manager = EventContractTradeHistoryManager()
report = await manager.generate_performance_report()
print(report)
```

### 2. 性能分析

```bash
# 生成性能报告
python3 tools/performance_monitor.py
```

### 3. 数据导出

```python
# 导出交易数据
await manager.export_to_csv('exports/trades.csv')
await manager.export_to_json('exports/trades.json')
```

## 🔄 系统扩展

### 1. 自定义策略

```python
# 创建自定义信号生成器
class CustomSignalGenerator(EventContractSignalGeneratorSimple):
    def __init__(self, api_client):
        super().__init__(api_client)
        # 自定义初始化
    
    async def generate_signal(self):
        # 自定义信号逻辑
        pass
```

### 2. 添加新功能

- 多币种支持
- 更多技术指标
- 机器学习模型
- 高级风险管理

### 3. 集成其他平台

- 其他交易所API
- 更多通知渠道
- 外部数据源
- 云服务集成

## 📞 技术支持

### 1. 问题反馈

- 查看日志文件定位问题
- 运行系统测试验证功能
- 检查配置文件设置
- 提供详细的错误信息

### 2. 性能监控

- 定期检查系统性能
- 监控资源使用情况
- 优化关键参数
- 及时处理异常

### 3. 文档参考

- [主策略使用指南](main_strategy_guide.md)
- [交易历史管理指南](trade_history_manager_guide.md)
- [API文档](api_documentation.md)

---

## ⚠️ 免责声明

**重要提醒**：
- 本系统仅供学习和研究使用
- 实际交易存在风险，请谨慎评估
- 建议在充分测试后小额试运行
- 任何投资决策请自行承担风险

**风险提示**：
- 市场波动可能导致亏损
- 技术故障可能影响交易
- 网络中断可能错过机会
- 请做好风险管理和资金控制

---

**版本**: v1.0.0  
**更新时间**: 2025-07-12  
**维护团队**: HertelQuant开发组 