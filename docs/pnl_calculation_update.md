# 币安事件合约盈亏计算规则更新

## 📋 更新概述

根据币安事件合约的实际交易规则，我们对盈亏计算逻辑进行了全面修正，确保系统完全符合真实的交易规则。

## 🎯 修正后的盈亏规则

### 1. 胜利（WIN）- 预测正确
- **收益率**: 80%
- **计算公式**: 盈利 = 投注金额 × 0.8
- **示例**: 投注20 USDT，盈利16 USDT

### 2. 亏损（LOSS）- 预测错误
- **收益率**: -100%
- **计算公式**: 亏损 = -投注金额
- **示例**: 投注20 USDT，亏损20 USDT

### 3. 平局（TIE）- 价格相等
- **收益率**: 0%
- **计算公式**: 盈亏 = 0
- **示例**: 投注20 USDT，盈亏0 USDT（回本）

## 🔄 主要修改内容

### 1. 结算检查器（EventContractSettlementChecker）

#### 修改的文件
- `quant/strategies/event_contract_settlement_checker.py`

#### 关键修改
```python
def _determine_result(self, contract: ContractRecord, final_price: float) -> str:
    """判断结果"""
    # 处理价格相等的情况（平局）
    if final_price == contract.predicted_price:
        return "tie"
    
    if contract.direction == "UP":
        return "win" if final_price > contract.predicted_price else "loss"
    elif contract.direction == "DOWN":
        return "win" if final_price < contract.predicted_price else "loss"
    else:
        return "loss"

def _calculate_pnl(self, contract: ContractRecord, result: str) -> float:
    """
    计算盈亏（根据币安事件合约实际规则）
    - 预测正确：获得80%收益
    - 预测错误：损失全部投入
    - 价格相等：回本（盈亏为0）
    """
    if result == "win":
        # 预测正确，获得80%收益
        return contract.bet_amount * 0.8
    elif result == "tie":
        # 价格相等，回本
        return 0.0
    else:
        # 预测错误，损失全部投入
        return -contract.bet_amount
```

### 2. 统计系统更新

#### 新增平局统计
```python
@dataclass
class TradingStatistics:
    """交易统计"""
    total_trades: int = 0
    wins: int = 0
    losses: int = 0
    ties: int = 0  # 新增：平局次数
    # ... 其他字段
```

#### 统计更新逻辑
- 平局不影响连胜连败记录
- 统计显示格式："胜/负/平 X/Y/Z"
- 每日统计包含平局数据

### 3. 钉钉通知更新

#### 修改的文件
- `quant/strategies/event_contract_dingtalk_notifier.py`

#### 结算通知更新
```python
def _build_settlement_message(self, order_id: str, result: str, pnl: float, decision: TradingDecision) -> str:
    """构建结算消息"""
    # 结果图标和文本
    if result == "win":
        result_icon = "🎉"
        result_text = "预测正确·盈利"
        pnl_icon = "💰"
    elif result == "tie":
        result_icon = "🤝"
        result_text = "价格相等·回本"
        pnl_icon = "💳"
    else:
        result_icon = "😔"
        result_text = "预测错误·亏损"
        pnl_icon = "💸"
    
    # 添加盈亏规则说明
    if result == "win":
        message += f"> **盈利说明:** 预测正确，获得80%收益\n\n"
    elif result == "tie":
        message += f"> **回本说明:** 价格相等，回本处理\n\n"
    else:
        message += f"> **亏损说明:** 预测错误，损失全部投入\n\n"
```

#### 每日总结更新
```python
# 交易统计
message += f"> **胜/负/平:** {stats.get('wins', 0)}/{stats.get('losses', 0)}/{stats.get('ties', 0)}\n\n"
```

## 🧪 测试验证

### 1. 盈亏计算测试
- 文件：`tests/test_pnl_calculation.py`
- 测试用例：5个场景（上涨胜利、下跌胜利、上涨亏损、下跌亏损、平局）
- 结果：✅ 所有测试通过

### 2. 钉钉通知测试
- 文件：`tests/test_updated_dingtalk_notification.py`
- 测试内容：胜利、亏损、平局结算通知 + 每日总结
- 结果：✅ 消息格式正确

### 3. 统计功能测试
- 验证平局统计正确记录
- 连胜连败逻辑正确（平局不影响）
- 每日统计包含平局数据

## 📊 盈亏规则对比

| 结果类型 | 旧规则 | 新规则 | 变化 |
|----------|--------|--------|------|
| 预测正确 | +40% | +80% | 收益翻倍 |
| 预测错误 | -100% | -100% | 无变化 |
| 价格相等 | 不处理 | 0%（回本） | 新增处理 |

## 🎯 业务影响分析

### 1. 盈利能力提升
- 胜利收益从40%提升到80%
- 更符合币安实际交易规则
- 提高系统盈利预期

### 2. 风险管理优化
- 平局情况下资金回本，降低风险
- 统计更加准确，便于策略调整
- 通知消息更加清晰易懂

### 3. 用户体验改善
- 钉钉通知包含详细的盈亏说明
- 每日总结显示完整的胜/负/平统计
- 更符合用户对交易结果的理解

## ✅ 完成状态

- [x] 修正盈亏计算逻辑
- [x] 添加平局处理机制
- [x] 更新统计系统
- [x] 修正钉钉通知消息
- [x] 创建测试用例
- [x] 验证功能正确性
- [x] 更新文档说明

## 🔮 后续优化建议

1. **数据迁移**: 如果有历史交易数据，考虑是否需要重新计算
2. **监控指标**: 添加平局率监控，分析价格预测准确性
3. **策略调整**: 根据新的盈亏规则调整交易策略参数
4. **回测验证**: 使用历史数据验证新规则的表现

---

**更新时间**: 2025-07-12  
**版本**: v1.1.0  
**状态**: ✅ 完成 