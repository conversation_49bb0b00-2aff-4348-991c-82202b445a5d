#!/usr/bin/env python3
"""
币安事件合约自动化交易策略启动脚本
"""

import sys
import os
import asyncio
import signal
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_main_strategy import EventContractMainStrategy
from quant.utils import logger


class StrategyRunner:
    """策略运行器"""
    
    def __init__(self):
        self.strategy = None
        self.is_shutdown = False
    
    def signal_handler(self, signum, frame):
        """处理系统信号"""
        logger.info(f"收到信号 {signum}，准备停止策略...")
        self.is_shutdown = True
        if self.strategy:
            asyncio.create_task(self.strategy.stop())
    
    async def run(self):
        """运行策略"""
        try:
            # 注册信号处理器
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)
            
            # 创建策略实例
            logger.info("正在初始化事件合约自动化交易策略...")
            self.strategy = EventContractMainStrategy()
            
            # 启动策略
            logger.info("策略启动中...")
            await self.strategy.start()
            
        except KeyboardInterrupt:
            logger.info("用户中断，正在停止策略...")
            # 🆕 在停止前打印今日信号汇总
            if self.strategy:
                print("\n" + "="*60)
                print("📊 策略停止前的今日信号汇总")
                print("="*60)
                self.strategy.print_daily_signal_summary()
        except Exception as e:
            logger.error(f"策略运行异常: {e}")
            import traceback
            traceback.print_exc()
        finally:
            if self.strategy:
                await self.strategy.stop()
            logger.info("策略已停止")


def main():
    """主函数"""
    print("🚀 币安事件合约自动化交易策略")
    print("=" * 50)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("按 Ctrl+C 停止策略")
    print("=" * 50)
    
    # 创建运行器
    runner = StrategyRunner()
    
    # 运行策略
    asyncio.run(runner.run())


if __name__ == "__main__":
    main() 