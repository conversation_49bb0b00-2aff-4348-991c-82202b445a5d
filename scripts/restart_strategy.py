#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重启交易策略脚本
"""

import os
import sys
import signal
import subprocess
import time

def kill_existing_processes():
    """杀死现有的策略进程"""
    print("🔍 查找现有的策略进程...")
    
    # 查找包含策略相关关键词的进程
    keywords = [
        "event_contract_main_strategy", 
        "run_event_contract_strategy",
        "EventContractMainStrategy"
    ]
    
    for keyword in keywords:
        try:
            result = subprocess.run(
                ["pgrep", "-f", keyword], 
                capture_output=True, 
                text=True
            )
            
            if result.returncode == 0:
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid:
                        print(f"🔪 杀死进程 {pid} ({keyword})")
                        os.kill(int(pid), signal.SIGTERM)
                        time.sleep(1)
                        
                        # 如果进程还在运行，强制杀死
                        try:
                            os.kill(int(pid), 0)  # 检查进程是否存在
                            print(f"🔪 强制杀死进程 {pid}")
                            os.kill(int(pid), signal.SIGKILL)
                        except ProcessLookupError:
                            pass  # 进程已经被杀死
                            
        except Exception as e:
            print(f"❌ 处理进程时出错: {e}")

def start_strategy():
    """启动策略"""
    print("🚀 启动新的策略进程...")
    
    script_path = os.path.join(os.path.dirname(__file__), "run_event_contract_strategy.py")
    
    try:
        # 启动策略
        process = subprocess.Popen(
            [sys.executable, script_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ 策略进程已启动，PID: {process.pid}")
        
        # 显示前几行输出
        print("📋 策略输出:")
        for i in range(10):  # 显示前10行
            line = process.stdout.readline()
            if line:
                print(f"   {line.strip()}")
            else:
                break
                
        return process
        
    except Exception as e:
        print(f"❌ 启动策略失败: {e}")
        return None

def main():
    """主函数"""
    print("🔄 重启交易策略...")
    
    # 1. 杀死现有进程
    kill_existing_processes()
    
    # 2. 等待一下确保进程完全停止
    print("⏳ 等待进程完全停止...")
    time.sleep(3)
    
    # 3. 启动新策略
    process = start_strategy()
    
    if process:
        print("✅ 策略重启成功！")
        print(f"💡 策略进程 PID: {process.pid}")
        print("💡 使用 'ps aux | grep python' 查看进程状态")
        print("💡 使用 'kill -TERM {pid}' 停止策略")
    else:
        print("❌ 策略重启失败！")

if __name__ == "__main__":
    main() 