#!/usr/bin/env python3
"""
查询今日钉钉信号发送历史脚本
按15分钟K线顺序排序显示
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_main_strategy import EventContractMainStrategy
from quant.utils import logger


async def query_daily_signals():
    """查询今日信号发送历史"""
    try:
        print("🔍 正在查询今日钉钉信号发送历史...")
        
        # 创建策略实例（仅用于查询，不启动）
        strategy = EventContractMainStrategy()
        
        # 获取今日信号历史
        history = strategy.get_daily_signal_history()
        
        # 显示详细信息
        print(f"\n{'='*80}")
        print(f"📊 今日钉钉信号发送历史 - {history['date']}")
        print(f"{'='*80}")
        
        if history['total_signals'] == 0:
            print("📭 今日暂无信号发送到钉钉")
            print("💡 提示：请确保策略正在运行并生成信号")
            return
        
        # 基本统计
        print(f"📈 信号发送统计:")
        print(f"   📡 总信号数: {history['total_signals']}")
        print(f"   🚀 看涨信号: {history['summary']['up_signals']}")
        print(f"   📉 看跌信号: {history['summary']['down_signals']}")
        
        # K线进度
        current_kline = strategy.daily_kline_tracker['kline_15m_count']
        print(f"   📊 当前K线: 第{current_kline}/96根 (15分钟)")
        
        # 详细信号列表
        print(f"\n📋 信号详情（按15分钟K线顺序排序）:")
        print(f"{'序号':<4} {'K线序号':<10} {'K线时间':<10} {'方向':<8} {'钉钉发送时间':<12} {'置信度':<8}")
        print("-" * 80)
        
        for i, signal in enumerate(history['signals'], 1):
            direction_symbol = "🚀 UP" if "UP" in signal['direction'] else "📉 DOWN"
            kline_sequence = signal['kline_sequence']
            kline_time = signal['kline_time']
            send_time = signal['timestamp']
            
            # 尝试从信号内容中提取置信度
            confidence = "未知"
            if "信心度:" in signal['direction']:
                try:
                    confidence_part = signal['direction'].split("信心度:")[1].split("%")[0]
                    confidence = confidence_part.strip() + "%"
                except:
                    pass
            
            print(f"{i:<4} "
                  f"第{kline_sequence:<3}根     "
                  f"{kline_time:<10} "
                  f"{direction_symbol:<8} "
                  f"{send_time:<12} "
                  f"{confidence:<8}")
        
        # K线时间分布分析
        print(f"\n📊 K线时间分布分析:")
        kline_distribution = {}
        for signal in history['signals']:
            hour = int(signal['kline_time'].split(':')[0])
            if hour not in kline_distribution:
                kline_distribution[hour] = {'up': 0, 'down': 0}
            
            if "UP" in signal['direction']:
                kline_distribution[hour]['up'] += 1
            else:
                kline_distribution[hour]['down'] += 1
        
        for hour in sorted(kline_distribution.keys()):
            up_count = kline_distribution[hour]['up']
            down_count = kline_distribution[hour]['down']
            total_count = up_count + down_count
            print(f"   {hour:02d}:00-{hour:02d}:59 -> 🚀{up_count} 📉{down_count} (共{total_count}个)")
        
        print(f"\n{'='*80}")
        print(f"✅ 查询完成 - 共找到 {history['total_signals']} 个信号")
        print(f"⏰ 查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*80}")
        
    except Exception as e:
        logger.error(f"查询今日信号历史异常: {e}")
        print(f"❌ 查询失败: {e}")


def main():
    """主函数"""
    print("🔍 币安事件合约 - 今日钉钉信号查询工具")
    print("=" * 60)
    print(f"查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 运行查询
    asyncio.run(query_daily_signals())


if __name__ == "__main__":
    main() 