#!/usr/bin/env python3
"""
胜率统计报表系统集成验证脚本
验证胜率报表功能是否正确集成到主策略中

Author: AI Assistant
Date: 2025-07-18
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def verify_integration():
    """验证胜率报表系统集成状态"""
    
    print("🔍 胜率统计报表系统集成验证")
    print("=" * 60)
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    verification_results = []
    
    # 1. 验证启动脚本
    print("1️⃣ 验证启动脚本...")
    try:
        script_path = "./scripts/run_event_contract_strategy.py"
        if os.path.exists(script_path):
            with open(script_path, 'r') as f:
                content = f.read()
                if "EventContractMainStrategy" in content:
                    print("   ✅ 启动脚本正确导入主策略")
                    verification_results.append(("启动脚本", True))
                else:
                    print("   ❌ 启动脚本未导入主策略")
                    verification_results.append(("启动脚本", False))
        else:
            print("   ❌ 启动脚本不存在")
            verification_results.append(("启动脚本", False))
    except Exception as e:
        print(f"   ❌ 启动脚本验证失败: {e}")
        verification_results.append(("启动脚本", False))
    
    # 2. 验证胜率报表系统导入
    print("\n2️⃣ 验证胜率报表系统...")
    try:
        from quant.strategies.win_rate_report_system import WinRateReportSystem
        print("   ✅ 胜率报表系统导入成功")
        verification_results.append(("胜率报表系统", True))
    except Exception as e:
        print(f"   ❌ 胜率报表系统导入失败: {e}")
        verification_results.append(("胜率报表系统", False))
    
    # 3. 验证钉钉通知器扩展
    print("\n3️⃣ 验证钉钉通知器扩展...")
    try:
        from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier
        notifier = EventContractDingtalkNotifier()
        
        if hasattr(notifier, 'send_win_rate_report'):
            print("   ✅ 钉钉通知器已扩展胜率报表功能")
            verification_results.append(("钉钉通知器扩展", True))
        else:
            print("   ❌ 钉钉通知器未扩展胜率报表功能")
            verification_results.append(("钉钉通知器扩展", False))
            
        if hasattr(notifier, '_build_win_rate_report_message'):
            print("   ✅ 钉钉通知器已添加报表消息构建方法")
        else:
            print("   ❌ 钉钉通知器未添加报表消息构建方法")
            
        if hasattr(notifier, '_check_win_rate_report_limit'):
            print("   ✅ 钉钉通知器已添加报表频率检查方法")
        else:
            print("   ❌ 钉钉通知器未添加报表频率检查方法")
            
    except Exception as e:
        print(f"   ❌ 钉钉通知器验证失败: {e}")
        verification_results.append(("钉钉通知器扩展", False))
    
    # 4. 验证主策略集成
    print("\n4️⃣ 验证主策略集成...")
    try:
        from quant.strategies.event_contract_main_strategy import EventContractMainStrategy
        strategy = EventContractMainStrategy()
        
        # 检查胜率报表系统属性
        if hasattr(strategy, 'win_rate_report_system'):
            print("   ✅ 主策略已集成胜率报表系统")
            verification_results.append(("主策略集成", True))
        else:
            print("   ❌ 主策略未集成胜率报表系统")
            verification_results.append(("主策略集成", False))
        
        # 检查定时任务方法
        if hasattr(strategy, '_check_win_rate_reports'):
            print("   ✅ 主策略已添加胜率报表定时任务方法")
        else:
            print("   ❌ 主策略未添加胜率报表定时任务方法")
        
        # 检查状态变量
        if hasattr(strategy, 'last_hourly_report_time') and hasattr(strategy, 'last_daily_calculation_time'):
            print("   ✅ 主策略已添加胜率报表状态变量")
        else:
            print("   ❌ 主策略未添加胜率报表状态变量")
            
    except Exception as e:
        print(f"   ❌ 主策略集成验证失败: {e}")
        verification_results.append(("主策略集成", False))
    
    # 5. 验证主循环集成
    print("\n5️⃣ 验证主循环集成...")
    try:
        main_strategy_path = "./quant/strategies/event_contract_main_strategy.py"
        if os.path.exists(main_strategy_path):
            with open(main_strategy_path, 'r') as f:
                content = f.read()
                if "_check_win_rate_reports()" in content:
                    print("   ✅ 主循环已调用胜率报表检查方法")
                    verification_results.append(("主循环集成", True))
                else:
                    print("   ❌ 主循环未调用胜率报表检查方法")
                    verification_results.append(("主循环集成", False))
        else:
            print("   ❌ 主策略文件不存在")
            verification_results.append(("主循环集成", False))
    except Exception as e:
        print(f"   ❌ 主循环集成验证失败: {e}")
        verification_results.append(("主循环集成", False))
    
    # 6. 验证文件结构
    print("\n6️⃣ 验证文件结构...")
    required_files = [
        "./quant/strategies/win_rate_report_system.py",
        "./tests/test_win_rate_report_system.py", 
        "./tests/demo_win_rate_report_system.py",
        "./docs/胜率报表系统使用说明.md"
    ]
    
    all_files_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} 不存在")
            all_files_exist = False
    
    verification_results.append(("文件结构", all_files_exist))
    
    # 7. 验证导出目录
    print("\n7️⃣ 验证导出目录...")
    try:
        os.makedirs("./exports", exist_ok=True)
        print("   ✅ 导出目录已创建")
        verification_results.append(("导出目录", True))
    except Exception as e:
        print(f"   ❌ 导出目录创建失败: {e}")
        verification_results.append(("导出目录", False))
    
    # 总结验证结果
    print("\n" + "=" * 60)
    print("📋 验证结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in verification_results if result)
    total = len(verification_results)
    
    for name, result in verification_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("\n🎉 所有验证通过！胜率统计报表系统已完全集成")
        print("\n📋 功能确认:")
        print("   ✅ 1. 胜率统计报表系统会随主策略一起启动")
        print("   ✅ 2. 每日0点的数据重新计算和每日总结发送功能正常")
        print("   ✅ 3. 每小时整点的胜率报表自动发送功能已启用")
        print("   ✅ 4. 钉钉通知功能会正常发送胜率报表消息")
        print("\n🚀 启动命令:")
        print("   python3 /Users/<USER>/PycharmProjects/mitchquant1/scripts/run_event_contract_strategy.py")
        print("\n📊 验证命令:")
        print("   python3 tests/test_win_rate_report_system.py")
        print("   python3 tests/demo_win_rate_report_system.py")
    else:
        print(f"\n⚠️ 有 {total - passed} 项验证失败，请检查相关配置")
    
    return passed == total


if __name__ == "__main__":
    success = verify_integration()
    sys.exit(0 if success else 1)
