# 建议命令

## 基本命令
- `python main.py` - 启动策略
- `python -m pytest tests/` - 运行测试
- `python tools/quality_monitor.py` - 运行质量监控
- `python tools/doc_generator.py` - 生成文档

## 开发命令
- `pip install -r requirements.txt` - 安装依赖
- `python setup.py install` - 安装框架
- `btpython setup.py install` - 宝塔面板安装

## 服务器部署
- `./run.sh` - 安装talib
- `btpython main.py` - 宝塔面板运行
- `systemctl start mongodb` - 启动MongoDB

## 工具命令
- `ls -la` - 查看文件列表
- `grep -r "pattern" .` - 搜索代码
- `find . -name "*.py"` - 查找Python文件
- `cd /www/server/` - 进入服务器目录