#!/usr/bin/env python3
"""
测试网络连接修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import json
from datetime import datetime
from quant.strategies.event_contract_main_strategy import EventContractMainStrategy
from quant.utils.network_health_checker import network_health_checker
from quant.utils import logger

async def test_network_fixes():
    """测试网络连接修复效果"""
    print("🔧 测试网络连接修复效果...")
    
    try:
        # 1. 测试网络健康检查器
        print("\n=== 测试网络健康检查器 ===")
        health_result = await network_health_checker.check_network_health()
        print(f"网络状态: {health_result['overall_status']}")
        print(f"成功端点: {health_result['summary']['successful']}")
        print(f"失败端点: {health_result['summary']['failed']}")
        
        # 2. 测试主策略的网络方法
        print("\n=== 测试主策略网络方法 ===")
        strategy = EventContractMainStrategy("scripts/config.json")
        
        # 测试获取BTC价格（带重试机制）
        print("测试获取BTC价格...")
        btc_price = await strategy._get_current_btc_price()
        if btc_price:
            print(f"✅ 成功获取BTC价格: {btc_price:.2f} USDT")
        else:
            print("❌ 获取BTC价格失败")
        
        # 测试获取最新数据（带重试机制）
        print("测试获取最新K线数据...")
        await strategy._fetch_latest_data()
        print("✅ K线数据获取测试完成")
        
        # 3. 测试网络健康检查集成
        print("\n=== 测试网络健康检查集成 ===")
        await strategy._check_network_health()
        print(f"策略网络状态: {strategy.network_status}")
        
        print("\n🎉 所有网络修复测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

async def test_continuous_monitoring():
    """测试持续监控（短时间）"""
    print("\n🔄 测试持续网络监控（30秒）...")
    
    try:
        # 创建监控任务
        monitoring_task = asyncio.create_task(
            network_health_checker.continuous_monitoring(interval_seconds=10)
        )
        
        # 运行30秒后停止
        await asyncio.sleep(30)
        monitoring_task.cancel()
        
        # 显示监控结果
        summary = network_health_checker.get_health_summary()
        print(f"\n📊 监控结果:")
        print(f"总检查次数: {summary['total_checks']}")
        print(f"总失败次数: {summary['total_failures']}")
        print(f"成功率: {summary['overall_success_rate']}%")
        
        print("✅ 持续监控测试完成")
        
    except asyncio.CancelledError:
        print("✅ 监控任务已取消")
    except Exception as e:
        print(f"❌ 持续监控测试异常: {e}")

def show_network_report():
    """显示网络健康报告"""
    print("\n" + "="*60)
    print("📊 网络健康报告")
    print("="*60)
    
    try:
        report = network_health_checker.format_health_report()
        print(report)
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")
    
    print("="*60)

async def main():
    """主函数"""
    print("🚀 开始网络连接修复测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 基础网络修复测试
    await test_network_fixes()
    
    # 显示初始报告
    show_network_report()
    
    # 持续监控测试
    await test_continuous_monitoring()
    
    # 显示最终报告
    show_network_report()
    
    print("\n✅ 所有测试完成！")
    print("\n💡 修复说明:")
    print("1. 增加了更强的重试机制（5次重试，递增延迟）")
    print("2. 添加了详细的错误日志记录")
    print("3. 集成了网络健康检查器")
    print("4. 优化了错误通知频率控制")
    print("5. 增加了连续失败次数跟踪")

if __name__ == "__main__":
    asyncio.run(main())
