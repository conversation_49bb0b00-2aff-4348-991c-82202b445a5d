#!/usr/bin/env python3
"""
简单的信号结算系统演示
展示10分钟信号结算的基本流程
"""

import time
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.signal_settlement_checker import SignalSettlementChecker
from quant.utils import logger


def main():
    """主演示函数"""
    logger.info("🎯 开始10分钟信号结算系统演示")
    logger.info("=" * 60)
    
    # 创建独立的演示数据库
    demo_db_path = f"./data/demo_settlement_{int(time.time())}.db"
    settlement_checker = SignalSettlementChecker(demo_db_path)
    
    try:
        # 1. 生成示例信号
        logger.info("\\n📈 步骤1: 生成示例信号")
        
        signal_data = {
            'direction': 'UP',
            'confidence': 75.0,
            'signal_price': 43500.0,
            'signal_strength': 'STRONG',
            'supporting_indicators': ['RSI', 'MACD'],
            'market_conditions': 'BULLISH'
        }
        
        signal_id = settlement_checker.add_signal_record(signal_data)
        logger.info(f"✅ 添加信号: {signal_id}")
        logger.info(f"   方向: {signal_data['direction']}")
        logger.info(f"   价格: {signal_data['signal_price']:.2f} USDT")
        logger.info(f"   置信度: {signal_data['confidence']:.1f}%")
        
        # 2. 显示待结算信号
        logger.info("\\n📋 步骤2: 检查待结算信号")
        pending_count = settlement_checker.get_pending_signals_count()
        logger.info(f"📊 待结算信号数量: {pending_count}")
        
        # 3. 验证10分钟到期时间
        logger.info("\\n⏰ 步骤3: 验证10分钟到期时间")
        import sqlite3
        conn = sqlite3.connect(demo_db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT timestamp, expiry_time 
            FROM signal_records 
            WHERE signal_id = ?
        ''', (signal_id,))
        
        record = cursor.fetchone()
        if record:
            signal_time = datetime.fromisoformat(record[0])
            expiry_time = datetime.fromisoformat(record[1])
            time_diff = expiry_time - signal_time
            
            logger.info(f"📅 信号时间: {signal_time}")
            logger.info(f"⏰ 到期时间: {expiry_time}")
            logger.info(f"🕐 时间差: {time_diff}")
            
            if time_diff.total_seconds() / 60 == 10:
                logger.info("✅ 确认: 信号正确设置为10分钟后到期")
            else:
                logger.warning(f"⚠️ 警告: 到期时间不是10分钟，实际为{time_diff}")
        
        conn.close()
        
        # 4. 模拟信号结算
        logger.info("\\n🎯 步骤4: 模拟信号结算")
        
        # 手动设置信号为过期状态
        conn = sqlite3.connect(demo_db_path)
        cursor = conn.cursor()
        
        past_expiry = (datetime.now() - timedelta(minutes=1)).isoformat()
        cursor.execute('''
            UPDATE signal_records 
            SET expiry_time = ? 
            WHERE signal_id = ?
        ''', (past_expiry, signal_id))
        
        conn.commit()
        conn.close()
        
        logger.info("⏰ 信号已设置为过期状态")
        
        # 执行结算
        current_price = 43550.0  # 模拟当前价格
        logger.info(f"💰 当前价格: {current_price:.2f} USDT")
        
        settled_signals = settlement_checker.check_and_settle_signals(current_price)
        
        if settled_signals:
            signal = settled_signals[0]
            result_emoji = "✅" if signal['result'] == 'WIN' else "❌" if signal['result'] == 'LOSS' else "⚖️"
            direction_emoji = "🚀" if signal['direction'] == 'UP' else "📉"
            
            logger.info(f"🎯 信号结算完成:")
            logger.info(f"   {result_emoji} 信号ID: {signal['signal_id']}")
            logger.info(f"   方向: {signal['direction']} {direction_emoji}")
            logger.info(f"   信号价格: {signal['signal_price']:.2f} USDT")
            logger.info(f"   结算价格: {signal['settlement_price']:.2f} USDT")
            logger.info(f"   价格变化: {signal['price_change']:.2f}%")
            logger.info(f"   结果: {signal['result']}")
            logger.info(f"   置信度: {signal['confidence']:.1f}%")
        else:
            logger.error("❌ 未找到结算信号")
        
        # 5. 显示统计信息
        logger.info("\\n📊 步骤5: 显示统计信息")
        stats = settlement_checker.get_settlement_stats(days=1)
        
        logger.info("结算统计:")
        logger.info(f"   总结算数: {stats['total_settled']}")
        logger.info(f"   胜利数: {stats['total_wins']}")
        logger.info(f"   失败数: {stats['total_losses']}")
        logger.info(f"   平局数: {stats['total_ties']}")
        logger.info(f"   整体胜率: {stats['overall_win_rate']:.1f}%")
        logger.info(f"   待结算数: {stats['pending_signals']}")
        
        logger.info("\\n🎉 演示完成！")
        logger.info("=" * 60)
        
        # 关键要点总结
        logger.info("\\n💡 关键要点:")
        logger.info("✅ 1. 信号生成时自动添加到结算跟踪器")
        logger.info("✅ 2. 信号到期时间正确设置为10分钟")
        logger.info("✅ 3. 10分钟后基于实时价格自动结算")
        logger.info("✅ 4. 结算结果用于胜率统计和策略优化")
        logger.info("✅ 5. 系统支持实时监控和报告")
        
    except Exception as e:
        logger.error(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理演示数据
        logger.info("\\n🧹 清理演示数据...")
        try:
            if os.path.exists(demo_db_path):
                os.remove(demo_db_path)
                logger.info("✅ 演示数据库已删除")
        except Exception as e:
            logger.error(f"❌ 清理失败: {e}")


if __name__ == "__main__":
    main()