#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
事件合约信号生成器使用示例

演示如何使用信号生成器分析K线数据并生成交易信号

Author: HertelQuant Enhanced
Date: 2025-01-31
"""

import time
import random
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_signal_generator import (
    EventContractSignalGenerator, 
    KlineData,
    TechnicalIndicators,
    SignalResult
)


class SignalGeneratorDemo:
    """信号生成器演示类"""
    
    def __init__(self):
        """初始化演示"""
        # 创建信号生成器
        self.signal_generator = EventContractSignalGenerator(
            signal_threshold=90.0,    # 90%概率阈值
            min_timeframe_consensus=2,  # 至少2个时间周期确认
            confidence_threshold=80.0   # 80%置信度阈值
        )
        
        # 模拟价格数据
        self.base_price = 95000.0
        self.current_timestamp = int((datetime.now() - timedelta(days=1)).timestamp() * 1000)
        
    def generate_realistic_kline_data(self, count: int = 200) -> None:
        """
        生成真实的K线数据
        
        Args:
            count: 生成的K线数量
        """
        print(f"\n📊 生成 {count} 根模拟1分钟K线数据...")
        
        current_price = self.base_price
        
        for i in range(count):
            # 生成时间戳（每分钟递增）
            timestamp = self.current_timestamp + i * 60 * 1000
            
            # 模拟价格波动（-0.1% 到 +0.1%）
            price_change = (random.random() - 0.5) * 0.002
            new_price = current_price * (1 + price_change)
            
            # 生成OHLC数据
            open_price = current_price
            close_price = new_price
            
            # 生成高低价（确保逻辑正确）
            price_range = abs(close_price - open_price) * (1 + random.random())
            high_price = max(open_price, close_price) + price_range * random.random() * 0.5
            low_price = min(open_price, close_price) - price_range * random.random() * 0.5
            
            # 生成成交量
            volume = 100 + random.random() * 200
            
            # 添加到信号生成器
            self.signal_generator.add_kline_data(
                timestamp=timestamp,
                open_price=open_price,
                high_price=high_price,
                low_price=low_price,
                close_price=close_price,
                volume=volume
            )
            
            # 更新当前价格
            current_price = new_price
            
            # 每50根K线显示进度
            if (i + 1) % 50 == 0:
                print(f"   ✅ 已生成 {i + 1}/{count} 根K线")
        
        print(f"✅ K线数据生成完成！最终价格: ${current_price:,.2f}")
        self.current_price = current_price
    
    def demonstrate_kline_logic(self):
        """演示K线阳线阴线判断逻辑"""
        print("\n" + "="*60)
        print("📈 K线阳线阴线判断逻辑演示")
        print("="*60)
        
        # 创建测试K线数据
        test_cases = [
            # (开盘价, 收盘价, 预期结果)
            (100.0, 105.0, "阳线"),  # 收盘价 > 开盘价
            (100.0, 95.0, "阴线"),   # 收盘价 < 开盘价
            (100.0, 100.0, "十字线"), # 收盘价 = 开盘价
            (98.5, 101.2, "阳线"),   # 收盘价 > 开盘价
            (101.8, 99.3, "阴线"),   # 收盘价 < 开盘价
        ]
        
        print("\n测试K线判断逻辑:")
        print("开盘价    收盘价    实际结果    预期结果    ✓/✗")
        print("-" * 55)
        
        for open_price, close_price, expected in test_cases:
            # 创建测试K线
            kline = KlineData(
                timestamp=int(time.time() * 1000),
                open=open_price,
                high=max(open_price, close_price) + 1,
                low=min(open_price, close_price) - 1,
                close=close_price,
                volume=100
            )
            
            # 判断结果
            if kline.is_bullish:
                actual = "阳线"
            elif kline.is_bearish:
                actual = "阴线"
            else:
                actual = "十字线"
            
            # 检查是否正确
            is_correct = "✅" if actual == expected else "❌"
            
            print(f"{open_price:8.1f}  {close_price:8.1f}  {actual:8s}  {expected:8s}  {is_correct}")
        
        print("\n📋 K线判断规则:")
        print("   🟢 阳线：收盘价 > 开盘价 (close > open)")
        print("   🔴 阴线：收盘价 < 开盘价 (close < open)")
        print("   ⚪ 十字线：收盘价 = 开盘价 (close = open)")
    
    def demonstrate_signal_generation(self):
        """演示信号生成过程"""
        print("\n" + "="*60)
        print("🎯 信号生成演示")
        print("="*60)
        
        # 检查数据状态
        status = self.signal_generator.get_status()
        print("\n📊 当前数据状态:")
        for timeframe, info in status.items():
            kline_count = info['total_klines']
            latest = info['latest_kline']
            
            if latest and kline_count > 0:
                kline_type = "阳线" if latest['is_bullish'] else "阴线" if latest['is_bearish'] else "十字线"
                print(f"   {timeframe:4s}: {kline_count:3d} 根K线, 最新: {kline_type} "
                      f"O={latest['open']:.1f} H={latest['high']:.1f} L={latest['low']:.1f} C={latest['close']:.1f}")
            else:
                print(f"   {timeframe:4s}: {kline_count:3d} 根K线 (数据不足)")
        
        # 生成信号
        print(f"\n🔄 开始信号生成...")
        signal_result = self.signal_generator.generate_signal()
        
        # 显示信号结果
        self.display_signal_result(signal_result)
        
        return signal_result
    
    def display_signal_result(self, signal: SignalResult):
        """
        显示信号结果
        
        Args:
            signal: 信号结果
        """
        print(f"\n🎯 信号生成结果:")
        print("-" * 50)
        
        if signal.has_signal:
            direction_emoji = "🚀" if signal.direction == "UP" else "📉" if signal.direction == "DOWN" else "➡️"
            print(f"   📶 信号状态: ✅ 有效信号")
            print(f"   🎯 信号方向: {direction_emoji} {signal.direction}")
            print(f"   📊 置信度: {signal.confidence:.1f}%")
            print(f"   📈 看涨概率: {signal.bullish_probability:.1f}%")
            print(f"   📉 看跌概率: {signal.bearish_probability:.1f}%")
            print(f"   🔧 技术评分: {signal.technical_score:.1f}/100")
            print(f"   ⚠️ 风险等级: {signal.risk_level}")
            print(f"   ⏰ 支持周期: {', '.join(signal.supporting_timeframes)}")
        else:
            print(f"   📶 信号状态: ❌ 无有效信号")
            print(f"   📈 看涨概率: {signal.bullish_probability:.1f}%")
            print(f"   📉 看跌概率: {signal.bearish_probability:.1f}%")
            print(f"   💡 建议: 等待更明确的市场方向")
        
        print(f"   🕐 生成时间: {signal.generated_at}")
        
        # 显示各时间周期详细分析
        if signal.timeframe_analysis:
            print(f"\n📊 各时间周期分析:")
            print("-" * 50)
            
            for timeframe, analysis in signal.timeframe_analysis.items():
                direction_emoji = "🚀" if analysis['dominant_direction'] == "UP" else "📉"
                print(f"   {timeframe:4s}: {direction_emoji} {analysis['dominant_direction']:4s} "
                      f"强度={analysis['signal_strength']:5.1f}% "
                      f"置信度={analysis['confidence']:5.1f}% "
                      f"看涨={analysis['bullish_probability']:5.1f}% "
                      f"看跌={analysis['bearish_probability']:5.1f}%")
    
    def demonstrate_continuous_monitoring(self, minutes: int = 5):
        """
        演示连续监控模式
        
        Args:
            minutes: 监控分钟数
        """
        print(f"\n" + "="*60)
        print(f"👁️ 连续监控模式演示 ({minutes} 分钟)")
        print("="*60)
        
        print(f"\n🔄 开始连续监控，每30秒检查一次信号...")
        
        start_time = time.time()
        check_count = 0
        signal_count = 0
        
        while time.time() - start_time < minutes * 60:
            check_count += 1
            
            # 模拟新的K线数据
            self._add_simulated_kline()
            
            # 生成信号
            signal = self.signal_generator.generate_signal()
            
            # 简化显示
            current_time = datetime.now().strftime("%H:%M:%S")
            
            if signal.has_signal:
                signal_count += 1
                direction_emoji = "🚀" if signal.direction == "UP" else "📉"
                print(f"   {current_time} - {direction_emoji} {signal.direction} 信号! "
                      f"置信度={signal.confidence:.1f}% 看涨={signal.bullish_probability:.1f}% "
                      f"看跌={signal.bearish_probability:.1f}%")
            else:
                print(f"   {current_time} - ⏳ 无信号 "
                      f"(看涨={signal.bullish_probability:.1f}% 看跌={signal.bearish_probability:.1f}%)")
            
            # 等待30秒
            time.sleep(30)
        
        print(f"\n📊 监控总结:")
        print(f"   总检查次数: {check_count}")
        print(f"   生成信号次数: {signal_count}")
        print(f"   信号生成率: {signal_count/check_count*100:.1f}%")
    
    def _add_simulated_kline(self):
        """添加模拟的新K线数据"""
        # 生成新的时间戳
        self.current_timestamp += 60 * 1000
        
        # 模拟价格波动
        price_change = (random.random() - 0.5) * 0.003  # ±0.15%
        new_price = self.current_price * (1 + price_change)
        
        # 生成K线数据
        open_price = self.current_price
        close_price = new_price
        
        price_range = abs(close_price - open_price) * (1 + random.random())
        high_price = max(open_price, close_price) + price_range * random.random() * 0.3
        low_price = min(open_price, close_price) - price_range * random.random() * 0.3
        
        volume = 80 + random.random() * 140
        
        # 添加到信号生成器
        self.signal_generator.add_kline_data(
            timestamp=self.current_timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume
        )
        
        # 更新当前价格
        self.current_price = new_price
    
    def run_complete_demo(self):
        """运行完整演示"""
        print("🚀 事件合约信号生成器完整演示")
        print("="*80)
        
        try:
            # 1. K线逻辑演示
            self.demonstrate_kline_logic()
            
            # 2. 生成历史数据
            self.generate_realistic_kline_data(200)
            
            # 3. 信号生成演示
            signal = self.demonstrate_signal_generation()
            
            # 4. 如果有时间，进行连续监控
            print(f"\n🤔 是否进行连续监控演示？")
            print(f"   当前信号状态: {'有信号' if signal.has_signal else '无信号'}")
            
            # 进行短期监控演示（2分钟）
            self.demonstrate_continuous_monitoring(2)
            
        except KeyboardInterrupt:
            print(f"\n⚠️ 用户中断演示")
        except Exception as e:
            print(f"\n❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"\n✅ 演示完成!")
        print("="*80)


def main():
    """主函数"""
    try:
        demo = SignalGeneratorDemo()
        demo.run_complete_demo()
    except Exception as e:
        print(f"❌ 演示启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()