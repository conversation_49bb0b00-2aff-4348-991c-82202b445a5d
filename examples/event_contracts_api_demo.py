#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
币安事件合约增强版API使用示例

Author: HertelQuant Enhanced
Date: 2025-01-31
"""

import time
import asyncio
from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.platform.enhanced_binance_event_contracts import EnhancedBinanceEventContractsAPI
from quant.utils.logger import logger


class EventContractsDemo:
    """事件合约API演示类"""
    
    def __init__(self):
        """初始化演示"""
        self.api = EnhancedBinanceEventContractsAPI()
        self.setup_callbacks()
        
    def setup_callbacks(self):
        """设置回调函数"""
        self.api.set_callbacks(
            on_price_update=self.on_price_update,
            on_orderbook_update=self.on_orderbook_update,
            on_trade_update=self.on_trade_update,
            on_ticker_update=self.on_ticker_update,
            on_error=self.on_error
        )
        
    def on_price_update(self, symbol: str, price_data: dict):
        """价格更新回调"""
        price = price_data.get('price', 0)
        timestamp = price_data.get('timestamp', 0)
        print(f"💰 Price Update: {symbol} = ${price:.4f} at {datetime.fromtimestamp(timestamp/1000)}")
        
    def on_orderbook_update(self, symbol: str, orderbook_data: dict):
        """订单簿更新回调"""
        bids = orderbook_data.get('bids', [])
        asks = orderbook_data.get('asks', [])
        
        if bids and asks:
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            spread = best_ask - best_bid
            print(f"📊 Orderbook: {symbol} Bid: ${best_bid:.4f} Ask: ${best_ask:.4f} Spread: ${spread:.4f}")
        
    def on_trade_update(self, trade_data: dict):
        """交易更新回调"""
        symbol = trade_data.get('symbol', '')
        price = trade_data.get('price', 0)
        quantity = trade_data.get('quantity', 0)
        is_buyer_maker = trade_data.get('is_buyer_maker', False)
        
        side = "SELL" if is_buyer_maker else "BUY"
        print(f"🔄 Trade: {symbol} {side} {quantity} @ ${price:.4f}")
        
    def on_ticker_update(self, symbol: str, ticker_data: dict):
        """24小时统计更新回调"""
        price_change_percent = ticker_data.get('price_change_percent', 0)
        volume = ticker_data.get('volume', 0)
        
        change_emoji = "📈" if price_change_percent > 0 else "📉" if price_change_percent < 0 else "➡️"
        print(f"{change_emoji} Ticker: {symbol} Change: {price_change_percent:.2f}% Volume: {volume}")
        
    def on_error(self, error):
        """错误回调"""
        print(f"❌ Error: {error}")
        
    def demo_basic_info(self):
        """演示基础信息查询"""
        print("\n" + "="*50)
        print("📋 基础信息查询演示")
        print("="*50)
        
        # 获取交易所信息
        print("\n1. 获取交易所信息...")
        success, error = self.api.get_exchange_info()
        if success:
            print(f"✅ 交易所信息获取成功，支持 {len(success.get('symbols', []))} 个交易对")
        else:
            print(f"❌ 获取交易所信息失败: {error}")
            
        # 获取活跃合约
        print("\n2. 获取活跃事件合约...")
        active_contracts = self.api.get_active_contracts()
        print(f"✅ 找到 {len(active_contracts)} 个活跃合约")
        
        # 显示前5个合约信息
        for i, contract in enumerate(active_contracts[:5]):
            symbol = contract.get('symbol', 'Unknown')
            parsed_info = contract.get('parsed_info', {})
            time_to_expiry = contract.get('time_to_expiry', 0)
            
            print(f"   📄 {i+1}. {symbol}")
            print(f"      标的: {parsed_info.get('underlying', 'N/A')}")
            print(f"      执行价: ${parsed_info.get('strike_price', 0)}")
            print(f"      到期时间: {time_to_expiry//60} 分钟")
            
    def demo_btc_contracts(self):
        """演示BTC合约查询"""
        print("\n" + "="*50)
        print("₿ BTC合约查询演示")
        print("="*50)
        
        # 获取BTC合约
        btc_contracts = self.api.get_btc_contracts()
        print(f"✅ 找到 {len(btc_contracts)} 个BTC相关合约")
        
        # 查找适合交易的BTC合约
        suitable_contracts = self.api.find_suitable_contracts(
            underlying="BTC",
            min_time_to_expiry=600,   # 10分钟
            max_time_to_expiry=1800,  # 30分钟
        )
        
        print(f"✅ 找到 {len(suitable_contracts)} 个适合交易的BTC合约")
        
        # 显示适合的合约
        for i, contract in enumerate(suitable_contracts[:3]):
            symbol = contract.get('symbol', 'Unknown')
            parsed_info = contract.get('parsed_info', {})
            time_to_expiry = contract.get('time_to_expiry', 0)
            
            print(f"   🎯 {i+1}. {symbol}")
            print(f"      执行价: ${parsed_info.get('strike_price', 0)}")
            print(f"      类型: {'看涨' if parsed_info.get('contract_type') == 'C' else '看跌'}")
            print(f"      剩余时间: {time_to_expiry//60} 分钟 {time_to_expiry%60} 秒")
            
    def demo_market_data(self, symbol: str = None):
        """演示市场数据获取"""
        print("\n" + "="*50)
        print("📊 市场数据获取演示")
        print("="*50)
        
        # 如果没有指定符号，使用第一个BTC合约
        if not symbol:
            btc_contracts = self.api.get_btc_contracts()
            if not btc_contracts:
                print("❌ 没有找到BTC合约")
                return
            symbol = btc_contracts[0].get('symbol')
            
        print(f"📈 获取合约 {symbol} 的市场数据...")
        
        # 获取市场数据
        market_data = self.api.get_contract_market_data(symbol)
        
        print(f"   当前价格: ${market_data.get('current_price', 'N/A')}")
        
        # 订单簿信息
        orderbook = market_data.get('orderbook')
        if orderbook:
            bids = orderbook.get('bids', [])
            asks = orderbook.get('asks', [])
            
            print(f"   最佳买价: ${bids[0][0]:.4f}" if bids else "   最佳买价: N/A")
            print(f"   最佳卖价: ${asks[0][0]:.4f}" if asks else "   最佳卖价: N/A")
            
        # 24小时统计
        ticker = market_data.get('ticker')
        if ticker:
            print(f"   24h涨跌: {ticker.get('price_change_percent', 0):.2f}%")
            print(f"   24h成交量: {ticker.get('volume', 0)}")
            
        # 最近交易
        recent_trades = market_data.get('recent_trades', [])
        if recent_trades:
            print(f"   最近 {len(recent_trades)} 笔交易:")
            for trade in recent_trades[-3:]:  # 显示最近3笔
                price = trade.get('price', 0)
                quantity = trade.get('quantity', 0)
                print(f"     ${price:.4f} x {quantity}")
                
    def demo_price_monitoring(self):
        """演示价格监控功能"""
        print("\n" + "="*50)
        print("👁️ 价格监控演示")
        print("="*50)
        
        # 获取一个BTC合约用于监控
        btc_contracts = self.api.get_btc_contracts()
        if not btc_contracts:
            print("❌ 没有找到BTC合约用于监控")
            return
            
        symbol = btc_contracts[0].get('symbol')
        current_price = self.api.data_manager.get_price(symbol)
        
        if not current_price:
            print(f"❌ 无法获取 {symbol} 的当前价格")
            return
            
        print(f"📌 设置价格监控: {symbol} (当前价格: ${current_price:.4f})")
        
        # 设置价格警报
        upper_target = current_price + 0.01  # 上涨1分钱触发
        lower_target = current_price - 0.01  # 下跌1分钱触发
        
        def price_alert_callback(monitor_id: str, monitor_data: dict):
            triggered_price = monitor_data.get('triggered_price', 0)
            target_price = monitor_data.get('target_price', 0)
            condition = monitor_data.get('condition', '')
            
            print(f"🚨 价格警报触发! {symbol} 价格 ${triggered_price:.4f} {condition} ${target_price:.4f}")
            
        # 添加监控
        upper_monitor = self.api.add_price_monitor(symbol, upper_target, price_alert_callback, "above")
        lower_monitor = self.api.add_price_monitor(symbol, lower_target, price_alert_callback, "below")
        
        print(f"   ⬆️ 上涨警报: ${upper_target:.4f} (ID: {upper_monitor})")
        print(f"   ⬇️ 下跌警报: ${lower_target:.4f} (ID: {lower_monitor})")
        
        return [upper_monitor, lower_monitor]
        
    def demo_analytics(self):
        """演示合约分析功能"""
        print("\n" + "="*50)
        print("🔍 合约分析演示")
        print("="*50)
        
        # 获取BTC合约进行分析
        btc_contracts = self.api.get_btc_contracts()
        if not btc_contracts:
            print("❌ 没有找到BTC合约")
            return
            
        # 分析前3个合约
        for i, contract in enumerate(btc_contracts[:3]):
            symbol = contract.get('symbol')
            print(f"\n📊 分析合约 {i+1}: {symbol}")
            
            analytics = self.api.get_contract_analytics(symbol)
            
            print(f"   当前价格: ${analytics.get('current_price', 0):.4f}")
            print(f"   买卖价差: ${analytics.get('bid_ask_spread', 0):.4f}")
            print(f"   价差百分比: {analytics.get('bid_ask_spread_percent', 0):.3f}%")
            print(f"   流动性评分: {analytics.get('liquidity_score', 0):.1f}/100")
            print(f"   24h涨跌: {analytics.get('price_change_percent_24h', 0):.2f}%")
            print(f"   24h成交量: {analytics.get('volume_24h', 0)}")
            
    def run_demo(self):
        """运行完整演示"""
        print("\n🚀 币安事件合约增强版API演示开始")
        print("="*80)
        
        try:
            # 基础信息演示
            self.demo_basic_info()
            
            # BTC合约演示
            self.demo_btc_contracts()
            
            # 获取一个合约符号用于后续演示
            btc_contracts = self.api.get_btc_contracts()
            demo_symbol = btc_contracts[0].get('symbol') if btc_contracts else None
            
            if demo_symbol:
                # 启动实时数据订阅
                print(f"\n🔄 启动实时数据订阅: {demo_symbol}")
                self.api.start_real_time_data([demo_symbol])
                
                # 等待一些数据
                print("⏳ 等待实时数据...")
                time.sleep(5)
                
                # 市场数据演示
                self.demo_market_data(demo_symbol)
                
                # 价格监控演示
                monitors = self.demo_price_monitoring()
                
                # 合约分析演示
                self.demo_analytics()
                
                # 运行监控检查
                print(f"\n👁️ 运行价格监控检查 (30秒)...")
                for i in range(6):  # 检查30秒
                    self.api.check_price_monitors()
                    time.sleep(5)
                    print(f"   ⏰ 检查中... {(i+1)*5}/30 秒")
                
                # 清理监控
                if monitors:
                    for monitor_id in monitors:
                        self.api.remove_price_monitor(monitor_id)
                    print(f"🧹 清理了 {len(monitors)} 个价格监控")
                
                # 停止实时数据
                self.api.stop_real_time_data()
                print("⏹️ 实时数据订阅已停止")
            else:
                print("❌ 没有找到可用的合约进行实时数据演示")
                
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断演示")
        except Exception as e:
            print(f"\n❌ 演示过程中出现错误: {e}")
        finally:
            # 确保清理资源
            self.api.stop_real_time_data()
            
        print("\n✅ 演示完成!")
        print("="*80)


def main():
    """主函数"""
    try:
        demo = EventContractsDemo()
        demo.run_demo()
    except Exception as e:
        print(f"❌ 演示启动失败: {e}")


if __name__ == "__main__":
    main()