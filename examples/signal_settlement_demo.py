#!/usr/bin/env python3
"""
信号结算系统演示
展示完整的10分钟信号结算流程：
1. 信号生成时自动添加到结算跟踪器
2. 10分钟后自动检查并结算到期信号
3. 实时胜率统计和报告
"""

import asyncio
import time
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.signal_settlement_checker import SignalSettlementChecker
from quant.utils import logger


class SignalSettlementDemo:
    def __init__(self):
        self.settlement_checker = SignalSettlementChecker("./data/demo_signal_settlement.db")
        logger.info("🎯 信号结算系统演示启动")

    def generate_sample_signals(self):
        """生成示例信号"""
        logger.info("\\n📈 生成示例信号...")
        
        # 生成多个测试信号
        signals = [
            {
                'direction': 'UP',
                'confidence': 75.0,
                'signal_price': 43500.0,
                'signal_strength': 'STRONG',
                'supporting_indicators': ['RSI', 'MACD', 'BB'],
                'market_conditions': 'BULLISH'
            },
            {
                'direction': 'DOWN', 
                'confidence': 80.0,
                'signal_price': 43600.0,
                'signal_strength': 'MEDIUM',
                'supporting_indicators': ['MACD', 'EMA'],
                'market_conditions': 'BEARISH'
            },
            {
                'direction': 'UP',
                'confidence': 65.0,
                'signal_price': 43550.0,
                'signal_strength': 'WEAK',
                'supporting_indicators': ['RSI'],
                'market_conditions': 'NEUTRAL'
            }
        ]
        
        signal_ids = []
        for i, signal in enumerate(signals):
            signal_id = self.settlement_checker.add_signal_record(signal)
            signal_ids.append(signal_id)
            
            logger.info(f"✅ 信号 {i+1}: {signal_id}")
            logger.info(f"   方向: {signal['direction']}")
            logger.info(f"   价格: {signal['signal_price']:.2f} USDT")
            logger.info(f"   置信度: {signal['confidence']:.1f}%")
            logger.info(f"   强度: {signal['signal_strength']}")
            logger.info("")
        
        return signal_ids

    def show_pending_signals(self):
        """显示待结算信号"""
        logger.info("\\n📋 当前待结算信号:")
        
        pending_count = self.settlement_checker.get_pending_signals_count()
        logger.info(f"📊 待结算信号数量: {pending_count}")
        
        if pending_count > 0:
            # 获取详细信息
            pending_signals = self.settlement_checker._get_pending_signals()
            
            for i, signal in enumerate(pending_signals, 1):
                expiry_time = datetime.fromisoformat(signal['expiry_time'])
                remaining_time = expiry_time - datetime.now()
                
                logger.info(f"   {i}. 信号ID: {signal['signal_id']}")
                logger.info(f"      方向: {signal['direction']}")
                logger.info(f"      价格: {signal['signal_price']:.2f} USDT")
                logger.info(f"      置信度: {signal['confidence']:.1f}%")
                logger.info(f"      剩余时间: {remaining_time}")
                logger.info("")
        else:
            logger.info("   暂无待结算信号")

    def simulate_settlement_process(self):
        """模拟结算过程"""
        logger.info("\\n⏰ 模拟信号结算过程...")
        
        # 模拟价格变化
        simulated_prices = [43520.0, 43580.0, 43490.0, 43610.0, 43540.0]
        
        for i, price in enumerate(simulated_prices):
            logger.info(f"\\n⏱️  模拟时间点 {i+1}: 当前价格 {price:.2f} USDT")
            
            # 检查结算
            settled_signals = self.settlement_checker.check_and_settle_signals(price)
            
            if settled_signals:
                logger.info(f"🎯 结算了 {len(settled_signals)} 个信号:")
                
                for signal in settled_signals:
                    result_emoji = "✅" if signal['result'] == 'WIN' else "❌" if signal['result'] == 'LOSS' else "⚖️"
                    direction_emoji = "🚀" if signal['direction'] == 'UP' else "📉"
                    
                    logger.info(f"   {result_emoji} 信号: {signal['signal_id']}")
                    logger.info(f"      方向: {signal['direction']} {direction_emoji}")
                    logger.info(f"      信号价格: {signal['signal_price']:.2f} USDT")
                    logger.info(f"      结算价格: {signal['settlement_price']:.2f} USDT")
                    logger.info(f"      价格变化: {signal['price_change']:.2f}%")
                    logger.info(f"      结果: {signal['result']}")
                    logger.info(f"      置信度: {signal['confidence']:.1f}%")
            else:
                logger.info("   无信号到期")
            
            # 显示当前统计
            self.show_current_stats()
            
            # 等待一段时间（模拟时间流逝）
            time.sleep(1)

    def show_current_stats(self):
        """显示当前统计信息"""
        stats = self.settlement_checker.get_settlement_stats(days=1)
        
        logger.info("\\n📊 当前统计:")
        logger.info(f"   总结算数: {stats['total_settled']}")
        logger.info(f"   胜利数: {stats['total_wins']}")
        logger.info(f"   失败数: {stats['total_losses']}")
        logger.info(f"   平局数: {stats['total_ties']}")
        logger.info(f"   整体胜率: {stats['overall_win_rate']:.1f}%")
        logger.info(f"   待结算数: {stats['pending_signals']}")

    def simulate_expired_signals(self):
        """模拟过期信号结算"""
        logger.info("\\n⏰ 模拟过期信号结算...")
        
        # 手动设置一些信号为过期状态
        import sqlite3
        
        # 重试逻辑避免数据库锁定
        max_retries = 3
        for attempt in range(max_retries):
            try:
                conn = sqlite3.connect(self.settlement_checker.db_path, timeout=30)
                cursor = conn.cursor()
                break
            except sqlite3.OperationalError as e:
                if attempt < max_retries - 1:
                    logger.warning(f"数据库连接失败，重试 {attempt + 1}/{max_retries}")
                    time.sleep(1)
                else:
                    logger.error(f"数据库连接失败: {e}")
                    return
        
        # 获取待结算信号
        cursor.execute('SELECT signal_id FROM signal_records WHERE result = "PENDING" LIMIT 3')
        pending_signals = cursor.fetchall()
        
        if pending_signals:
            past_expiry = (datetime.now() - timedelta(minutes=1)).isoformat()
            
            for signal_id_tuple in pending_signals:
                signal_id = signal_id_tuple[0]
                cursor.execute('''
                    UPDATE signal_records 
                    SET expiry_time = ? 
                    WHERE signal_id = ?
                ''', (past_expiry, signal_id))
                
                logger.info(f"⏰ 设置信号 {signal_id} 为过期状态")
        
        conn.commit()
        conn.close()
        
        # 执行结算
        current_price = 43555.0
        logger.info(f"💰 当前价格: {current_price:.2f} USDT")
        
        settled_signals = self.settlement_checker.check_and_settle_signals(current_price)
        
        if settled_signals:
            logger.info(f"🎯 结算了 {len(settled_signals)} 个过期信号:")
            
            for signal in settled_signals:
                result_emoji = "✅" if signal['result'] == 'WIN' else "❌" if signal['result'] == 'LOSS' else "⚖️"
                direction_emoji = "🚀" if signal['direction'] == 'UP' else "📉"
                
                logger.info(f"   {result_emoji} 信号: {signal['signal_id']}")
                logger.info(f"      方向: {signal['direction']} {direction_emoji}")
                logger.info(f"      信号价格: {signal['signal_price']:.2f} USDT")
                logger.info(f"      结算价格: {signal['settlement_price']:.2f} USDT")
                logger.info(f"      价格变化: {signal['price_change']:.2f}%")
                logger.info(f"      结果: {signal['result']}")
        else:
            logger.info("   无过期信号需要结算")

    def show_final_report(self):
        """显示最终报告"""
        logger.info("\\n📋 最终结算报告")
        logger.info("=" * 50)
        
        # 获取详细统计
        stats = self.settlement_checker.get_settlement_stats(days=1)
        
        logger.info("📊 整体表现:")
        logger.info(f"   总信号数: {stats['total_settled']}")
        logger.info(f"   胜利信号: {stats['total_wins']} ✅")
        logger.info(f"   失败信号: {stats['total_losses']} ❌")
        logger.info(f"   平局信号: {stats['total_ties']} ⚖️")
        logger.info(f"   整体胜率: {stats['overall_win_rate']:.1f}%")
        logger.info(f"   平均价格变化: {stats['total_pnl']:.2f}%")
        logger.info(f"   待结算信号: {stats['pending_signals']}")
        
        # 按方向分析
        up_stats = self.settlement_checker.get_historical_win_rates(direction='UP', lookback_days=1)
        down_stats = self.settlement_checker.get_historical_win_rates(direction='DOWN', lookback_days=1)
        
        logger.info("\\n🚀 看涨信号表现:")
        logger.info(f"   总数: {up_stats['total_signals']}")
        logger.info(f"   胜率: {up_stats['win_rate']:.1f}%")
        logger.info(f"   平均置信度: {up_stats['avg_confidence']:.1f}%")
        
        logger.info("\\n📉 看跌信号表现:")
        logger.info(f"   总数: {down_stats['total_signals']}")
        logger.info(f"   胜率: {down_stats['win_rate']:.1f}%")
        logger.info(f"   平均置信度: {down_stats['avg_confidence']:.1f}%")
        
        logger.info("\\n" + "=" * 50)

    def cleanup(self):
        """清理演示数据"""
        logger.info("\\n🧹 清理演示数据...")
        try:
            import sqlite3
            conn = sqlite3.connect(self.settlement_checker.db_path)
            cursor = conn.cursor()
            
            # 清空演示数据
            cursor.execute('DELETE FROM signal_records')
            cursor.execute('DELETE FROM settlement_stats')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 演示数据已清理")
        except Exception as e:
            logger.error(f"❌ 清理演示数据失败: {e}")

    def run_demo(self):
        """运行完整演示"""
        logger.info("🎯 开始信号结算系统演示")
        logger.info("=" * 60)
        
        try:
            # 1. 生成示例信号
            signal_ids = self.generate_sample_signals()
            
            # 2. 显示待结算信号
            self.show_pending_signals()
            
            # 3. 模拟结算过程
            self.simulate_settlement_process()
            
            # 4. 模拟过期信号结算
            self.simulate_expired_signals()
            
            # 5. 显示最终报告
            self.show_final_report()
            
            logger.info("\\n🎉 演示完成！")
            
        except Exception as e:
            logger.error(f"❌ 演示过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理演示数据
            self.cleanup()


def main():
    """主函数"""
    demo = SignalSettlementDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()