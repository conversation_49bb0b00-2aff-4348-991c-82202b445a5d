"""
币安事件合约系统性能监控工具
监控系统运行性能和资源使用情况
"""

import asyncio
import time
import psutil
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any
import traceback

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {
            'system_metrics': [],
            'api_metrics': [],
            'trading_metrics': [],
            'notification_metrics': []
        }
        self.start_time = datetime.now()
        self.is_monitoring = False
    
    async def start_monitoring(self, duration_minutes: int = 60):
        """开始性能监控"""
        self.is_monitoring = True
        self.start_time = datetime.now()
        
        print(f"🔍 开始性能监控，持续时间: {duration_minutes} 分钟")
        print("=" * 60)
        
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        
        while self.is_monitoring and datetime.now() < end_time:
            # 收集系统指标
            await self.collect_system_metrics()
            
            # 收集API性能指标
            await self.collect_api_metrics()
            
            # 收集交易性能指标
            await self.collect_trading_metrics()
            
            # 收集通知性能指标
            await self.collect_notification_metrics()
            
            # 显示实时指标
            await self.display_realtime_metrics()
            
            # 等待下一次收集
            await asyncio.sleep(10)  # 每10秒收集一次
        
        print("\n📊 性能监控完成")
        await self.generate_performance_report()
    
    async def collect_system_metrics(self):
        """收集系统性能指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            # 网络使用情况
            network = psutil.net_io_counters()
            
            system_metric = {
                'timestamp': datetime.now(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_mb': memory.used / (1024 * 1024),
                'memory_available_mb': memory.available / (1024 * 1024),
                'disk_percent': (disk.used / disk.total) * 100,
                'disk_used_gb': disk.used / (1024 * 1024 * 1024),
                'disk_free_gb': disk.free / (1024 * 1024 * 1024),
                'network_bytes_sent': network.bytes_sent,
                'network_bytes_recv': network.bytes_recv
            }
            
            self.metrics['system_metrics'].append(system_metric)
            
        except Exception as e:
            print(f"❌ 收集系统指标失败: {e}")
    
    async def collect_api_metrics(self):
        """收集API性能指标"""
        try:
            # 模拟API调用性能测试
            api_tests = [
                {'name': 'get_price', 'expected_time': 0.1},
                {'name': 'place_order', 'expected_time': 0.2},
                {'name': 'get_balance', 'expected_time': 0.05},
                {'name': 'get_contracts', 'expected_time': 0.15}
            ]
            
            api_results = []
            for test in api_tests:
                start_time = time.time()
                
                # 模拟API调用
                await asyncio.sleep(0.01)  # 模拟网络延迟
                
                end_time = time.time()
                response_time = end_time - start_time
                
                api_results.append({
                    'api_name': test['name'],
                    'response_time': response_time,
                    'expected_time': test['expected_time'],
                    'performance_ok': response_time <= test['expected_time'],
                    'timestamp': datetime.now()
                })
            
            api_metric = {
                'timestamp': datetime.now(),
                'api_calls': api_results,
                'avg_response_time': sum(r['response_time'] for r in api_results) / len(api_results),
                'success_rate': sum(1 for r in api_results if r['performance_ok']) / len(api_results)
            }
            
            self.metrics['api_metrics'].append(api_metric)
            
        except Exception as e:
            print(f"❌ 收集API指标失败: {e}")
    
    async def collect_trading_metrics(self):
        """收集交易性能指标"""
        try:
            # 模拟交易流程性能测试
            trading_steps = [
                {'name': 'signal_generation', 'expected_time': 0.05},
                {'name': 'decision_making', 'expected_time': 0.03},
                {'name': 'order_execution', 'expected_time': 0.2},
                {'name': 'record_keeping', 'expected_time': 0.02}
            ]
            
            trading_results = []
            total_start_time = time.time()
            
            for step in trading_steps:
                start_time = time.time()
                
                # 模拟交易步骤
                if step['name'] == 'signal_generation':
                    await self.simulate_signal_generation()
                elif step['name'] == 'decision_making':
                    await self.simulate_decision_making()
                elif step['name'] == 'order_execution':
                    await self.simulate_order_execution()
                elif step['name'] == 'record_keeping':
                    await self.simulate_record_keeping()
                
                end_time = time.time()
                step_time = end_time - start_time
                
                trading_results.append({
                    'step_name': step['name'],
                    'execution_time': step_time,
                    'expected_time': step['expected_time'],
                    'performance_ok': step_time <= step['expected_time']
                })
            
            total_time = time.time() - total_start_time
            
            trading_metric = {
                'timestamp': datetime.now(),
                'trading_steps': trading_results,
                'total_execution_time': total_time,
                'expected_total_time': 0.3,  # 300ms总预期时间
                'overall_performance_ok': total_time <= 1.0,  # 1秒内完成
                'throughput': 1 / total_time if total_time > 0 else 0  # 每秒处理能力
            }
            
            self.metrics['trading_metrics'].append(trading_metric)
            
        except Exception as e:
            print(f"❌ 收集交易指标失败: {e}")
    
    async def simulate_signal_generation(self):
        """模拟信号生成过程"""
        # 模拟技术指标计算
        await asyncio.sleep(0.01)
        
        # 模拟信号分析
        await asyncio.sleep(0.02)
    
    async def simulate_decision_making(self):
        """模拟决策制定过程"""
        # 模拟风险评估
        await asyncio.sleep(0.01)
        
        # 模拟决策逻辑
        await asyncio.sleep(0.01)
    
    async def simulate_order_execution(self):
        """模拟订单执行过程"""
        # 模拟API调用
        await asyncio.sleep(0.05)
        
        # 模拟订单确认
        await asyncio.sleep(0.02)
    
    async def simulate_record_keeping(self):
        """模拟记录保存过程"""
        # 模拟数据库写入
        await asyncio.sleep(0.01)
    
    async def collect_notification_metrics(self):
        """收集通知性能指标"""
        try:
            # 模拟通知发送性能测试
            notification_types = [
                {'name': 'trade_signal', 'expected_time': 0.1},
                {'name': 'settlement', 'expected_time': 0.08},
                {'name': 'risk_warning', 'expected_time': 0.05},
                {'name': 'status_report', 'expected_time': 0.12}
            ]
            
            notification_results = []
            for notif_type in notification_types:
                start_time = time.time()
                
                # 模拟通知发送
                await asyncio.sleep(0.02)  # 模拟网络请求
                
                end_time = time.time()
                send_time = end_time - start_time
                
                notification_results.append({
                    'notification_type': notif_type['name'],
                    'send_time': send_time,
                    'expected_time': notif_type['expected_time'],
                    'performance_ok': send_time <= notif_type['expected_time'],
                    'success': True  # 模拟成功发送
                })
            
            notification_metric = {
                'timestamp': datetime.now(),
                'notifications': notification_results,
                'avg_send_time': sum(r['send_time'] for r in notification_results) / len(notification_results),
                'success_rate': sum(1 for r in notification_results if r['success']) / len(notification_results),
                'performance_rate': sum(1 for r in notification_results if r['performance_ok']) / len(notification_results)
            }
            
            self.metrics['notification_metrics'].append(notification_metric)
            
        except Exception as e:
            print(f"❌ 收集通知指标失败: {e}")
    
    async def display_realtime_metrics(self):
        """显示实时性能指标"""
        try:
            # 获取最新指标
            if self.metrics['system_metrics']:
                latest_system = self.metrics['system_metrics'][-1]
                print(f"💻 系统: CPU {latest_system['cpu_percent']:.1f}%, 内存 {latest_system['memory_percent']:.1f}%")
            
            if self.metrics['api_metrics']:
                latest_api = self.metrics['api_metrics'][-1]
                print(f"🔌 API: 平均响应时间 {latest_api['avg_response_time']:.3f}s, 成功率 {latest_api['success_rate']:.1%}")
            
            if self.metrics['trading_metrics']:
                latest_trading = self.metrics['trading_metrics'][-1]
                print(f"📈 交易: 总执行时间 {latest_trading['total_execution_time']:.3f}s, 吞吐量 {latest_trading['throughput']:.1f}/s")
            
            if self.metrics['notification_metrics']:
                latest_notification = self.metrics['notification_metrics'][-1]
                print(f"📱 通知: 平均发送时间 {latest_notification['avg_send_time']:.3f}s, 成功率 {latest_notification['success_rate']:.1%}")
            
            print("-" * 60)
            
        except Exception as e:
            print(f"❌ 显示实时指标失败: {e}")
    
    async def generate_performance_report(self):
        """生成性能报告"""
        try:
            print("\n📊 性能监控报告")
            print("=" * 60)
            
            # 系统性能统计
            if self.metrics['system_metrics']:
                system_metrics = self.metrics['system_metrics']
                avg_cpu = sum(m['cpu_percent'] for m in system_metrics) / len(system_metrics)
                avg_memory = sum(m['memory_percent'] for m in system_metrics) / len(system_metrics)
                max_cpu = max(m['cpu_percent'] for m in system_metrics)
                max_memory = max(m['memory_percent'] for m in system_metrics)
                
                print(f"💻 系统性能:")
                print(f"   平均CPU使用率: {avg_cpu:.1f}%")
                print(f"   平均内存使用率: {avg_memory:.1f}%")
                print(f"   最大CPU使用率: {max_cpu:.1f}%")
                print(f"   最大内存使用率: {max_memory:.1f}%")
            
            # API性能统计
            if self.metrics['api_metrics']:
                api_metrics = self.metrics['api_metrics']
                avg_response_time = sum(m['avg_response_time'] for m in api_metrics) / len(api_metrics)
                avg_success_rate = sum(m['success_rate'] for m in api_metrics) / len(api_metrics)
                
                print(f"\n🔌 API性能:")
                print(f"   平均响应时间: {avg_response_time:.3f}s")
                print(f"   平均成功率: {avg_success_rate:.1%}")
            
            # 交易性能统计
            if self.metrics['trading_metrics']:
                trading_metrics = self.metrics['trading_metrics']
                avg_execution_time = sum(m['total_execution_time'] for m in trading_metrics) / len(trading_metrics)
                avg_throughput = sum(m['throughput'] for m in trading_metrics) / len(trading_metrics)
                performance_ok_rate = sum(1 for m in trading_metrics if m['overall_performance_ok']) / len(trading_metrics)
                
                print(f"\n📈 交易性能:")
                print(f"   平均执行时间: {avg_execution_time:.3f}s")
                print(f"   平均吞吐量: {avg_throughput:.1f} 交易/秒")
                print(f"   性能达标率: {performance_ok_rate:.1%}")
            
            # 通知性能统计
            if self.metrics['notification_metrics']:
                notification_metrics = self.metrics['notification_metrics']
                avg_send_time = sum(m['avg_send_time'] for m in notification_metrics) / len(notification_metrics)
                avg_success_rate = sum(m['success_rate'] for m in notification_metrics) / len(notification_metrics)
                avg_performance_rate = sum(m['performance_rate'] for m in notification_metrics) / len(notification_metrics)
                
                print(f"\n📱 通知性能:")
                print(f"   平均发送时间: {avg_send_time:.3f}s")
                print(f"   平均成功率: {avg_success_rate:.1%}")
                print(f"   性能达标率: {avg_performance_rate:.1%}")
            
            # 总体评估
            print(f"\n🎯 总体评估:")
            
            # 性能等级评估
            performance_grade = self.calculate_performance_grade()
            print(f"   性能等级: {performance_grade}")
            
            # 建议
            recommendations = self.generate_recommendations()
            if recommendations:
                print(f"\n💡 优化建议:")
                for rec in recommendations:
                    print(f"   - {rec}")
            
            # 保存报告
            await self.save_performance_report()
            
        except Exception as e:
            print(f"❌ 生成性能报告失败: {e}")
            traceback.print_exc()
    
    def calculate_performance_grade(self) -> str:
        """计算性能等级"""
        score = 0
        max_score = 0
        
        # 系统性能评分
        if self.metrics['system_metrics']:
            system_metrics = self.metrics['system_metrics']
            avg_cpu = sum(m['cpu_percent'] for m in system_metrics) / len(system_metrics)
            avg_memory = sum(m['memory_percent'] for m in system_metrics) / len(system_metrics)
            
            if avg_cpu < 50:
                score += 25
            elif avg_cpu < 70:
                score += 15
            elif avg_cpu < 90:
                score += 5
            
            if avg_memory < 50:
                score += 25
            elif avg_memory < 70:
                score += 15
            elif avg_memory < 90:
                score += 5
            
            max_score += 50
        
        # API性能评分
        if self.metrics['api_metrics']:
            api_metrics = self.metrics['api_metrics']
            avg_response_time = sum(m['avg_response_time'] for m in api_metrics) / len(api_metrics)
            avg_success_rate = sum(m['success_rate'] for m in api_metrics) / len(api_metrics)
            
            if avg_response_time < 0.1:
                score += 15
            elif avg_response_time < 0.2:
                score += 10
            elif avg_response_time < 0.5:
                score += 5
            
            if avg_success_rate >= 0.95:
                score += 10
            elif avg_success_rate >= 0.9:
                score += 5
            
            max_score += 25
        
        # 交易性能评分
        if self.metrics['trading_metrics']:
            trading_metrics = self.metrics['trading_metrics']
            avg_execution_time = sum(m['total_execution_time'] for m in trading_metrics) / len(trading_metrics)
            
            if avg_execution_time < 0.5:
                score += 25
            elif avg_execution_time < 1.0:
                score += 15
            elif avg_execution_time < 2.0:
                score += 5
            
            max_score += 25
        
        # 计算百分比
        if max_score > 0:
            percentage = (score / max_score) * 100
            
            if percentage >= 90:
                return "A+ (优秀)"
            elif percentage >= 80:
                return "A (良好)"
            elif percentage >= 70:
                return "B (一般)"
            elif percentage >= 60:
                return "C (需要改进)"
            else:
                return "D (性能不佳)"
        
        return "无法评估"
    
    def generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 系统性能建议
        if self.metrics['system_metrics']:
            system_metrics = self.metrics['system_metrics']
            avg_cpu = sum(m['cpu_percent'] for m in system_metrics) / len(system_metrics)
            avg_memory = sum(m['memory_percent'] for m in system_metrics) / len(system_metrics)
            
            if avg_cpu > 80:
                recommendations.append("CPU使用率过高，建议优化算法或增加硬件资源")
            if avg_memory > 80:
                recommendations.append("内存使用率过高，建议优化内存管理或增加内存")
        
        # API性能建议
        if self.metrics['api_metrics']:
            api_metrics = self.metrics['api_metrics']
            avg_response_time = sum(m['avg_response_time'] for m in api_metrics) / len(api_metrics)
            
            if avg_response_time > 0.5:
                recommendations.append("API响应时间过长，建议优化网络连接或使用缓存")
        
        # 交易性能建议
        if self.metrics['trading_metrics']:
            trading_metrics = self.metrics['trading_metrics']
            avg_execution_time = sum(m['total_execution_time'] for m in trading_metrics) / len(trading_metrics)
            
            if avg_execution_time > 1.0:
                recommendations.append("交易执行时间过长，建议优化交易流程或使用并行处理")
        
        return recommendations
    
    async def save_performance_report(self):
        """保存性能报告"""
        try:
            report_data = {
                'monitoring_period': {
                    'start_time': self.start_time.isoformat(),
                    'end_time': datetime.now().isoformat(),
                    'duration_minutes': (datetime.now() - self.start_time).total_seconds() / 60
                },
                'metrics': self.metrics,
                'performance_grade': self.calculate_performance_grade(),
                'recommendations': self.generate_recommendations()
            }
            
            # 保存JSON格式
            json_path = f'tools/performance_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"📄 性能报告已保存到: {json_path}")
            
        except Exception as e:
            print(f"❌ 保存性能报告失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        print("🛑 性能监控已停止")


async def main():
    """主函数"""
    print("🔍 币安事件合约系统性能监控工具")
    print("=" * 60)
    
    monitor = PerformanceMonitor()
    
    try:
        # 开始监控（默认5分钟）
        await monitor.start_monitoring(duration_minutes=5)
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断监控")
        monitor.stop_monitoring()
    except Exception as e:
        print(f"❌ 监控过程出错: {e}")
        traceback.print_exc()
    
    print("\n🎉 性能监控完成！")


if __name__ == "__main__":
    asyncio.run(main()) 