"""
代码质量监控系统
提供代码质量分析、监控和报告功能
"""

import os
import subprocess
import json
import time
import ast
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import xml.etree.ElementTree as ET

from config.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class QualityMetrics:
    """代码质量指标"""
    coverage_percentage: float = 0.0
    complexity_score: float = 0.0
    duplication_percentage: float = 0.0
    maintainability_index: float = 0.0
    security_issues: int = 0
    code_smells: int = 0
    bugs: int = 0
    vulnerabilities: int = 0
    technical_debt_minutes: int = 0
    lines_of_code: int = 0
    test_count: int = 0
    timestamp: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "coverage_percentage": self.coverage_percentage,
            "complexity_score": self.complexity_score,
            "duplication_percentage": self.duplication_percentage,
            "maintainability_index": self.maintainability_index,
            "security_issues": self.security_issues,
            "code_smells": self.code_smells,
            "bugs": self.bugs,
            "vulnerabilities": self.vulnerabilities,
            "technical_debt_minutes": self.technical_debt_minutes,
            "lines_of_code": self.lines_of_code,
            "test_count": self.test_count,
            "timestamp": self.timestamp
        }


@dataclass
class QualityIssue:
    """代码质量问题"""
    file_path: str
    line_number: int
    issue_type: str
    severity: str
    message: str
    rule_id: str
    tool: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "file_path": self.file_path,
            "line_number": self.line_number,
            "issue_type": self.issue_type,
            "severity": self.severity,
            "message": self.message,
            "rule_id": self.rule_id,
            "tool": self.tool,
            "metadata": self.metadata
        }


class CodeComplexityAnalyzer:
    """代码复杂度分析器"""
    
    def __init__(self):
        self.complexity_threshold = 10
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """分析单个文件的复杂度"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            analyzer = ComplexityVisitor()
            analyzer.visit(tree)
            
            return {
                "file_path": file_path,
                "total_complexity": analyzer.total_complexity,
                "function_complexities": analyzer.function_complexities,
                "class_complexities": analyzer.class_complexities,
                "max_complexity": max(analyzer.function_complexities.values()) if analyzer.function_complexities else 0
            }
            
        except Exception as e:
            logger.error(f"分析文件复杂度失败: {file_path}, 错误: {e}")
            return {
                "file_path": file_path,
                "total_complexity": 0,
                "function_complexities": {},
                "class_complexities": {},
                "max_complexity": 0,
                "error": str(e)
            }
    
    def analyze_directory(self, directory: str) -> Dict[str, Any]:
        """分析目录下所有Python文件的复杂度"""
        results = []
        total_complexity = 0
        high_complexity_functions = []
        
        for file_path in Path(directory).rglob("*.py"):
            if self._should_skip_file(str(file_path)):
                continue
            
            file_result = self.analyze_file(str(file_path))
            results.append(file_result)
            
            total_complexity += file_result["total_complexity"]
            
            # 检查高复杂度函数
            for func_name, complexity in file_result["function_complexities"].items():
                if complexity > self.complexity_threshold:
                    high_complexity_functions.append({
                        "file": str(file_path),
                        "function": func_name,
                        "complexity": complexity
                    })
        
        return {
            "results": results,
            "total_complexity": total_complexity,
            "average_complexity": total_complexity / len(results) if results else 0,
            "high_complexity_functions": high_complexity_functions,
            "files_analyzed": len(results)
        }
    
    def _should_skip_file(self, file_path: str) -> bool:
        """判断是否应该跳过文件"""
        skip_patterns = [
            "__pycache__",
            ".git",
            "venv",
            "env",
            ".pytest_cache",
            "build",
            "dist"
        ]
        
        return any(pattern in file_path for pattern in skip_patterns)


class ComplexityVisitor(ast.NodeVisitor):
    """AST访问器，用于计算圈复杂度"""
    
    def __init__(self):
        self.total_complexity = 0
        self.function_complexities = {}
        self.class_complexities = {}
        self.current_function = None
        self.current_class = None
        self.function_complexity = 0
    
    def visit_FunctionDef(self, node):
        """访问函数定义"""
        old_function = self.current_function
        old_complexity = self.function_complexity
        
        self.current_function = node.name
        self.function_complexity = 1  # 基础复杂度
        
        self.generic_visit(node)
        
        self.function_complexities[node.name] = self.function_complexity
        self.total_complexity += self.function_complexity
        
        self.current_function = old_function
        self.function_complexity = old_complexity
    
    def visit_AsyncFunctionDef(self, node):
        """访问异步函数定义"""
        self.visit_FunctionDef(node)
    
    def visit_ClassDef(self, node):
        """访问类定义"""
        old_class = self.current_class
        self.current_class = node.name
        
        class_start_complexity = self.total_complexity
        self.generic_visit(node)
        
        self.class_complexities[node.name] = self.total_complexity - class_start_complexity
        self.current_class = old_class
    
    def visit_If(self, node):
        """访问if语句"""
        self.function_complexity += 1
        self.generic_visit(node)
    
    def visit_While(self, node):
        """访问while循环"""
        self.function_complexity += 1
        self.generic_visit(node)
    
    def visit_For(self, node):
        """访问for循环"""
        self.function_complexity += 1
        self.generic_visit(node)
    
    def visit_AsyncFor(self, node):
        """访问异步for循环"""
        self.function_complexity += 1
        self.generic_visit(node)
    
    def visit_ExceptHandler(self, node):
        """访问异常处理"""
        self.function_complexity += 1
        self.generic_visit(node)
    
    def visit_With(self, node):
        """访问with语句"""
        self.function_complexity += 1
        self.generic_visit(node)
    
    def visit_AsyncWith(self, node):
        """访问异步with语句"""
        self.function_complexity += 1
        self.generic_visit(node)


class CodeDuplicationDetector:
    """代码重复检测器"""
    
    def __init__(self, min_lines: int = 6):
        self.min_lines = min_lines
    
    def detect_duplications(self, directory: str) -> Dict[str, Any]:
        """检测代码重复"""
        try:
            # 使用pylint的相似性检查
            result = subprocess.run([
                "pylint", "--disable=all", "--enable=similarities",
                f"--min-similarity-lines={self.min_lines}",
                directory
            ], capture_output=True, text=True, timeout=300)
            
            duplications = self._parse_pylint_similarities(result.stdout)
            
            return {
                "duplications": duplications,
                "duplication_count": len(duplications),
                "total_duplicated_lines": sum(d["lines"] for d in duplications)
            }
            
        except subprocess.TimeoutExpired:
            logger.error("代码重复检测超时")
            return {"duplications": [], "duplication_count": 0, "total_duplicated_lines": 0}
        except Exception as e:
            logger.error(f"代码重复检测失败: {e}")
            return {"duplications": [], "duplication_count": 0, "total_duplicated_lines": 0}
    
    def _parse_pylint_similarities(self, output: str) -> List[Dict[str, Any]]:
        """解析pylint相似性输出"""
        duplications = []
        lines = output.split('\n')
        
        current_duplication = None
        for line in lines:
            if line.startswith('Similar lines in'):
                # 解析重复行信息
                match = re.search(r'Similar lines in (\d+) files', line)
                if match:
                    current_duplication = {
                        "files": int(match.group(1)),
                        "locations": [],
                        "lines": 0
                    }
            elif current_duplication and ':' in line and line.strip():
                # 解析文件位置信息
                parts = line.strip().split(':')
                if len(parts) >= 2:
                    file_path = parts[0]
                    line_info = parts[1]
                    current_duplication["locations"].append({
                        "file": file_path,
                        "line_info": line_info
                    })
            elif current_duplication and line.strip() == '':
                # 重复块结束
                if current_duplication["locations"]:
                    duplications.append(current_duplication)
                current_duplication = None
        
        return duplications


class SecurityScanner:
    """安全扫描器"""
    
    def scan_directory(self, directory: str) -> Dict[str, Any]:
        """扫描目录安全问题"""
        try:
            # 使用bandit进行安全扫描
            result = subprocess.run([
                "bandit", "-r", directory, "-f", "json"
            ], capture_output=True, text=True, timeout=300)
            
            if result.stdout:
                bandit_results = json.loads(result.stdout)
                security_issues = self._parse_bandit_results(bandit_results)
            else:
                security_issues = []
            
            return {
                "security_issues": security_issues,
                "total_issues": len(security_issues),
                "high_severity": len([i for i in security_issues if i["severity"] == "HIGH"]),
                "medium_severity": len([i for i in security_issues if i["severity"] == "MEDIUM"]),
                "low_severity": len([i for i in security_issues if i["severity"] == "LOW"])
            }
            
        except subprocess.TimeoutExpired:
            logger.error("安全扫描超时")
            return {"security_issues": [], "total_issues": 0}
        except Exception as e:
            logger.error(f"安全扫描失败: {e}")
            return {"security_issues": [], "total_issues": 0}
    
    def _parse_bandit_results(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析bandit扫描结果"""
        issues = []
        
        for result in results.get("results", []):
            issues.append({
                "file": result.get("filename", ""),
                "line": result.get("line_number", 0),
                "test_id": result.get("test_id", ""),
                "test_name": result.get("test_name", ""),
                "issue_severity": result.get("issue_severity", ""),
                "issue_confidence": result.get("issue_confidence", ""),
                "issue_text": result.get("issue_text", ""),
                "code": result.get("code", "")
            })
        
        return issues


class CoverageAnalyzer:
    """代码覆盖率分析器"""
    
    def analyze_coverage(self, source_dir: str = ".", test_dir: str = "tests") -> Dict[str, Any]:
        """分析代码覆盖率"""
        try:
            # 运行pytest with coverage
            result = subprocess.run([
                "pytest", test_dir, "--cov=" + source_dir,
                "--cov-report=xml", "--cov-report=json"
            ], capture_output=True, text=True, timeout=600)
            
            # 解析覆盖率报告
            coverage_data = self._parse_coverage_reports()
            
            return coverage_data
            
        except subprocess.TimeoutExpired:
            logger.error("覆盖率分析超时")
            return {"coverage_percentage": 0.0, "covered_lines": 0, "total_lines": 0}
        except Exception as e:
            logger.error(f"覆盖率分析失败: {e}")
            return {"coverage_percentage": 0.0, "covered_lines": 0, "total_lines": 0}
    
    def _parse_coverage_reports(self) -> Dict[str, Any]:
        """解析覆盖率报告"""
        coverage_data = {
            "coverage_percentage": 0.0,
            "covered_lines": 0,
            "total_lines": 0,
            "files": []
        }
        
        # 尝试解析JSON报告
        if os.path.exists("coverage.json"):
            try:
                with open("coverage.json", 'r') as f:
                    json_data = json.load(f)
                
                totals = json_data.get("totals", {})
                coverage_data["coverage_percentage"] = totals.get("percent_covered", 0.0)
                coverage_data["covered_lines"] = totals.get("covered_lines", 0)
                coverage_data["total_lines"] = totals.get("num_statements", 0)
                
                # 文件级别的覆盖率
                for file_path, file_data in json_data.get("files", {}).items():
                    summary = file_data.get("summary", {})
                    coverage_data["files"].append({
                        "file": file_path,
                        "coverage": summary.get("percent_covered", 0.0),
                        "covered_lines": summary.get("covered_lines", 0),
                        "total_lines": summary.get("num_statements", 0)
                    })
                
            except Exception as e:
                logger.error(f"解析JSON覆盖率报告失败: {e}")
        
        # 尝试解析XML报告
        elif os.path.exists("coverage.xml"):
            try:
                tree = ET.parse("coverage.xml")
                root = tree.getroot()
                
                # 获取总体覆盖率
                coverage_elem = root.find(".//coverage")
                if coverage_elem is not None:
                    line_rate = float(coverage_elem.get("line-rate", 0))
                    coverage_data["coverage_percentage"] = line_rate * 100
                
            except Exception as e:
                logger.error(f"解析XML覆盖率报告失败: {e}")
        
        return coverage_data


class QualityMonitor:
    """代码质量监控器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.complexity_analyzer = CodeComplexityAnalyzer()
        self.duplication_detector = CodeDuplicationDetector()
        self.security_scanner = SecurityScanner()
        self.coverage_analyzer = CoverageAnalyzer()
        self.quality_history: List[QualityMetrics] = []
    
    def run_full_analysis(self) -> QualityMetrics:
        """运行完整的质量分析"""
        logger.info("开始代码质量分析")
        start_time = time.time()
        
        # 复杂度分析
        logger.info("分析代码复杂度...")
        complexity_results = self.complexity_analyzer.analyze_directory(str(self.project_root))
        
        # 重复代码检测
        logger.info("检测代码重复...")
        duplication_results = self.duplication_detector.detect_duplications(str(self.project_root))
        
        # 安全扫描
        logger.info("执行安全扫描...")
        security_results = self.security_scanner.scan_directory(str(self.project_root))
        
        # 覆盖率分析
        logger.info("分析代码覆盖率...")
        coverage_results = self.coverage_analyzer.analyze_coverage(str(self.project_root))
        
        # 计算代码行数
        lines_of_code = self._count_lines_of_code()
        
        # 创建质量指标
        metrics = QualityMetrics(
            coverage_percentage=coverage_results.get("coverage_percentage", 0.0),
            complexity_score=complexity_results.get("average_complexity", 0.0),
            duplication_percentage=self._calculate_duplication_percentage(duplication_results, lines_of_code),
            maintainability_index=self._calculate_maintainability_index(complexity_results, duplication_results),
            security_issues=security_results.get("total_issues", 0),
            vulnerabilities=security_results.get("high_severity", 0),
            lines_of_code=lines_of_code,
            technical_debt_minutes=self._estimate_technical_debt(complexity_results, duplication_results, security_results)
        )
        
        # 记录历史
        self.quality_history.append(metrics)
        
        duration = time.time() - start_time
        logger.info(f"代码质量分析完成，耗时: {duration:.2f}秒")
        
        return metrics
    
    def _count_lines_of_code(self) -> int:
        """统计代码行数"""
        total_lines = 0
        
        for file_path in self.project_root.rglob("*.py"):
            if self._should_skip_file(str(file_path)):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # 排除空行和注释行
                    code_lines = [line for line in lines if line.strip() and not line.strip().startswith('#')]
                    total_lines += len(code_lines)
            except Exception as e:
                logger.warning(f"读取文件失败: {file_path}, 错误: {e}")
        
        return total_lines
    
    def _should_skip_file(self, file_path: str) -> bool:
        """判断是否应该跳过文件"""
        skip_patterns = [
            "__pycache__",
            ".git",
            "venv",
            "env",
            ".pytest_cache",
            "build",
            "dist",
            "migrations"
        ]
        
        return any(pattern in file_path for pattern in skip_patterns)
    
    def _calculate_duplication_percentage(self, duplication_results: Dict[str, Any], total_lines: int) -> float:
        """计算重复代码百分比"""
        if total_lines == 0:
            return 0.0
        
        duplicated_lines = duplication_results.get("total_duplicated_lines", 0)
        return (duplicated_lines / total_lines) * 100
    
    def _calculate_maintainability_index(self, complexity_results: Dict[str, Any], duplication_results: Dict[str, Any]) -> float:
        """计算可维护性指数"""
        # 简化的可维护性指数计算
        complexity_score = complexity_results.get("average_complexity", 0)
        duplication_count = duplication_results.get("duplication_count", 0)
        
        # 基础分数100，根据复杂度和重复度扣分
        maintainability = 100 - (complexity_score * 2) - (duplication_count * 5)
        
        return max(0, min(100, maintainability))
    
    def _estimate_technical_debt(
        self,
        complexity_results: Dict[str, Any],
        duplication_results: Dict[str, Any],
        security_results: Dict[str, Any]
    ) -> int:
        """估算技术债务（分钟）"""
        debt_minutes = 0
        
        # 复杂度债务：每个高复杂度函数30分钟
        high_complexity_functions = complexity_results.get("high_complexity_functions", [])
        debt_minutes += len(high_complexity_functions) * 30
        
        # 重复代码债务：每个重复块15分钟
        duplications = duplication_results.get("duplication_count", 0)
        debt_minutes += duplications * 15
        
        # 安全问题债务：高危60分钟，中危30分钟，低危10分钟
        debt_minutes += security_results.get("high_severity", 0) * 60
        debt_minutes += security_results.get("medium_severity", 0) * 30
        debt_minutes += security_results.get("low_severity", 0) * 10
        
        return debt_minutes
    
    def generate_quality_report(self, output_file: str = "quality_report.html") -> None:
        """生成质量报告"""
        if not self.quality_history:
            logger.warning("没有质量数据可生成报告")
            return
        
        latest_metrics = self.quality_history[-1]
        html_content = self._generate_html_report(latest_metrics)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"质量报告已生成: {output_file}")
    
    def _generate_html_report(self, metrics: QualityMetrics) -> str:
        """生成HTML质量报告"""
        # 质量等级判断
        def get_quality_grade(percentage: float) -> Tuple[str, str]:
            if percentage >= 90:
                return "优秀", "green"
            elif percentage >= 80:
                return "良好", "blue"
            elif percentage >= 70:
                return "一般", "orange"
            else:
                return "需改进", "red"
        
        coverage_grade, coverage_color = get_quality_grade(metrics.coverage_percentage)
        maintainability_grade, maintainability_color = get_quality_grade(metrics.maintainability_index)
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>代码质量报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f5f5f5; padding: 20px; margin-bottom: 20px; }}
                .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
                .metric-card {{ border: 1px solid #ddd; padding: 15px; border-radius: 5px; }}
                .metric-value {{ font-size: 2em; font-weight: bold; }}
                .metric-label {{ color: #666; margin-bottom: 10px; }}
                .good {{ color: green; }}
                .warning {{ color: orange; }}
                .error {{ color: red; }}
                .progress-bar {{ width: 100%; height: 20px; background-color: #f0f0f0; border-radius: 10px; overflow: hidden; }}
                .progress-fill {{ height: 100%; transition: width 0.3s ease; }}
                .green {{ background-color: #4CAF50; }}
                .blue {{ background-color: #2196F3; }}
                .orange {{ background-color: #FF9800; }}
                .red {{ background-color: #f44336; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>代码质量报告</h1>
                <p>生成时间: {time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(metrics.timestamp))}</p>
                <p>项目: Hertelquant 7.0 量化交易系统</p>
            </div>
            
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-label">代码覆盖率</div>
                    <div class="metric-value {coverage_color}">{metrics.coverage_percentage:.1f}%</div>
                    <div class="progress-bar">
                        <div class="progress-fill {coverage_color}" style="width: {metrics.coverage_percentage}%"></div>
                    </div>
                    <p>等级: <span class="{coverage_color}">{coverage_grade}</span></p>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">平均复杂度</div>
                    <div class="metric-value {'good' if metrics.complexity_score <= 5 else 'warning' if metrics.complexity_score <= 10 else 'error'}">{metrics.complexity_score:.1f}</div>
                    <p>建议保持在10以下</p>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">代码重复率</div>
                    <div class="metric-value {'good' if metrics.duplication_percentage <= 3 else 'warning' if metrics.duplication_percentage <= 5 else 'error'}">{metrics.duplication_percentage:.1f}%</div>
                    <p>建议保持在3%以下</p>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">可维护性指数</div>
                    <div class="metric-value {maintainability_color}">{metrics.maintainability_index:.1f}</div>
                    <div class="progress-bar">
                        <div class="progress-fill {maintainability_color}" style="width: {metrics.maintainability_index}%"></div>
                    </div>
                    <p>等级: <span class="{maintainability_color}">{maintainability_grade}</span></p>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">安全问题</div>
                    <div class="metric-value {'good' if metrics.security_issues == 0 else 'warning' if metrics.security_issues <= 5 else 'error'}">{metrics.security_issues}</div>
                    <p>漏洞: {metrics.vulnerabilities}</p>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">技术债务</div>
                    <div class="metric-value {'good' if metrics.technical_debt_minutes <= 60 else 'warning' if metrics.technical_debt_minutes <= 180 else 'error'}">{metrics.technical_debt_minutes // 60}h {metrics.technical_debt_minutes % 60}m</div>
                    <p>估算修复时间</p>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">代码行数</div>
                    <div class="metric-value">{metrics.lines_of_code:,}</div>
                    <p>不包括注释和空行</p>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">测试数量</div>
                    <div class="metric-value">{metrics.test_count}</div>
                    <p>单元测试和集成测试</p>
                </div>
            </div>
            
            <div style="margin-top: 30px;">
                <h2>质量趋势</h2>
                <p>历史记录: {len(self.quality_history)} 次分析</p>
            </div>
            
            <div style="margin-top: 30px;">
                <h2>改进建议</h2>
                <ul>
        """
        
        # 添加改进建议
        if metrics.coverage_percentage < 85:
            html += f"<li>代码覆盖率({metrics.coverage_percentage:.1f}%)低于目标(85%)，建议增加测试用例</li>"
        
        if metrics.complexity_score > 10:
            html += f"<li>平均复杂度({metrics.complexity_score:.1f})过高，建议重构复杂函数</li>"
        
        if metrics.duplication_percentage > 3:
            html += f"<li>代码重复率({metrics.duplication_percentage:.1f}%)过高，建议消除重复代码</li>"
        
        if metrics.security_issues > 0:
            html += f"<li>发现{metrics.security_issues}个安全问题，建议及时修复</li>"
        
        if metrics.technical_debt_minutes > 180:
            html += f"<li>技术债务({metrics.technical_debt_minutes // 60}小时)较高，建议制定重构计划</li>"
        
        html += """
                </ul>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def export_metrics(self, output_file: str = "quality_metrics.json") -> None:
        """导出质量指标"""
        if not self.quality_history:
            logger.warning("没有质量数据可导出")
            return
        
        data = {
            "project": "Hertelquant 7.0",
            "export_time": time.time(),
            "metrics_history": [m.to_dict() for m in self.quality_history],
            "latest_metrics": self.quality_history[-1].to_dict() if self.quality_history else None
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"质量指标已导出到: {output_file}")


# 全局质量监控器实例
_quality_monitor: Optional[QualityMonitor] = None


def get_quality_monitor(project_root: str = ".") -> QualityMonitor:
    """获取全局质量监控器实例"""
    global _quality_monitor
    if _quality_monitor is None:
        _quality_monitor = QualityMonitor(project_root)
    return _quality_monitor


def main():
    """主函数，用于命令行执行"""
    import argparse
    
    parser = argparse.ArgumentParser(description="代码质量监控工具")
    parser.add_argument("--project-root", default=".", help="项目根目录")
    parser.add_argument("--output-dir", default=".", help="输出目录")
    parser.add_argument("--report-format", choices=["html", "json"], default="html", help="报告格式")
    
    args = parser.parse_args()
    
    # 创建质量监控器
    monitor = QualityMonitor(args.project_root)
    
    # 运行分析
    metrics = monitor.run_full_analysis()
    
    # 生成报告
    if args.report_format == "html":
        report_file = os.path.join(args.output_dir, "quality_report.html")
        monitor.generate_quality_report(report_file)
    else:
        report_file = os.path.join(args.output_dir, "quality_metrics.json")
        monitor.export_metrics(report_file)
    
    # 输出摘要
    print(f"\n代码质量分析完成:")
    print(f"覆盖率: {metrics.coverage_percentage:.1f}%")
    print(f"复杂度: {metrics.complexity_score:.1f}")
    print(f"重复率: {metrics.duplication_percentage:.1f}%")
    print(f"安全问题: {metrics.security_issues}")
    print(f"技术债务: {metrics.technical_debt_minutes // 60}h {metrics.technical_debt_minutes % 60}m")
    print(f"报告已生成: {report_file}")


if __name__ == "__main__":
    main()