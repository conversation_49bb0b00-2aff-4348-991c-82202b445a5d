"""
币安事件合约策略参数优化工具
用于调优信号生成、决策引擎等关键参数
"""

import json
import asyncio
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any
import itertools
from dataclasses import dataclass

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


@dataclass
class OptimizationResult:
    """优化结果数据类"""
    parameters: Dict[str, Any]
    total_trades: int
    win_rate: float
    total_pnl: float
    max_drawdown: float
    sharpe_ratio: float
    score: float


class StrategyOptimizer:
    """策略参数优化器"""
    
    def __init__(self):
        self.optimization_results = []
        self.best_parameters = None
        self.test_data = self._generate_test_data()
    
    def _generate_test_data(self) -> List[Dict]:
        """生成测试数据（模拟历史K线数据）"""
        test_data = []
        base_price = 50000
        
        # 生成100个15分钟K线数据
        for i in range(100):
            # 模拟价格波动
            price_change = (i % 10 - 5) * 100 + (i % 3 - 1) * 50
            current_price = base_price + price_change
            
            kline = {
                'timestamp': datetime.now() - timedelta(minutes=15 * (100 - i)),
                'open': current_price - 20,
                'high': current_price + 30,
                'low': current_price - 40,
                'close': current_price,
                'volume': 1000 + i * 10
            }
            test_data.append(kline)
        
        return test_data
    
    def calculate_technical_indicators(self, data: List[Dict], params: Dict) -> Dict:
        """计算技术指标"""
        if len(data) < 20:
            return {}
        
        # 获取收盘价
        closes = [k['close'] for k in data[-20:]]
        
        # RSI计算
        rsi_period = params.get('rsi_period', 14)
        rsi = self._calculate_rsi(closes, rsi_period)
        
        # MACD计算
        macd_fast = params.get('macd_fast', 12)
        macd_slow = params.get('macd_slow', 26)
        macd_signal = params.get('macd_signal', 9)
        macd = self._calculate_macd(closes, macd_fast, macd_slow, macd_signal)
        
        # 布林带计算
        bb_period = params.get('bb_period', 20)
        bb_std = params.get('bb_std', 2.0)
        bb_upper, bb_lower = self._calculate_bollinger_bands(closes, bb_period, bb_std)
        
        # 移动平均线
        sma_period = params.get('sma_period', 20)
        sma = sum(closes[-sma_period:]) / sma_period
        
        return {
            'rsi': rsi,
            'macd': macd,
            'bb_upper': bb_upper,
            'bb_lower': bb_lower,
            'sma': sma,
            'current_price': closes[-1]
        }
    
    def _calculate_rsi(self, prices: List[float], period: int) -> float:
        """计算RSI"""
        if len(prices) < period + 1:
            return 50.0
        
        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _calculate_macd(self, prices: List[float], fast: int, slow: int, signal: int) -> float:
        """计算MACD"""
        if len(prices) < slow:
            return 0.0
        
        ema_fast = self._calculate_ema(prices, fast)
        ema_slow = self._calculate_ema(prices, slow)
        
        macd_line = ema_fast - ema_slow
        return macd_line
    
    def _calculate_ema(self, prices: List[float], period: int) -> float:
        """计算EMA"""
        if len(prices) < period:
            return sum(prices) / len(prices)
        
        multiplier = 2 / (period + 1)
        ema = prices[0]
        
        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))
        
        return ema
    
    def _calculate_bollinger_bands(self, prices: List[float], period: int, std_dev: float) -> Tuple[float, float]:
        """计算布林带"""
        if len(prices) < period:
            return prices[-1], prices[-1]
        
        sma = sum(prices[-period:]) / period
        variance = sum((p - sma) ** 2 for p in prices[-period:]) / period
        std = variance ** 0.5
        
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        
        return upper, lower
    
    def generate_signal(self, indicators: Dict, params: Dict) -> Dict:
        """生成交易信号"""
        if not indicators:
            return {'should_trade': False, 'direction': 'NONE', 'confidence': 0.0}
        
        signal_strength = 0.0
        
        # RSI信号
        rsi_oversold = params.get('rsi_oversold', 30)
        rsi_overbought = params.get('rsi_overbought', 70)
        
        if indicators['rsi'] < rsi_oversold:
            signal_strength += params.get('rsi_weight', 0.3)
        elif indicators['rsi'] > rsi_overbought:
            signal_strength -= params.get('rsi_weight', 0.3)
        
        # MACD信号
        macd_threshold = params.get('macd_threshold', 0)
        if indicators['macd'] > macd_threshold:
            signal_strength += params.get('macd_weight', 0.2)
        else:
            signal_strength -= params.get('macd_weight', 0.2)
        
        # 布林带信号
        current_price = indicators['current_price']
        if current_price > indicators['bb_upper']:
            signal_strength -= params.get('bb_weight', 0.2)
        elif current_price < indicators['bb_lower']:
            signal_strength += params.get('bb_weight', 0.2)
        
        # 移动平均线信号
        if current_price > indicators['sma']:
            signal_strength += params.get('sma_weight', 0.1)
        else:
            signal_strength -= params.get('sma_weight', 0.1)
        
        # 生成最终信号
        signal_threshold = params.get('signal_threshold', 0.5)
        should_trade = abs(signal_strength) > signal_threshold
        direction = 'UP' if signal_strength > 0 else 'DOWN'
        confidence = min(abs(signal_strength), 1.0)
        
        return {
            'should_trade': should_trade,
            'direction': direction,
            'confidence': confidence,
            'signal_strength': signal_strength
        }
    
    def simulate_trading(self, params: Dict) -> OptimizationResult:
        """模拟交易过程"""
        trades = []
        balance = 10000  # 初始资金
        
        for i in range(20, len(self.test_data)):
            # 获取历史数据
            historical_data = self.test_data[:i]
            
            # 计算技术指标
            indicators = self.calculate_technical_indicators(historical_data, params)
            
            # 生成信号
            signal = self.generate_signal(indicators, params)
            
            if signal['should_trade']:
                # 模拟交易
                entry_price = self.test_data[i]['close']
                amount = params.get('base_amount', 20) * signal['confidence']
                
                # 模拟10分钟后的价格（下一根K线）
                if i + 1 < len(self.test_data):
                    exit_price = self.test_data[i + 1]['close']
                    
                    # 判断交易结果
                    if signal['direction'] == 'UP':
                        win = exit_price > entry_price
                    else:
                        win = exit_price < entry_price
                    
                    # 计算盈亏（按币安规则）
                    if win:
                        pnl = amount * 0.8  # 80%收益
                    else:
                        pnl = -amount  # 100%损失
                    
                    balance += pnl
                    
                    trades.append({
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'direction': signal['direction'],
                        'amount': amount,
                        'pnl': pnl,
                        'win': win
                    })
        
        # 计算统计指标
        total_trades = len(trades)
        if total_trades == 0:
            return OptimizationResult(
                parameters=params,
                total_trades=0,
                win_rate=0.0,
                total_pnl=0.0,
                max_drawdown=0.0,
                sharpe_ratio=0.0,
                score=0.0
            )
        
        wins = sum(1 for t in trades if t['win'])
        win_rate = wins / total_trades
        total_pnl = sum(t['pnl'] for t in trades)
        
        # 计算最大回撤
        running_balance = 10000
        peak_balance = 10000
        max_drawdown = 0.0
        
        for trade in trades:
            running_balance += trade['pnl']
            if running_balance > peak_balance:
                peak_balance = running_balance
            drawdown = (peak_balance - running_balance) / peak_balance
            max_drawdown = max(max_drawdown, drawdown)
        
        # 计算夏普比率（简化版）
        pnl_list = [t['pnl'] for t in trades]
        avg_pnl = sum(pnl_list) / len(pnl_list)
        pnl_std = (sum((p - avg_pnl) ** 2 for p in pnl_list) / len(pnl_list)) ** 0.5
        sharpe_ratio = avg_pnl / pnl_std if pnl_std > 0 else 0.0
        
        # 计算综合评分
        score = (win_rate * 0.3 + 
                (total_pnl / 1000) * 0.4 + 
                (1 - max_drawdown) * 0.2 + 
                sharpe_ratio * 0.1)
        
        return OptimizationResult(
            parameters=params,
            total_trades=total_trades,
            win_rate=win_rate,
            total_pnl=total_pnl,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            score=score
        )
    
    def optimize_parameters(self) -> Dict[str, Any]:
        """参数优化主函数"""
        print("🔧 开始策略参数优化...")
        
        # 定义参数搜索空间
        parameter_space = {
            'rsi_period': [10, 14, 20],
            'rsi_oversold': [25, 30, 35],
            'rsi_overbought': [65, 70, 75],
            'rsi_weight': [0.2, 0.3, 0.4],
            'macd_fast': [10, 12, 14],
            'macd_slow': [24, 26, 28],
            'macd_weight': [0.1, 0.2, 0.3],
            'bb_period': [18, 20, 22],
            'bb_std': [1.8, 2.0, 2.2],
            'bb_weight': [0.1, 0.2, 0.3],
            'sma_period': [18, 20, 22],
            'sma_weight': [0.05, 0.1, 0.15],
            'signal_threshold': [0.4, 0.5, 0.6],
            'base_amount': [15, 20, 25]
        }
        
        # 生成参数组合（限制组合数量以避免过长时间）
        param_names = list(parameter_space.keys())
        param_values = list(parameter_space.values())
        
        # 使用网格搜索（限制前5个最重要的参数）
        important_params = ['rsi_period', 'rsi_oversold', 'rsi_overbought', 'signal_threshold', 'base_amount']
        
        best_score = -float('inf')
        best_params = None
        
        print(f"🔍 搜索参数组合...")
        
        # 简化搜索：只优化重要参数
        combinations = list(itertools.product(
            parameter_space['rsi_period'],
            parameter_space['rsi_oversold'],
            parameter_space['rsi_overbought'],
            parameter_space['signal_threshold'],
            parameter_space['base_amount']
        ))
        
        total_combinations = len(combinations)
        print(f"📊 总共需要测试 {total_combinations} 种参数组合")
        
        for i, combo in enumerate(combinations):
            # 构建参数字典
            params = {
                'rsi_period': combo[0],
                'rsi_oversold': combo[1],
                'rsi_overbought': combo[2],
                'signal_threshold': combo[3],
                'base_amount': combo[4],
                # 使用默认值填充其他参数
                'rsi_weight': 0.3,
                'macd_fast': 12,
                'macd_slow': 26,
                'macd_weight': 0.2,
                'bb_period': 20,
                'bb_std': 2.0,
                'bb_weight': 0.2,
                'sma_period': 20,
                'sma_weight': 0.1,
                'macd_threshold': 0,
                'macd_signal': 9
            }
            
            # 运行模拟
            result = self.simulate_trading(params)
            self.optimization_results.append(result)
            
            # 更新最佳参数
            if result.score > best_score:
                best_score = result.score
                best_params = params
            
            # 进度显示
            if (i + 1) % 10 == 0:
                print(f"📈 已完成 {i + 1}/{total_combinations} ({(i + 1)/total_combinations:.1%})")
        
        self.best_parameters = best_params
        
        print(f"\n✅ 参数优化完成！")
        print(f"🏆 最佳评分: {best_score:.4f}")
        
        return best_params
    
    def generate_optimization_report(self) -> str:
        """生成优化报告"""
        if not self.optimization_results:
            return "没有优化结果"
        
        # 按评分排序
        sorted_results = sorted(self.optimization_results, key=lambda x: x.score, reverse=True)
        
        report = "📊 策略参数优化报告\n"
        report += "=" * 50 + "\n\n"
        
        # 最佳结果
        best = sorted_results[0]
        report += f"🏆 最佳参数组合:\n"
        report += f"   评分: {best.score:.4f}\n"
        report += f"   总交易数: {best.total_trades}\n"
        report += f"   胜率: {best.win_rate:.2%}\n"
        report += f"   总盈亏: {best.total_pnl:.2f} USDT\n"
        report += f"   最大回撤: {best.max_drawdown:.2%}\n"
        report += f"   夏普比率: {best.sharpe_ratio:.4f}\n\n"
        
        report += f"🔧 最佳参数:\n"
        for key, value in best.parameters.items():
            report += f"   {key}: {value}\n"
        
        report += f"\n📈 前5名结果:\n"
        for i, result in enumerate(sorted_results[:5]):
            report += f"   {i+1}. 评分: {result.score:.4f}, 胜率: {result.win_rate:.2%}, 盈亏: {result.total_pnl:.2f}\n"
        
        # 统计信息
        all_scores = [r.score for r in self.optimization_results]
        all_win_rates = [r.win_rate for r in self.optimization_results]
        all_pnls = [r.total_pnl for r in self.optimization_results]
        
        report += f"\n📊 统计信息:\n"
        report += f"   平均评分: {sum(all_scores) / len(all_scores):.4f}\n"
        report += f"   平均胜率: {sum(all_win_rates) / len(all_win_rates):.2%}\n"
        report += f"   平均盈亏: {sum(all_pnls) / len(all_pnls):.2f} USDT\n"
        
        return report
    
    def save_optimization_results(self, filepath: str):
        """保存优化结果"""
        results_data = {
            'optimization_time': datetime.now().isoformat(),
            'best_parameters': self.best_parameters,
            'all_results': [
                {
                    'parameters': r.parameters,
                    'total_trades': r.total_trades,
                    'win_rate': r.win_rate,
                    'total_pnl': r.total_pnl,
                    'max_drawdown': r.max_drawdown,
                    'sharpe_ratio': r.sharpe_ratio,
                    'score': r.score
                }
                for r in self.optimization_results
            ]
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 优化结果已保存到: {filepath}")


async def main():
    """主函数"""
    optimizer = StrategyOptimizer()
    
    # 运行参数优化
    best_params = optimizer.optimize_parameters()
    
    # 生成报告
    report = optimizer.generate_optimization_report()
    print("\n" + report)
    
    # 保存结果
    optimizer.save_optimization_results('tools/optimization_results.json')
    
    # 保存报告
    with open('tools/optimization_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("\n🎉 策略参数优化完成！")


if __name__ == "__main__":
    asyncio.run(main()) 