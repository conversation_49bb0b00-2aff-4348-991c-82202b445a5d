"""
文档管理系统
自动生成和维护API文档、技术规范、操作手册等
"""

import os
import ast
import inspect
import json
import time
from typing import Dict, List, Any, Optional, Type, Callable
from dataclasses import dataclass, field
from pathlib import Path
import importlib.util
import subprocess

from config.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class APIDocumentation:
    """API文档数据类"""
    module_name: str
    class_name: Optional[str] = None
    function_name: Optional[str] = None
    docstring: Optional[str] = None
    parameters: List[Dict[str, Any]] = field(default_factory=list)
    return_type: Optional[str] = None
    examples: List[str] = field(default_factory=list)
    source_file: Optional[str] = None
    line_number: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "module_name": self.module_name,
            "class_name": self.class_name,
            "function_name": self.function_name,
            "docstring": self.docstring,
            "parameters": self.parameters,
            "return_type": self.return_type,
            "examples": self.examples,
            "source_file": self.source_file,
            "line_number": self.line_number
        }


class DocstringParser:
    """文档字符串解析器"""
    
    def parse_google_style(self, docstring: str) -> Dict[str, Any]:
        """解析Google风格的文档字符串"""
        if not docstring:
            return {}
        
        lines = docstring.strip().split('\n')
        parsed = {
            "description": "",
            "args": [],
            "returns": "",
            "raises": [],
            "examples": [],
            "notes": []
        }
        
        current_section = "description"
        current_content = []
        
        for line in lines:
            line = line.strip()
            
            # 检查是否是新的段落标题
            if line.endswith(':') and line[:-1] in ['Args', 'Arguments', 'Parameters', 'Returns', 'Return', 'Yields', 'Yield', 'Raises', 'Examples', 'Example', 'Note', 'Notes']:
                # 保存当前段落内容
                if current_content:
                    self._add_section_content(parsed, current_section, current_content)
                
                # 开始新段落
                current_section = line[:-1].lower()
                if current_section in ['arguments', 'parameters']:
                    current_section = 'args'
                elif current_section in ['return', 'yields', 'yield']:
                    current_section = 'returns'
                elif current_section == 'example':
                    current_section = 'examples'
                elif current_section == 'note':
                    current_section = 'notes'
                
                current_content = []
            else:
                current_content.append(line)
        
        # 处理最后一个段落
        if current_content:
            self._add_section_content(parsed, current_section, current_content)
        
        return parsed
    
    def _add_section_content(self, parsed: Dict[str, Any], section: str, content: List[str]) -> None:
        """添加段落内容到解析结果"""
        content_text = '\n'.join(content).strip()
        
        if section == "description":
            parsed["description"] = content_text
        elif section == "args":
            # 解析参数
            args = self._parse_args_section(content)
            parsed["args"].extend(args)
        elif section == "returns":
            parsed["returns"] = content_text
        elif section == "raises":
            # 解析异常
            raises = self._parse_raises_section(content)
            parsed["raises"].extend(raises)
        elif section == "examples":
            parsed["examples"].append(content_text)
        elif section == "notes":
            parsed["notes"].append(content_text)
    
    def _parse_args_section(self, content: List[str]) -> List[Dict[str, Any]]:
        """解析参数段落"""
        args = []
        current_arg = None
        
        for line in content:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否是新参数定义
            if ':' in line and not line.startswith(' '):
                # 保存上一个参数
                if current_arg:
                    args.append(current_arg)
                
                # 解析新参数
                parts = line.split(':', 1)
                param_def = parts[0].strip()
                param_desc = parts[1].strip() if len(parts) > 1 else ""
                
                # 解析参数名和类型
                if '(' in param_def and ')' in param_def:
                    param_name = param_def.split('(')[0].strip()
                    param_type = param_def.split('(')[1].split(')')[0].strip()
                else:
                    param_name = param_def
                    param_type = ""
                
                current_arg = {
                    "name": param_name,
                    "type": param_type,
                    "description": param_desc,
                    "optional": "optional" in param_desc.lower() or "default" in param_desc.lower()
                }
            elif current_arg and line.startswith(' '):
                # 继续当前参数的描述
                current_arg["description"] += " " + line.strip()
        
        # 添加最后一个参数
        if current_arg:
            args.append(current_arg)
        
        return args
    
    def _parse_raises_section(self, content: List[str]) -> List[Dict[str, Any]]:
        """解析异常段落"""
        raises = []
        current_exception = None
        
        for line in content:
            line = line.strip()
            if not line:
                continue
            
            if ':' in line and not line.startswith(' '):
                # 保存上一个异常
                if current_exception:
                    raises.append(current_exception)
                
                # 解析新异常
                parts = line.split(':', 1)
                exception_type = parts[0].strip()
                exception_desc = parts[1].strip() if len(parts) > 1 else ""
                
                current_exception = {
                    "type": exception_type,
                    "description": exception_desc
                }
            elif current_exception and line.startswith(' '):
                # 继续当前异常的描述
                current_exception["description"] += " " + line.strip()
        
        # 添加最后一个异常
        if current_exception:
            raises.append(current_exception)
        
        return raises


class CodeAnalyzer:
    """代码分析器"""
    
    def __init__(self):
        self.docstring_parser = DocstringParser()
    
    def analyze_module(self, module_path: str) -> List[APIDocumentation]:
        """分析模块并提取API文档"""
        docs = []
        
        try:
            # 读取源代码
            with open(module_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # 解析AST
            tree = ast.parse(source_code)
            
            # 获取模块名
            module_name = Path(module_path).stem
            
            # 分析模块级别的文档
            module_docstring = ast.get_docstring(tree)
            if module_docstring:
                parsed_doc = self.docstring_parser.parse_google_style(module_docstring)
                docs.append(APIDocumentation(
                    module_name=module_name,
                    docstring=module_docstring,
                    source_file=module_path,
                    line_number=1
                ))
            
            # 遍历AST节点
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    # 分析类
                    class_docs = self._analyze_class(node, module_name, module_path)
                    docs.extend(class_docs)
                elif isinstance(node, ast.FunctionDef) or isinstance(node, ast.AsyncFunctionDef):
                    # 分析模块级函数
                    if not self._is_nested_function(node, tree):
                        func_doc = self._analyze_function(node, module_name, module_path)
                        if func_doc:
                            docs.append(func_doc)
            
        except Exception as e:
            logger.error(f"分析模块失败: {module_path}, 错误: {e}")
        
        return docs
    
    def _analyze_class(self, class_node: ast.ClassDef, module_name: str, module_path: str) -> List[APIDocumentation]:
        """分析类"""
        docs = []
        
        # 类文档
        class_docstring = ast.get_docstring(class_node)
        if class_docstring:
            parsed_doc = self.docstring_parser.parse_google_style(class_docstring)
            docs.append(APIDocumentation(
                module_name=module_name,
                class_name=class_node.name,
                docstring=class_docstring,
                source_file=module_path,
                line_number=class_node.lineno
            ))
        
        # 分析类方法
        for node in class_node.body:
            if isinstance(node, ast.FunctionDef) or isinstance(node, ast.AsyncFunctionDef):
                method_doc = self._analyze_function(node, module_name, module_path, class_node.name)
                if method_doc:
                    docs.append(method_doc)
        
        return docs
    
    def _analyze_function(self, func_node: ast.FunctionDef, module_name: str, module_path: str, class_name: Optional[str] = None) -> Optional[APIDocumentation]:
        """分析函数或方法"""
        docstring = ast.get_docstring(func_node)
        if not docstring:
            return None
        
        parsed_doc = self.docstring_parser.parse_google_style(docstring)
        
        # 提取参数信息
        parameters = []
        for arg in func_node.args.args:
            param_info = {
                "name": arg.arg,
                "type": self._get_annotation_string(arg.annotation) if arg.annotation else "",
                "description": "",
                "optional": False
            }
            
            # 从文档字符串中查找参数描述
            for doc_arg in parsed_doc.get("args", []):
                if doc_arg["name"] == arg.arg:
                    param_info["description"] = doc_arg["description"]
                    param_info["optional"] = doc_arg["optional"]
                    if not param_info["type"] and doc_arg["type"]:
                        param_info["type"] = doc_arg["type"]
                    break
            
            parameters.append(param_info)
        
        # 处理默认参数
        defaults = func_node.args.defaults
        if defaults:
            default_offset = len(parameters) - len(defaults)
            for i, default in enumerate(defaults):
                param_index = default_offset + i
                if param_index < len(parameters):
                    parameters[param_index]["optional"] = True
        
        # 获取返回类型
        return_type = ""
        if func_node.returns:
            return_type = self._get_annotation_string(func_node.returns)
        
        return APIDocumentation(
            module_name=module_name,
            class_name=class_name,
            function_name=func_node.name,
            docstring=docstring,
            parameters=parameters,
            return_type=return_type,
            examples=parsed_doc.get("examples", []),
            source_file=module_path,
            line_number=func_node.lineno
        )
    
    def _get_annotation_string(self, annotation) -> str:
        """获取类型注解字符串"""
        if annotation is None:
            return ""
        
        try:
            if isinstance(annotation, ast.Name):
                return annotation.id
            elif isinstance(annotation, ast.Constant):
                return str(annotation.value)
            elif isinstance(annotation, ast.Attribute):
                return f"{self._get_annotation_string(annotation.value)}.{annotation.attr}"
            elif isinstance(annotation, ast.Subscript):
                value = self._get_annotation_string(annotation.value)
                slice_value = self._get_annotation_string(annotation.slice)
                return f"{value}[{slice_value}]"
            else:
                return ast.unparse(annotation)
        except Exception:
            return ""
    
    def _is_nested_function(self, func_node: ast.FunctionDef, tree: ast.AST) -> bool:
        """判断是否是嵌套函数"""
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                if node != func_node and func_node in ast.walk(node):
                    return True
        return False


class DocumentationGenerator:
    """文档生成器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.code_analyzer = CodeAnalyzer()
        self.api_docs: List[APIDocumentation] = []
    
    def scan_project(self, include_patterns: List[str] = None, exclude_patterns: List[str] = None) -> None:
        """扫描项目并提取API文档"""
        if include_patterns is None:
            include_patterns = ["*.py"]
        
        if exclude_patterns is None:
            exclude_patterns = [
                "__pycache__",
                ".git",
                "venv",
                "env",
                ".pytest_cache",
                "build",
                "dist",
                "migrations"
            ]
        
        logger.info("开始扫描项目文档...")
        
        for pattern in include_patterns:
            for file_path in self.project_root.rglob(pattern):
                if self._should_skip_file(str(file_path), exclude_patterns):
                    continue
                
                logger.debug(f"分析文件: {file_path}")
                docs = self.code_analyzer.analyze_module(str(file_path))
                self.api_docs.extend(docs)
        
        logger.info(f"扫描完成，共提取 {len(self.api_docs)} 个API文档")
    
    def _should_skip_file(self, file_path: str, exclude_patterns: List[str]) -> bool:
        """判断是否应该跳过文件"""
        return any(pattern in file_path for pattern in exclude_patterns)
    
    def generate_markdown_docs(self, output_dir: str = "docs/api") -> None:
        """生成Markdown格式的API文档"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 按模块分组
        modules = {}
        for doc in self.api_docs:
            if doc.module_name not in modules:
                modules[doc.module_name] = []
            modules[doc.module_name].append(doc)
        
        # 生成每个模块的文档
        for module_name, docs in modules.items():
            self._generate_module_markdown(module_name, docs, output_path)
        
        # 生成索引文件
        self._generate_index_markdown(modules, output_path)
        
        logger.info(f"Markdown文档已生成到: {output_dir}")
    
    def _generate_module_markdown(self, module_name: str, docs: List[APIDocumentation], output_path: Path) -> None:
        """生成单个模块的Markdown文档"""
        file_path = output_path / f"{module_name}.md"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"# {module_name} 模块\n\n")
            
            # 模块级文档
            module_docs = [doc for doc in docs if doc.class_name is None and doc.function_name is None]
            if module_docs:
                f.write("## 模块说明\n\n")
                for doc in module_docs:
                    if doc.docstring:
                        f.write(f"{doc.docstring}\n\n")
            
            # 类文档
            classes = {}
            for doc in docs:
                if doc.class_name and doc.function_name is None:
                    classes[doc.class_name] = doc
            
            if classes:
                f.write("## 类\n\n")
                for class_name, class_doc in classes.items():
                    f.write(f"### {class_name}\n\n")
                    if class_doc.docstring:
                        f.write(f"{class_doc.docstring}\n\n")
                    
                    # 类方法
                    methods = [doc for doc in docs if doc.class_name == class_name and doc.function_name]
                    if methods:
                        f.write("#### 方法\n\n")
                        for method in methods:
                            self._write_function_doc(f, method)
            
            # 模块级函数
            functions = [doc for doc in docs if doc.class_name is None and doc.function_name]
            if functions:
                f.write("## 函数\n\n")
                for func in functions:
                    self._write_function_doc(f, func)
    
    def _write_function_doc(self, f, doc: APIDocumentation) -> None:
        """写入函数文档"""
        func_name = doc.function_name
        f.write(f"##### {func_name}\n\n")
        
        # 函数签名
        params = ", ".join([
            f"{p['name']}: {p['type']}" if p['type'] else p['name']
            for p in doc.parameters
        ])
        return_type = f" -> {doc.return_type}" if doc.return_type else ""
        f.write(f"```python\n{func_name}({params}){return_type}\n```\n\n")
        
        # 描述
        if doc.docstring:
            f.write(f"{doc.docstring}\n\n")
        
        # 参数
        if doc.parameters:
            f.write("**参数:**\n\n")
            for param in doc.parameters:
                optional_text = " (可选)" if param['optional'] else ""
                type_text = f" ({param['type']})" if param['type'] else ""
                f.write(f"- `{param['name']}`{type_text}{optional_text}: {param['description']}\n")
            f.write("\n")
        
        # 返回值
        if doc.return_type:
            f.write(f"**返回值:** {doc.return_type}\n\n")
        
        # 示例
        if doc.examples:
            f.write("**示例:**\n\n")
            for example in doc.examples:
                f.write(f"```python\n{example}\n```\n\n")
        
        f.write("---\n\n")
    
    def _generate_index_markdown(self, modules: Dict[str, List[APIDocumentation]], output_path: Path) -> None:
        """生成索引文档"""
        index_path = output_path / "README.md"
        
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write("# API 文档\n\n")
            f.write("本文档包含项目的完整API参考。\n\n")
            f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 模块列表\n\n")
            for module_name in sorted(modules.keys()):
                docs = modules[module_name]
                class_count = len(set(doc.class_name for doc in docs if doc.class_name))
                function_count = len([doc for doc in docs if doc.function_name])
                
                f.write(f"- [{module_name}]({module_name}.md) - {class_count} 个类, {function_count} 个函数\n")
    
    def generate_html_docs(self, output_dir: str = "docs/html") -> None:
        """生成HTML格式的API文档"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 生成主页
        self._generate_html_index(output_path)
        
        # 按模块分组生成HTML
        modules = {}
        for doc in self.api_docs:
            if doc.module_name not in modules:
                modules[doc.module_name] = []
            modules[doc.module_name].append(doc)
        
        for module_name, docs in modules.items():
            self._generate_module_html(module_name, docs, output_path)
        
        # 复制CSS样式
        self._generate_css(output_path)
        
        logger.info(f"HTML文档已生成到: {output_dir}")
    
    def _generate_html_index(self, output_path: Path) -> None:
        """生成HTML索引页面"""
        index_path = output_path / "index.html"
        
        modules = {}
        for doc in self.api_docs:
            if doc.module_name not in modules:
                modules[doc.module_name] = []
            modules[doc.module_name].append(doc)
        
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>API 文档 - Hertelquant 7.0</title>
            <link rel="stylesheet" href="style.css">
        </head>
        <body>
            <div class="container">
                <header>
                    <h1>API 文档</h1>
                    <p>Hertelquant 7.0 量化交易系统</p>
                </header>
                
                <main>
                    <section class="overview">
                        <h2>概览</h2>
                        <div class="stats">
        """
        
        total_modules = len(modules)
        total_classes = len(set(doc.class_name for doc in self.api_docs if doc.class_name))
        total_functions = len([doc for doc in self.api_docs if doc.function_name])
        
        html += f"""
                            <div class="stat">
                                <span class="number">{total_modules}</span>
                                <span class="label">模块</span>
                            </div>
                            <div class="stat">
                                <span class="number">{total_classes}</span>
                                <span class="label">类</span>
                            </div>
                            <div class="stat">
                                <span class="number">{total_functions}</span>
                                <span class="label">函数</span>
                            </div>
                        </div>
                    </section>
                    
                    <section class="modules">
                        <h2>模块列表</h2>
                        <div class="module-grid">
        """
        
        for module_name in sorted(modules.keys()):
            docs = modules[module_name]
            class_count = len(set(doc.class_name for doc in docs if doc.class_name))
            function_count = len([doc for doc in docs if doc.function_name])
            
            html += f"""
                            <div class="module-card">
                                <h3><a href="{module_name}.html">{module_name}</a></h3>
                                <p>{class_count} 个类, {function_count} 个函数</p>
                            </div>
            """
        
        html += f"""
                        </div>
                    </section>
                </main>
                
                <footer>
                    <p>生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
                </footer>
            </div>
        </body>
        </html>
        """
        
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write(html)
    
    def _generate_module_html(self, module_name: str, docs: List[APIDocumentation], output_path: Path) -> None:
        """生成模块HTML文档"""
        file_path = output_path / f"{module_name}.html"
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{module_name} - API 文档</title>
            <link rel="stylesheet" href="style.css">
        </head>
        <body>
            <div class="container">
                <header>
                    <h1>{module_name} 模块</h1>
                    <nav>
                        <a href="index.html">← 返回首页</a>
                    </nav>
                </header>
                
                <main>
        """
        
        # 模块说明
        module_docs = [doc for doc in docs if doc.class_name is None and doc.function_name is None]
        if module_docs:
            html += "<section class='module-description'><h2>模块说明</h2>"
            for doc in module_docs:
                if doc.docstring:
                    html += f"<p>{self._format_docstring_html(doc.docstring)}</p>"
            html += "</section>"
        
        # 类文档
        classes = {}
        for doc in docs:
            if doc.class_name and doc.function_name is None:
                classes[doc.class_name] = doc
        
        if classes:
            html += "<section class='classes'><h2>类</h2>"
            for class_name, class_doc in classes.items():
                html += f"<div class='class-doc'><h3>{class_name}</h3>"
                if class_doc.docstring:
                    html += f"<p>{self._format_docstring_html(class_doc.docstring)}</p>"
                
                # 类方法
                methods = [doc for doc in docs if doc.class_name == class_name and doc.function_name]
                if methods:
                    html += "<h4>方法</h4>"
                    for method in methods:
                        html += self._format_function_html(method)
                
                html += "</div>"
            html += "</section>"
        
        # 模块级函数
        functions = [doc for doc in docs if doc.class_name is None and doc.function_name]
        if functions:
            html += "<section class='functions'><h2>函数</h2>"
            for func in functions:
                html += self._format_function_html(func)
            html += "</section>"
        
        html += """
                </main>
            </div>
        </body>
        </html>
        """
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html)
    
    def _format_function_html(self, doc: APIDocumentation) -> str:
        """格式化函数HTML文档"""
        func_name = doc.function_name
        
        # 函数签名
        params = ", ".join([
            f"{p['name']}: {p['type']}" if p['type'] else p['name']
            for p in doc.parameters
        ])
        return_type = f" -> {doc.return_type}" if doc.return_type else ""
        signature = f"{func_name}({params}){return_type}"
        
        html = f"""
        <div class="function-doc">
            <h5>{func_name}</h5>
            <pre class="signature"><code>{signature}</code></pre>
        """
        
        if doc.docstring:
            html += f"<p>{self._format_docstring_html(doc.docstring)}</p>"
        
        if doc.parameters:
            html += "<h6>参数:</h6><ul>"
            for param in doc.parameters:
                optional_text = " (可选)" if param['optional'] else ""
                type_text = f" ({param['type']})" if param['type'] else ""
                html += f"<li><code>{param['name']}</code>{type_text}{optional_text}: {param['description']}</li>"
            html += "</ul>"
        
        if doc.return_type:
            html += f"<h6>返回值:</h6><p>{doc.return_type}</p>"
        
        if doc.examples:
            html += "<h6>示例:</h6>"
            for example in doc.examples:
                html += f"<pre><code>{example}</code></pre>"
        
        html += "</div>"
        
        return html
    
    def _format_docstring_html(self, docstring: str) -> str:
        """格式化文档字符串为HTML"""
        # 简单的格式化，将换行转换为<br>
        return docstring.replace('\n', '<br>')
    
    def _generate_css(self, output_path: Path) -> None:
        """生成CSS样式文件"""
        css_path = output_path / "style.css"
        
        css = """
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            border-bottom: 1px solid #eee;
            margin-bottom: 30px;
            padding-bottom: 20px;
        }
        
        header h1 {
            margin: 0;
            color: #2c3e50;
        }
        
        nav a {
            color: #3498db;
            text-decoration: none;
        }
        
        nav a:hover {
            text-decoration: underline;
        }
        
        .stats {
            display: flex;
            gap: 30px;
            margin: 20px 0;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat .number {
            display: block;
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat .label {
            color: #666;
            font-size: 0.9em;
        }
        
        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .module-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #f9f9f9;
        }
        
        .module-card h3 {
            margin: 0 0 10px 0;
        }
        
        .module-card a {
            color: #2c3e50;
            text-decoration: none;
        }
        
        .module-card a:hover {
            color: #3498db;
        }
        
        .function-doc, .class-doc {
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            background: #fafafa;
        }
        
        .signature {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            font-family: 'Monaco', 'Menlo', monospace;
            overflow-x: auto;
        }
        
        code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        
        pre {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            overflow-x: auto;
        }
        
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        h3 {
            color: #34495e;
        }
        
        h5 {
            color: #2c3e50;
            margin: 15px 0 5px 0;
        }
        
        h6 {
            color: #666;
            margin: 10px 0 5px 0;
            font-size: 0.9em;
        }
        
        ul {
            padding-left: 20px;
        }
        
        li {
            margin: 5px 0;
        }
        
        footer {
            border-top: 1px solid #eee;
            margin-top: 40px;
            padding-top: 20px;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }
        """
        
        with open(css_path, 'w', encoding='utf-8') as f:
            f.write(css)
    
    def export_json(self, output_file: str = "docs/api_docs.json") -> None:
        """导出JSON格式的API文档"""
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        docs_data = {
            "generated_at": time.strftime('%Y-%m-%d %H:%M:%S'),
            "total_docs": len(self.api_docs),
            "modules": {}
        }
        
        # 按模块分组
        for doc in self.api_docs:
            if doc.module_name not in docs_data["modules"]:
                docs_data["modules"][doc.module_name] = []
            docs_data["modules"][doc.module_name].append(doc.to_dict())
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(docs_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"JSON文档已导出到: {output_file}")
    
    def generate_changelog(self, output_file: str = "CHANGELOG.md") -> None:
        """生成变更日志模板"""
        changelog_template = """# 变更日志

本文档记录项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
-

### 变更
-

### 修复
-

### 移除
-

## [1.0.0] - 2024-01-01

### 新增
- 初始版本发布
- 基础交易系统功能
- 风险管理模块
- 信号生成器

### 变更
-

### 修复
-

### 移除
-

---

## 版本说明

- **新增** - 新功能
- **变更** - 现有功能的变更
- **弃用** - 即将移除的功能
- **移除** - 已移除的功能
- **修复** - 错误修复
- **安全** - 安全相关的修复
"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(changelog_template)
        
        logger.info(f"变更日志模板已生成: {output_file}")


def main():
    """主函数 - 演示文档生成器的使用"""
    # 创建文档生成器
    doc_gen = DocumentationGenerator()
    
    # 扫描项目
    doc_gen.scan_project(
        include_patterns=["*.py"],
        exclude_patterns=[
            "__pycache__",
            ".git",
            "venv",
            "env",
            ".pytest_cache",
            "build",
            "dist",
            "migrations",
            "tests"  # 可选：排除测试文件
        ]
    )
    
    # 生成不同格式的文档
    doc_gen.generate_markdown_docs("docs/api")
    doc_gen.generate_html_docs("docs/html")
    doc_gen.export_json("docs/api_docs.json")
    doc_gen.generate_changelog("CHANGELOG.md")
    
    print("文档生成完成！")
    print("- Markdown文档: docs/api/")
    print("- HTML文档: docs/html/")
    print("- JSON文档: docs/api_docs.json")
    print("- 变更日志: CHANGELOG.md")


if __name__ == "__main__":
    main()
        