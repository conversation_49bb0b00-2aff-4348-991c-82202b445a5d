#!/usr/bin/env python3
"""
网络连接调试脚本
用于诊断币安API连接问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
import traceback
from quant.platform.binance_spot import BinanceSpotRestApi
from quant.utils.http_client import HttpRequests
from quant import const

def test_direct_requests():
    """测试直接使用requests库"""
    print("=== 测试直接requests调用 ===")
    try:
        import requests
        
        # 测试基本连接
        print("1. 测试基本连接...")
        response = requests.get("https://api.binance.com/api/v3/time", timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        
        # 测试K线API
        print("2. 测试K线API...")
        params = {
            'symbol': 'BTCUSDT',
            'interval': '1m',
            'limit': 1
        }
        response = requests.get("https://api.binance.com/api/v3/klines", params=params, timeout=10)
        print(f"   状态码: {response.status_code}")
        data = response.json()
        print(f"   K线数据: {data[0] if data else 'None'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 直接requests调用失败: {e}")
        print(f"   错误详情: {traceback.format_exc()}")
        return False

def test_http_client():
    """测试HttpRequests类"""
    print("\n=== 测试HttpRequests类 ===")
    try:
        # 测试基本连接
        print("1. 测试基本连接...")
        result, error = HttpRequests.get("https://api.binance.com/api/v3/time", timeout=10)
        if error:
            print(f"   ❌ 错误: {error}")
            return False
        else:
            print(f"   ✅ 成功: {result}")
        
        # 测试K线API
        print("2. 测试K线API...")
        params = {
            'symbol': 'BTCUSDT',
            'interval': '1m',
            'limit': 1
        }
        result, error = HttpRequests.get("https://api.binance.com/api/v3/klines", params=params, timeout=10)
        if error:
            print(f"   ❌ 错误: {error}")
            return False
        else:
            print(f"   ✅ 成功: {result[0] if result else 'None'}")
            
        return True
        
    except Exception as e:
        print(f"   ❌ HttpRequests调用失败: {e}")
        print(f"   错误详情: {traceback.format_exc()}")
        return False

def test_binance_api():
    """测试BinanceSpotRestApi类"""
    print("\n=== 测试BinanceSpotRestApi类 ===")
    try:
        # 加载配置
        with open('scripts/config.json', 'r') as f:
            config = json.load(f)
        
        # 创建API客户端
        api = BinanceSpotRestApi(
            access_key=config['PLATFORMS']['binance']['access_key'],
            secret_key=config['PLATFORMS']['binance']['secret_key']
        )
        
        print("1. 测试get_kline方法...")
        success, error = api.get_kline("BTC/USDT", const.KLINE_1M)
        
        if error:
            print(f"   ❌ 错误: {error}")
            print(f"   错误类型: {type(error)}")
            return False
        else:
            print(f"   ✅ 成功获取 {len(success)} 根K线")
            if success:
                latest = success[-1]
                print(f"   最新K线: 时间={latest[0]}, 收盘价={latest[4]}")
            
        return True
        
    except Exception as e:
        print(f"   ❌ BinanceSpotRestApi调用失败: {e}")
        print(f"   错误详情: {traceback.format_exc()}")
        return False

def test_main_strategy_simulation():
    """模拟主策略中的确切调用"""
    print("\n=== 模拟主策略调用 ===")
    try:
        # 加载配置（模拟主策略的配置加载）
        from quant.config import config
        config.loads('scripts/config.json')

        # 创建API客户端（完全模拟主策略）
        api = BinanceSpotRestApi(
            access_key=config.platforms['binance']['access_key'],
            secret_key=config.platforms['binance']['secret_key']
        )

        print("1. 模拟_get_current_btc_price调用...")
        success, error = api.get_kline("BTC/USDT", const.KLINE_1M)

        if success and len(success) > 0:
            latest_kline = success[-1]
            current_price = float(latest_kline[4])  # 收盘价
            print(f"   ✅ 成功获取BTC价格: {current_price:.2f} USDT")
            return True
        else:
            print(f"   ❌ 获取BTC价格失败: {error}")
            return False

    except Exception as e:
        print(f"   ❌ 主策略模拟调用失败: {e}")
        print(f"   错误详情: {traceback.format_exc()}")
        return False

def test_async_simulation():
    """测试异步环境下的调用"""
    print("\n=== 测试异步环境调用 ===")
    try:
        import asyncio

        async def async_test():
            # 加载配置
            from quant.config import config
            config.loads('scripts/config.json')

            # 创建API客户端
            api = BinanceSpotRestApi(
                access_key=config.platforms['binance']['access_key'],
                secret_key=config.platforms['binance']['secret_key']
            )

            print("1. 异步环境中测试API调用...")
            success, error = api.get_kline("BTC/USDT", const.KLINE_1M)

            if success and len(success) > 0:
                latest_kline = success[-1]
                current_price = float(latest_kline[4])
                print(f"   ✅ 异步环境成功: {current_price:.2f} USDT")
                return True
            else:
                print(f"   ❌ 异步环境失败: {error}")
                return False

        # 运行异步测试
        result = asyncio.run(async_test())
        return result

    except Exception as e:
        print(f"   ❌ 异步测试失败: {e}")
        print(f"   错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🔍 开始网络连接诊断...")

    # 测试1: 直接requests
    test1_ok = test_direct_requests()

    # 测试2: HttpRequests类
    test2_ok = test_http_client()

    # 测试3: BinanceSpotRestApi类
    test3_ok = test_binance_api()

    # 测试4: 主策略模拟
    test4_ok = test_main_strategy_simulation()

    # 测试5: 异步环境测试
    test5_ok = test_async_simulation()

    print("\n" + "="*50)
    print("📊 诊断结果汇总:")
    print(f"   直接requests调用: {'✅ 正常' if test1_ok else '❌ 异常'}")
    print(f"   HttpRequests类: {'✅ 正常' if test2_ok else '❌ 异常'}")
    print(f"   BinanceSpotRestApi类: {'✅ 正常' if test3_ok else '❌ 异常'}")
    print(f"   主策略模拟调用: {'✅ 正常' if test4_ok else '❌ 异常'}")
    print(f"   异步环境测试: {'✅ 正常' if test5_ok else '❌ 异常'}")

    if all([test1_ok, test2_ok, test3_ok, test4_ok, test5_ok]):
        print("\n🎉 所有测试通过！网络连接正常。")
        print("💡 问题可能出现在特定的运行时环境或并发情况下。")
        print("💡 建议检查主策略的运行日志和错误发生的具体时间。")
    else:
        print("\n⚠️ 发现网络连接问题，请根据上述错误信息进行排查。")

if __name__ == "__main__":
    main()
