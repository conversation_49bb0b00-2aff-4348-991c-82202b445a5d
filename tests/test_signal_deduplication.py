#!/usr/bin/env python3
"""
择时信号去重系统测试

测试信号去重机制和频率控制的有效性
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier, NotificationConfig


class SignalDeduplicationTester:
    """信号去重测试器"""
    
    def __init__(self):
        # 配置测试用的通知器
        self.config = NotificationConfig(
            enable_signal_notification=True,
            enable_signal_deduplication=True,
            min_recommendation_interval=300,  # 5分钟间隔
            similarity_threshold=0.8,
            dedup_time_window=1800,  # 30分钟窗口
            max_daily_notifications=10
        )
        self.notifier = EventContractDingtalkNotifier(self.config)
        
    def test_duplicate_detection(self):
        """测试重复信号检测"""
        print("🔍 测试重复信号检测")
        print("=" * 50)
        
        # 创建相似的推荐
        base_recommendation = {
            'direction': 'UP',
            'confidence': 75.0,
            'stake': 20.0,
            'score': 45.0,
            'remaining_time': 300,
            'generated_at': datetime.now().isoformat()
        }
        
        # 测试完全相同的推荐
        duplicate_recommendation = base_recommendation.copy()
        
        # 测试相似的推荐
        similar_recommendation = base_recommendation.copy()
        similar_recommendation['confidence'] = 76.0  # 微小差异
        similar_recommendation['stake'] = 21.0
        
        # 测试不同的推荐
        different_recommendation = {
            'direction': 'DOWN',
            'confidence': 60.0,
            'stake': 25.0,
            'score': 35.0,
            'remaining_time': 400,
            'generated_at': datetime.now().isoformat()
        }
        
        print("1️⃣ 发送基础推荐...")
        success1, error1 = self.notifier.send_recommendation(base_recommendation)
        print(f"   结果: {'✅ 成功' if success1 else '❌ 失败'} - {error1 or '正常发送'}")
        
        print("\n2️⃣ 发送完全相同的推荐...")
        success2, error2 = self.notifier.send_recommendation(duplicate_recommendation)
        print(f"   结果: {'✅ 成功' if success2 else '❌ 失败'} - {error2 or '应该被去重'}")
        
        print("\n3️⃣ 发送相似的推荐...")
        success3, error3 = self.notifier.send_recommendation(similar_recommendation)
        print(f"   结果: {'✅ 成功' if success3 else '❌ 失败'} - {error3 or '应该被去重'}")
        
        print("\n4️⃣ 发送不同的推荐...")
        success4, error4 = self.notifier.send_recommendation(different_recommendation)
        print(f"   结果: {'✅ 成功' if success4 else '❌ 失败'} - {error4 or '应该正常发送'}")
        
        # 验证去重效果
        expected_sends = 2  # 基础推荐 + 不同推荐
        actual_sends = sum([success1, success2, success3, success4])
        
        print(f"\n📊 去重效果:")
        print(f"   预期发送: {expected_sends} 次")
        print(f"   实际发送: {actual_sends} 次")
        print(f"   去重效果: {'✅ 正常' if actual_sends == expected_sends else '❌ 异常'}")
        
        return actual_sends == expected_sends
    
    def test_frequency_control(self):
        """测试频率控制"""
        print("\n🕒 测试频率控制")
        print("=" * 50)
        
        recommendation = {
            'direction': 'UP',
            'confidence': 80.0,
            'stake': 20.0,
            'score': 50.0,
            'remaining_time': 400,
            'generated_at': datetime.now().isoformat()
        }
        
        results = []
        
        print("1️⃣ 快速连续发送5个推荐...")
        for i in range(5):
            # 稍微修改推荐内容避免去重
            test_rec = recommendation.copy()
            test_rec['confidence'] = 80.0 + i * 5  # 不同置信度
            test_rec['generated_at'] = datetime.now().isoformat()
            
            success, error = self.notifier.send_recommendation(test_rec)
            results.append(success)
            
            print(f"   推荐 {i+1}: {'✅ 成功' if success else '❌ 失败'} - {error or '正常'}")
            
            # 短暂延迟
            time.sleep(1)
        
        successful_sends = sum(results)
        print(f"\n📊 频率控制效果:")
        print(f"   尝试发送: 5 次")
        print(f"   成功发送: {successful_sends} 次")
        print(f"   频率控制: {'✅ 正常' if successful_sends <= 2 else '❌ 异常'}")
        
        return successful_sends <= 2
    
    def test_similarity_calculation(self):
        """测试相似度计算"""
        print("\n🔢 测试相似度计算")
        print("=" * 50)
        
        base_rec = {
            'direction': 'UP',
            'confidence': 75.0,
            'stake': 20.0,
            'score': 45.0
        }
        
        test_cases = [
            # (测试推荐, 预期相似度范围, 描述)
            ({'direction': 'UP', 'confidence': 75.0, 'stake': 20.0, 'score': 45.0}, (0.95, 1.0), "完全相同"),
            ({'direction': 'UP', 'confidence': 76.0, 'stake': 21.0, 'score': 46.0}, (0.85, 0.95), "微小差异"),
            ({'direction': 'UP', 'confidence': 80.0, 'stake': 25.0, 'score': 50.0}, (0.7, 0.85), "中等差异"),
            ({'direction': 'DOWN', 'confidence': 75.0, 'stake': 20.0, 'score': 45.0}, (0.0, 0.1), "方向不同"),
            ({'direction': 'UP', 'confidence': 50.0, 'stake': 10.0, 'score': 20.0}, (0.3, 0.6), "较大差异"),
        ]
        
        all_passed = True
        
        for i, (test_rec, expected_range, description) in enumerate(test_cases, 1):
            similarity = self.notifier._calculate_recommendation_similarity(base_rec, test_rec)
            min_expected, max_expected = expected_range
            passed = min_expected <= similarity <= max_expected
            
            print(f"{i}️⃣ {description}:")
            print(f"   相似度: {similarity:.3f}")
            print(f"   预期范围: {min_expected:.3f} - {max_expected:.3f}")
            print(f"   结果: {'✅ 通过' if passed else '❌ 失败'}")
            
            if not passed:
                all_passed = False
        
        print(f"\n📊 相似度计算测试: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")
        return all_passed
    
    def test_time_window_cleanup(self):
        """测试时间窗口清理"""
        print("\n🧹 测试时间窗口清理")
        print("=" * 50)
        
        # 添加一些旧的信号记录
        old_time = datetime.now() - timedelta(seconds=2000)  # 超过30分钟窗口
        recent_time = datetime.now() - timedelta(seconds=300)  # 5分钟前
        
        self.notifier.recent_signals = [
            {
                'type': 'recommendation',
                'timestamp': old_time,
                'data': {'direction': 'UP', 'confidence': 70.0},
                'fingerprint': 'UP_70.0_20'
            },
            {
                'type': 'recommendation', 
                'timestamp': recent_time,
                'data': {'direction': 'DOWN', 'confidence': 80.0},
                'fingerprint': 'DOWN_80.0_25'
            }
        ]
        
        print(f"清理前信号数量: {len(self.notifier.recent_signals)}")
        
        # 触发清理
        self.notifier._cleanup_old_signals(datetime.now())
        
        print(f"清理后信号数量: {len(self.notifier.recent_signals)}")
        
        # 验证只保留了最近的信号
        remaining_recent = sum(1 for signal in self.notifier.recent_signals 
                             if signal['timestamp'] > datetime.now() - timedelta(seconds=1800))
        
        print(f"30分钟内信号数量: {remaining_recent}")
        
        cleanup_success = len(self.notifier.recent_signals) == 1 and remaining_recent == 1
        print(f"清理效果: {'✅ 正常' if cleanup_success else '❌ 异常'}")
        
        return cleanup_success
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 择时信号去重系统测试")
        print("=" * 60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        test_results = []
        
        # 运行各项测试
        test_results.append(("重复信号检测", self.test_duplicate_detection()))
        test_results.append(("频率控制", self.test_frequency_control()))
        test_results.append(("相似度计算", self.test_similarity_calculation()))
        test_results.append(("时间窗口清理", self.test_time_window_cleanup()))
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("📋 测试结果汇总")
        print("=" * 60)
        
        passed_count = 0
        for test_name, passed in test_results:
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"{test_name}: {status}")
            if passed:
                passed_count += 1
        
        total_tests = len(test_results)
        print(f"\n总体结果: {passed_count}/{total_tests} 项测试通过")
        
        if passed_count == total_tests:
            print("🎉 所有测试通过！信号去重系统工作正常")
        else:
            print("⚠️ 部分测试失败，需要检查相关功能")
        
        return passed_count == total_tests


def main():
    """主函数"""
    tester = SignalDeduplicationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n💡 优化建议:")
        print("1. 择时信号推送频率已优化到5分钟间隔")
        print("2. 相似信号去重机制已启用，避免重复推送")
        print("3. 30分钟时间窗口内的信号会被去重检查")
        print("4. 每日通知数量限制为50次，避免过度推送")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
