#!/usr/bin/env python3
"""
钉钉通知集成演示
展示如何在真实的交易决策中集成钉钉通知
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from unittest.mock import patch
from quant.strategies.event_contract_decision_engine import (
    EventContractDecisionEngine,
    RiskManagementConfig,
    TradingDecision,
    RiskLevel,
    MarketCondition
)
from quant.strategies.event_contract_dingtalk_notifier import (
    EventContractDingtalkNotifier,
    NotificationConfig
)
from quant.strategies.event_contract_signal_generator_simple import SignalResult

def demo_dingtalk_integration():
    """演示钉钉通知集成"""
    print("🚀 钉钉通知集成演示")
    print("=" * 60)
    
    # 1. 初始化决策引擎
    risk_config = RiskManagementConfig(
        base_bet_amount=25.0,
        daily_soft_limit=500.0,
        daily_hard_limit=1000.0
    )
    decision_engine = EventContractDecisionEngine(risk_config)
    
    # 2. 初始化钉钉通知器
    notification_config = NotificationConfig(
        enable_signal_notification=True,
        enable_settlement_notification=True,
        enable_risk_notification=True,
        min_signal_interval=10,
        max_daily_notifications=20
    )
    notifier = EventContractDingtalkNotifier(notification_config)
    
    print(f"💼 系统初始化完成")
    print(f"   📊 基础投注: {risk_config.base_bet_amount} USDT")
    print(f"   ⚠️ 风险限制: {risk_config.daily_soft_limit} USDT")
    print(f"   📱 通知配置: 已启用所有通知类型")
    print()
    
    # 使用Mock替代真实的钉钉发送
    with patch('quant.strategies.event_contract_dingtalk_notifier.Dingtalk.markdown') as mock_dingtalk:
        mock_dingtalk.return_value = ({"errcode": 0, "errmsg": "ok"}, None)
        
        # 3. 模拟多种交易场景
        scenarios = [
            {
                'name': '🚀 强势上涨信号',
                'signal': SignalResult(
                    has_signal=True,
                    direction="UP",
                    confidence=92.0,
                    technical_score=88.0,
                    market_status="强势上涨",
                    user_reminder="RSI超卖反弹，MACD金叉确认"
                ),
                'market_data': {
                    'price': 95500.0,
                    'volume': 1500000,
                    'volatility': 0.04,
                    'trend_strength': 0.85
                },
                'expected_result': 'win'
            },
            {
                'name': '📉 谨慎下跌信号',
                'signal': SignalResult(
                    has_signal=True,
                    direction="DOWN",
                    confidence=78.0,
                    technical_score=72.0,
                    market_status="震荡下跌",
                    user_reminder="支撑位破位，成交量放大"
                ),
                'market_data': {
                    'price': 94200.0,
                    'volume': 1100000,
                    'volatility': 0.03,
                    'trend_strength': 0.65
                },
                'expected_result': 'loss'
            },
            {
                'name': '⚡ 高波动突破',
                'signal': SignalResult(
                    has_signal=True,
                    direction="UP",
                    confidence=95.0,
                    technical_score=92.0,
                    market_status="高波动突破",
                    user_reminder="关键阻力位突破，成交量激增"
                ),
                'market_data': {
                    'price': 96800.0,
                    'volume': 2200000,
                    'volatility': 0.12,
                    'trend_strength': 0.92
                },
                'expected_result': 'win'
            }
        ]
        
        executed_trades = []
        
        # 4. 处理每个场景
        for i, scenario in enumerate(scenarios, 1):
            print(f"🎯 场景 {i}: {scenario['name']}")
            print(f"   📊 信号: {scenario['signal'].direction} - {scenario['signal'].confidence:.1f}%")
            print(f"   💰 当前价格: {scenario['market_data']['price']:.0f} USDT")
            print(f"   📈 波动率: {scenario['market_data']['volatility']:.2%}")
            print()
            
            # 生成交易决策
            decision = decision_engine.make_trading_decision(
                signal_result=scenario['signal'],
                current_balance=5000.0,
                market_data=scenario['market_data']
            )
            
            print(f"   🎲 决策结果:")
            print(f"     - 执行交易: {'✅ 是' if decision.should_trade else '❌ 否'}")
            print(f"     - 投注金额: {decision.bet_amount:.2f} USDT")
            print(f"     - 风险等级: {decision.risk_level.value}")
            print(f"     - 市场条件: {decision.market_condition.value}")
            print(f"     - 仓位比例: {decision.position_size_ratio:.2%}")
            print()
            
            if decision.should_trade:
                # 1. 发送交易信号通知
                print(f"   📢 发送交易信号通知...")
                success, error = notifier.send_trading_signal(
                    decision=decision,
                    signal_result=scenario['signal'],
                    market_data=scenario['market_data']
                )
                
                if success:
                    print(f"   ✅ 交易信号通知发送成功")
                    print(f"   💬 通知包含关键词: 交易 ✓, 小火箭 ✓")
                else:
                    print(f"   ❌ 交易信号通知发送失败: {error}")
                
                # 2. 模拟下单
                order_id = f"order_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i}"
                decision_engine.record_trade(
                    decision=decision,
                    order_id=order_id,
                    executed_price=scenario['market_data']['price'],
                    executed_amount=decision.bet_amount
                )
                
                print(f"   📝 订单已提交: {order_id}")
                
                # 3. 模拟结算结果
                expected_result = scenario['expected_result']
                pnl = decision.bet_amount * 0.9 if expected_result == 'win' else -decision.bet_amount
                
                decision_engine.update_trade_result(order_id, expected_result, pnl)
                
                print(f"   📊 模拟结算: {'🎉 盈利' if expected_result == 'win' else '😔 亏损'}")
                print(f"   💰 盈亏金额: {pnl:+.2f} USDT")
                
                # 4. 发送结算通知
                print(f"   📢 发送结算通知...")
                success, error = notifier.send_settlement_notification(
                    order_id=order_id,
                    result=expected_result,
                    pnl=pnl,
                    decision=decision
                )
                
                if success:
                    print(f"   ✅ 结算通知发送成功")
                else:
                    print(f"   ❌ 结算通知发送失败: {error}")
                
                executed_trades.append({
                    'decision': decision,
                    'result': expected_result,
                    'pnl': pnl
                })
                
                # 5. 检查风险状态
                should_stop, reason = decision_engine.should_stop_trading()
                if should_stop:
                    print(f"   ⚠️ 风险警告: {reason}")
                    
                    # 发送风险提醒
                    print(f"   📢 发送风险提醒...")
                    risk_summary = decision_engine.get_risk_summary()
                    success, error = notifier.send_risk_alert(
                        risk_type="交易风险",
                        message=reason,
                        current_loss=risk_summary['daily_loss'],
                        loss_ratio=risk_summary['daily_loss_ratio']
                    )
                    
                    if success:
                        print(f"   ✅ 风险提醒发送成功")
                    else:
                        print(f"   ❌ 风险提醒发送失败: {error}")
                    
                    if should_stop:
                        print(f"   ⏹️ 系统建议停止交易")
                        break
            
            print("-" * 60)
        
        # 6. 发送每日总结
        print(f"📊 发送每日交易总结")
        stats = decision_engine.get_daily_stats()
        risk_summary = decision_engine.get_risk_summary()
        
        print(f"   📈 今日统计:")
        print(f"     - 总交易数: {stats['total_trades']}")
        print(f"     - 胜率: {stats['win_rate']:.1%}")
        print(f"     - 总盈亏: {stats['total_pnl']:+.2f} USDT")
        print(f"     - 连胜/连败: {stats['current_streak']}")
        print(f"     - 当日损失: {risk_summary['daily_loss']:.2f} USDT")
        print()
        
        success, error = notifier.send_daily_summary(stats, risk_summary)
        
        if success:
            print(f"   ✅ 每日总结发送成功")
        else:
            print(f"   ❌ 每日总结发送失败: {error}")
        
        # 7. 显示钉钉调用统计
        print(f"\n📱 钉钉通知统计:")
        print(f"   总调用次数: {mock_dingtalk.call_count}")
        
        notification_stats = notifier.get_notification_stats()
        print(f"   通知统计: {notification_stats['types_today']}")
        print(f"   剩余配额: {notification_stats['remaining_quota']}")
        
        # 8. 显示发送的消息样本
        print(f"\n💬 消息样本预览:")
        if executed_trades:
            sample_decision = executed_trades[0]['decision']
            sample_signal = scenarios[0]['signal']
            sample_market = scenarios[0]['market_data']
            
            print(f"   📝 交易信号消息样本:")
            sample_message = notifier._build_signal_message(
                sample_decision, sample_signal, sample_market
            )
            print("   " + "="*50)
            for line in sample_message.split('\n')[:8]:  # 显示前8行
                print(f"   {line}")
            print("   ... (更多内容)")
            print("   " + "="*50)
        
        return mock_dingtalk.call_count

def demo_notification_keywords():
    """演示钉钉通知关键词"""
    print("\n🔍 钉钉通知关键词检查")
    print("=" * 60)
    
    notifier = EventContractDingtalkNotifier()
    
    # 测试信号
    signal = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=90.0,
        technical_score=85.0,
        market_status="强势上涨",
        user_reminder="突破关键阻力位"
    )
    
    # 测试决策
    decision = TradingDecision(
        should_trade=True,
        bet_amount=30.0,
        direction="UP",
        confidence=90.0,
        risk_level=RiskLevel.LOW,
        market_condition=MarketCondition.TRENDING,
        reason="满足交易条件：信心度90.0%，风险low",
        timestamp=datetime.now(),
        max_loss_allowed=1000.0,
        current_daily_loss=0.0,
        position_size_ratio=0.03,
        market_score=85.0,
        signal_strength=90.0,
        entry_timing="优秀"
    )
    
    market_data = {
        'price': 96500.0,
        'volume': 1800000,
        'volatility': 0.05
    }
    
    # 检查各类消息的关键词
    messages = {
        '交易信号': notifier._build_signal_message(decision, signal, market_data),
        '结算通知': notifier._build_settlement_message("test_order", "win", 27.0, decision),
        '风险提醒': notifier._build_risk_alert_message("高风险", "测试风险", 200.0, 0.2),
        '每日总结': notifier._build_daily_summary_message(
            {'total_trades': 5, 'win_rate': 0.8, 'total_pnl': 150.0, 'current_streak': 3},
            {'daily_loss': 50.0, 'daily_loss_ratio': 0.1}
        )
    }
    
    required_keywords = ['交易']
    bonus_keywords = ['🚀', '小火箭']
    
    print(f"📋 检查必需关键词: {required_keywords}")
    print(f"📋 检查特殊关键词: {bonus_keywords}")
    print()
    
    for msg_type, message in messages.items():
        print(f"📝 {msg_type}:")
        
        # 检查必需关键词
        for keyword in required_keywords:
            has_keyword = keyword in message
            print(f"   - '{keyword}': {'✅' if has_keyword else '❌'}")
        
        # 检查特殊关键词
        for keyword in bonus_keywords:
            has_keyword = keyword in message
            print(f"   - '{keyword}': {'✅' if has_keyword else '⚪'}")
        
        # 统计关键词总数
        total_keywords = sum(1 for kw in required_keywords + bonus_keywords if kw in message)
        print(f"   关键词总数: {total_keywords}/{len(required_keywords + bonus_keywords)}")
        print()

def main():
    """主函数"""
    try:
        print("🎯 钉钉通知集成完整演示")
        print("=" * 70)
        
        # 演示完整的钉钉集成
        dingtalk_calls = demo_dingtalk_integration()
        
        # 演示关键词检查
        demo_notification_keywords()
        
        print(f"\n🎉 演示完成！")
        print(f"✅ 钉钉通知集成功能已完成")
        print(f"✅ 所有消息都包含必需的关键词")
        print(f"✅ 支持交易信号、结算通知、风险提醒、每日总结")
        print(f"✅ 具备完整的频率限制和统计功能")
        print(f"📊 本次演示共模拟发送 {dingtalk_calls} 条钉钉消息")
        
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()