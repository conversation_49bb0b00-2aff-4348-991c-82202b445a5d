#!/usr/bin/env python3
"""
钉钉通知器测试脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
from unittest.mock import patch, MagicMock
from quant.strategies.event_contract_dingtalk_notifier import (
    EventContractDingtalkNotifier,
    NotificationConfig
)
from quant.strategies.event_contract_decision_engine import (
    TradingDecision,
    RiskLevel,
    MarketCondition
)
from quant.strategies.event_contract_signal_generator_simple import SignalResult

def test_notification_messages():
    """测试通知消息构建"""
    print("=== 测试通知消息构建 ===")
    
    # 创建通知器
    config = NotificationConfig(
        enable_signal_notification=True,
        enable_settlement_notification=True,
        enable_risk_notification=True,
        dingtalk_token="test_token"
    )
    
    notifier = EventContractDingtalkNotifier(config)
    
    # 创建测试数据
    signal_result = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=88.0,
        technical_score=82.0,
        market_status="正常",
        user_reminder="强势上涨信号"
    )
    
    decision = TradingDecision(
        should_trade=True,
        bet_amount=25.0,
        direction="UP",
        confidence=88.0,
        risk_level=RiskLevel.LOW,
        market_condition=MarketCondition.TRENDING,
        reason="满足交易条件：信心度88.0%，风险low",
        timestamp=datetime.now(),
        max_loss_allowed=1000.0,
        current_daily_loss=0.0,
        position_size_ratio=0.025,
        market_score=82.0,
        signal_strength=88.0,
        entry_timing="良好"
    )
    
    market_data = {
        'price': 95000.0,
        'volume': 1200000,
        'volatility': 0.03
    }
    
    # 测试交易信号消息
    print("1️⃣ 交易信号消息:")
    signal_message = notifier._build_signal_message(decision, signal_result, market_data)
    print(signal_message)
    print()
    
    # 测试结算消息
    print("2️⃣ 结算通知消息:")
    settlement_message = notifier._build_settlement_message(
        order_id="test_order_001",
        result="win",
        pnl=22.5,
        decision=decision
    )
    print(settlement_message)
    print()
    
    # 测试风险提醒消息
    print("3️⃣ 风险提醒消息:")
    risk_message = notifier._build_risk_alert_message(
        risk_type="损失超限",
        alert_message="当日损失已达到50%软限制",
        current_loss=500.0,
        loss_ratio=0.5
    )
    print(risk_message)
    print()
    
    # 测试每日总结消息
    print("4️⃣ 每日总结消息:")
    stats = {
        'total_trades': 8,
        'win_rate': 0.625,
        'total_pnl': 156.5,
        'avg_bet': 22.5,
        'current_streak': 3
    }
    
    risk_summary = {
        'daily_loss': 85.0,
        'daily_loss_ratio': 0.085,
        'remaining_soft_limit': 915.0
    }
    
    daily_message = notifier._build_daily_summary_message(stats, risk_summary)
    print(daily_message)
    print()

def test_notification_flow():
    """测试通知流程"""
    print("=== 测试通知流程 ===")
    
    # 使用mock替代真实的钉钉发送
    with patch('quant.strategies.event_contract_dingtalk_notifier.Dingtalk.markdown') as mock_dingtalk:
        mock_dingtalk.return_value = ({"errcode": 0, "errmsg": "ok"}, None)
        
        # 创建通知器
        notifier = EventContractDingtalkNotifier()
        
        # 测试数据
        signal_result = SignalResult(
            has_signal=True,
            direction="DOWN",
            confidence=92.0,
            technical_score=88.0,
            market_status="高波动",
            user_reminder="高波动突破信号"
        )
        
        decision = TradingDecision(
            should_trade=True,
            bet_amount=30.0,
            direction="DOWN",
            confidence=92.0,
            risk_level=RiskLevel.MEDIUM,
            market_condition=MarketCondition.VOLATILE,
            reason="满足交易条件：信心度92.0%，风险medium",
            timestamp=datetime.now(),
            max_loss_allowed=950.0,
            current_daily_loss=50.0,
            position_size_ratio=0.03,
            market_score=88.0,
            signal_strength=92.0,
            entry_timing="良好"
        )
        
        market_data = {
            'price': 93500.0,
            'volume': 1500000,
            'volatility': 0.08
        }
        
        # 1. 测试交易信号通知
        print("1️⃣ 发送交易信号通知")
        success, error = notifier.send_trading_signal(decision, signal_result, market_data)
        print(f"   发送结果: {'成功' if success else '失败'}")
        if error:
            print(f"   错误信息: {error}")
        print(f"   调用次数: {mock_dingtalk.call_count}")
        
        # 2. 测试结算通知
        print("2️⃣ 发送结算通知")
        success, error = notifier.send_settlement_notification(
            order_id="test_order_002",
            result="win",
            pnl=27.0,
            decision=decision
        )
        print(f"   发送结果: {'成功' if success else '失败'}")
        if error:
            print(f"   错误信息: {error}")
        print(f"   调用次数: {mock_dingtalk.call_count}")
        
        # 3. 测试风险提醒
        print("3️⃣ 发送风险提醒")
        success, error = notifier.send_risk_alert(
            risk_type="连续亏损",
            message="连续3次亏损，请检查策略",
            current_loss=150.0,
            loss_ratio=0.15
        )
        print(f"   发送结果: {'成功' if success else '失败'}")
        if error:
            print(f"   错误信息: {error}")
        print(f"   调用次数: {mock_dingtalk.call_count}")
        
        # 4. 测试每日总结
        print("4️⃣ 发送每日总结")
        stats = {
            'total_trades': 12,
            'win_rate': 0.75,
            'total_pnl': 285.5,
            'avg_bet': 25.0,
            'current_streak': 2
        }
        
        risk_summary = {
            'daily_loss': 125.0,
            'daily_loss_ratio': 0.125,
            'remaining_soft_limit': 875.0
        }
        
        success, error = notifier.send_daily_summary(stats, risk_summary)
        print(f"   发送结果: {'成功' if success else '失败'}")
        if error:
            print(f"   错误信息: {error}")
        print(f"   总调用次数: {mock_dingtalk.call_count}")
        
        # 5. 检查通知统计
        print("5️⃣ 通知统计")
        notification_stats = notifier.get_notification_stats()
        print(f"   今日通知总数: {notification_stats['total_today']}")
        print(f"   剩余配额: {notification_stats['remaining_quota']}")
        print(f"   各类型通知数量: {notification_stats['types_today']}")

def test_notification_limits():
    """测试通知频率限制"""
    print("\n=== 测试通知频率限制 ===")
    
    # 创建严格限制的配置
    config = NotificationConfig(
        min_signal_interval=10,  # 10秒间隔
        max_daily_notifications=3  # 每日最多3条
    )
    
    with patch('quant.strategies.event_contract_dingtalk_notifier.Dingtalk.markdown') as mock_dingtalk:
        mock_dingtalk.return_value = ({"errcode": 0, "errmsg": "ok"}, None)
        
        notifier = EventContractDingtalkNotifier(config)
        
        # 测试数据
        signal_result = SignalResult(
            has_signal=True,
            direction="UP",
            confidence=85.0,
            technical_score=80.0,
            market_status="正常",
            user_reminder="测试信号"
        )
        
        decision = TradingDecision(
            should_trade=True,
            bet_amount=20.0,
            direction="UP",
            confidence=85.0,
            risk_level=RiskLevel.LOW,
            market_condition=MarketCondition.TRENDING,
            reason="测试决策",
            timestamp=datetime.now(),
            max_loss_allowed=1000.0,
            current_daily_loss=0.0,
            position_size_ratio=0.02,
            market_score=80.0,
            signal_strength=85.0,
            entry_timing="良好"
        )
        
        # 快速发送多个通知
        print("1️⃣ 快速发送多个通知（测试每日限制）")
        for i in range(5):
            success, error = notifier.send_trading_signal(decision, signal_result)
            print(f"   第{i+1}次发送: {'成功' if success else '失败'}")
            if error:
                print(f"   错误: {error}")
        
        print(f"   实际调用次数: {mock_dingtalk.call_count}")
        
        # 检查统计
        stats = notifier.get_notification_stats()
        print(f"   今日通知数: {stats['total_today']}")
        print(f"   剩余配额: {stats['remaining_quota']}")

def test_message_keywords():
    """测试消息关键词"""
    print("\n=== 测试消息关键词 ===")
    
    notifier = EventContractDingtalkNotifier()
    
    # 测试各种消息是否包含必要关键词
    signal_result = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=90.0,
        technical_score=85.0,
        market_status="强势",
        user_reminder="强势突破信号"
    )
    
    decision = TradingDecision(
        should_trade=True,
        bet_amount=35.0,
        direction="UP",
        confidence=90.0,
        risk_level=RiskLevel.LOW,
        market_condition=MarketCondition.TRENDING,
        reason="满足交易条件：信心度90.0%，风险low",
        timestamp=datetime.now(),
        max_loss_allowed=1000.0,
        current_daily_loss=0.0,
        position_size_ratio=0.035,
        market_score=85.0,
        signal_strength=90.0,
        entry_timing="优秀"
    )
    
    # 检查交易信号消息
    signal_message = notifier._build_signal_message(decision, signal_result)
    
    print("1️⃣ 检查交易信号消息关键词")
    print(f"   包含'交易': {'交易' in signal_message}")
    print(f"   包含'🚀': {'🚀' in signal_message}")
    print(f"   包含'小火箭': {'小火箭' in signal_message}")
    
    # 检查结算消息
    settlement_message = notifier._build_settlement_message(
        order_id="test_order_003",
        result="win",
        pnl=31.5,
        decision=decision
    )
    
    print("2️⃣ 检查结算消息关键词")
    print(f"   包含'交易': {'交易' in settlement_message}")
    
    # 检查风险提醒消息
    risk_message = notifier._build_risk_alert_message(
        risk_type="高风险",
        alert_message="风险等级过高",
        current_loss=200.0,
        loss_ratio=0.2
    )
    
    print("3️⃣ 检查风险提醒消息关键词")
    print(f"   包含'交易': {'交易' in risk_message}")

def main():
    """主函数"""
    print("钉钉通知器测试")
    print("=" * 50)
    
    try:
        test_notification_messages()
        test_notification_flow()
        test_notification_limits()
        test_message_keywords()
        
        print("\n✅ 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()