#!/usr/bin/env python3
"""
BTC/USDT价格极值预测器Web服务器
提供实时数据推送和Web界面

Author: HertelQuant Enhanced
Date: 2025-06-21
"""

import json
import threading
import time
import sys
import numpy as np
import pandas as pd
import os
import webbrowser
from datetime import datetime, timedelta
from collections import deque
from typing import Dict, List, Tuple, Optional
from flask import Flask, render_template, jsonify, request, send_file
from flask_socketio import SocketIO, emit
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor

# 导入现有的币安客户端
try:
    from enhanced_binance_client import get_binance_client, BinanceAPIError
    BINANCE_CLIENT_AVAILABLE = True
    print("✅ 币安客户端导入成功")
except ImportError as e:
    print(f"⚠️ 币安客户端导入失败: {e}")
    BINANCE_CLIENT_AVAILABLE = False

# DingTalk integration imports
try:
    from quant.config import config
    from quant.utils.dingtalk import Dingtalk
    DINGTALK_AVAILABLE = True
    print("✅ DingTalk模块导入成功")
except ImportError as e:
    print(f"⚠️ DingTalk模块导入失败: {e}")
    print("⚠️ DingTalk通知功能将被禁用")
    DINGTALK_AVAILABLE = False

# 设置日志级别，避免Flask的调试信息干扰
logging.getLogger('werkzeug').setLevel(logging.WARNING)

def convert_to_json_serializable(obj):
    """将对象转换为JSON可序列化的格式"""
    try:
        if isinstance(obj, dict):
            return {k: convert_to_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return [convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, bool):
            return obj  # Python布尔值是JSON可序列化的
        elif obj is None:
            return None
        elif isinstance(obj, (int, float, str)):
            return obj
        elif hasattr(obj, 'isoformat'):  # datetime对象
            return obj.isoformat()
        elif pd.isna(obj):
            return None
        else:
            # 对于其他类型，尝试转换为字符串
            return str(obj)
    except Exception as e:
        print(f"⚠️ JSON序列化警告: {e}, 对象类型: {type(obj)}")
        return str(obj) if obj is not None else None


class RealTimeDataManager:
    """实时数据管理器 - 获取真实币安数据"""
    
    def __init__(self):
        self.binance_client = None
        self.is_running = False
        self.current_price = 0.0
        self.last_kline_data = {}
        self.price_history = deque(maxlen=500)  # 保留最近500个价格点
        self.kline_history = deque(maxlen=100)  # 保留最近100根K线
        self.last_update_time = None
        self.update_interval = 30  # 30秒更新一次
        self.lock = threading.Lock()
        
        # 技术指标计算器
        self.indicators_calculator = None
        
        # 初始化币安客户端
        if BINANCE_CLIENT_AVAILABLE:
            try:
                self.binance_client = get_binance_client()
                print("✅ 实时数据管理器：币安客户端初始化成功")
            except Exception as e:
                print(f"❌ 实时数据管理器：币安客户端初始化失败: {e}")
        else:
            print("⚠️ 实时数据管理器：币安客户端不可用，将使用模拟数据")
    
    def start_real_time_updates(self):
        """启动实时数据更新线程"""
        if self.is_running:
            return
        
        self.is_running = True
        thread = threading.Thread(target=self._update_loop, daemon=True)
        thread.start()
        print("🚀 实时数据更新线程已启动")
    
    def stop_real_time_updates(self):
        """停止实时数据更新"""
        self.is_running = False
        print("⏹️ 实时数据更新已停止")
    
    def _update_loop(self):
        """数据更新循环"""
        while self.is_running:
            try:
                # 获取实时数据
                self._fetch_real_time_data()
                time.sleep(self.update_interval)
            except Exception as e:
                print(f"❌ 实时数据更新失败: {e}")
                time.sleep(self.update_interval)
    
    def _fetch_real_time_data(self):
        """获取实时数据"""
        try:
            if self.binance_client:
                # 获取真实的币安数据
                ticker_data = self._get_binance_ticker()
                kline_data = self._get_binance_kline()
                
                if ticker_data and kline_data:
                    self._update_data(ticker_data, kline_data)
                    print(f"📊 实时数据更新: BTC价格 ${self.current_price:,.2f}")
            else:
                # 使用模拟数据
                self._generate_mock_data()
                
        except Exception as e:
            print(f"❌ 获取实时数据失败: {e}")
            # 回退到模拟数据
            self._generate_mock_data()
    
    def _get_binance_ticker(self):
        """获取币安现货价格"""
        try:
            klines_data = self.binance_client.get_klines_with_retry("BTCUSDT", "1m", 1)
            if klines_data and isinstance(klines_data, list) and len(klines_data) > 0:
                # 币安API返回的是K线列表，每个元素是一个列表
                # [开盘时间, 开盘价, 最高价, 最低价, 收盘价, 成交量, 收盘时间, ...]
                kline = klines_data[0]
                return {
                    'symbol': 'BTCUSDT',
                    'price': float(kline[4]),  # 收盘价
                    'timestamp': int(kline[6])  # 收盘时间
                }
        except Exception as e:
            print(f"❌ 获取币安Ticker失败: {e}")
        return None
    
    def _get_binance_kline(self):
        """获取币安K线数据"""
        try:
            klines_data = self.binance_client.get_klines_with_retry("BTCUSDT", "1m", 1)
            if klines_data and isinstance(klines_data, list) and len(klines_data) > 0:
                # 币安API返回的是K线列表
                # [开盘时间, 开盘价, 最高价, 最低价, 收盘价, 成交量, 收盘时间, ...]
                latest_kline = klines_data[0]
                return {
                    'open': float(latest_kline[1]),
                    'high': float(latest_kline[2]),
                    'low': float(latest_kline[3]),
                    'close': float(latest_kline[4]),
                    'volume': float(latest_kline[5]),
                    'timestamp': int(latest_kline[6])
                }
        except Exception as e:
            print(f"❌ 获取币安K线失败: {e}")
        return None
    
    def _generate_mock_data(self):
        """生成模拟数据（当币安API不可用时）"""
        try:
            # 基于当前价格生成波动
            if self.current_price == 0:
                self.current_price = 95000.0  # 初始价格
            
            # 模拟价格波动 (-0.5% 到 +0.5%)
            change_pct = (np.random.random() - 0.5) * 0.01  # -0.5% to +0.5%
            new_price = self.current_price * (1 + change_pct)
            
            # 模拟K线数据
            mock_kline = {
                'open': self.current_price,
                'high': max(self.current_price, new_price) * (1 + np.random.random() * 0.001),
                'low': min(self.current_price, new_price) * (1 - np.random.random() * 0.001),
                'close': new_price,
                'volume': 1000 + np.random.random() * 500,
                'timestamp': int(time.time() * 1000)
            }
            
            mock_ticker = {
                'symbol': 'BTCUSDT',
                'price': new_price,
                'timestamp': int(time.time() * 1000)
            }
            
            self._update_data(mock_ticker, mock_kline)
            print(f"📊 模拟数据更新: BTC价格 ${self.current_price:,.2f} (模拟)")
            
        except Exception as e:
            print(f"❌ 生成模拟数据失败: {e}")
    
    def _update_data(self, ticker_data, kline_data):
        """更新数据"""
        with self.lock:
            # 更新当前价格
            self.current_price = ticker_data['price']
            self.last_update_time = datetime.now()
            
            # 更新价格历史
            self.price_history.append({
                'price': self.current_price,
                'timestamp': ticker_data['timestamp']
            })
            
            # 更新K线历史
            self.kline_history.append(kline_data)
            self.last_kline_data = kline_data
    
    def get_current_data(self):
        """获取当前数据"""
        with self.lock:
            return {
                'current_price': self.current_price,
                'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None,
                'kline_data': self.last_kline_data,
                'price_history': list(self.price_history)[-50:],  # 最近50个价格点
                'is_real_data': self.binance_client is not None,
                'data_source': '币安API' if self.binance_client else '模拟数据'
            }
    
    def calculate_technical_indicators(self):
        """计算技术指标"""
        if len(self.kline_history) < 50:
            return self._get_default_indicators()
        
        try:
            # 转换为列表格式进行计算
            closes = [k['close'] for k in list(self.kline_history)]
            highs = [k['high'] for k in list(self.kline_history)]
            lows = [k['low'] for k in list(self.kline_history)]
            volumes = [k['volume'] for k in list(self.kline_history)]
            
            # 使用pandas计算技术指标
            df = pd.DataFrame({
                'close': closes,
                'high': highs,
                'low': lows,
                'volume': volumes
            })
            
            # 计算各种技术指标
            indicators = self._calculate_indicators_from_df(df)
            return indicators
            
        except Exception as e:
            print(f"❌ 计算技术指标失败: {e}")
            return self._get_default_indicators()
    
    def _calculate_indicators_from_df(self, df):
        """从DataFrame计算技术指标"""
        try:
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            # EMA
            ema_5 = df['close'].ewm(span=5).mean()
            ema_10 = df['close'].ewm(span=10).mean()
            ema_20 = df['close'].ewm(span=20).mean()
            ema_50 = df['close'].ewm(span=50).mean()
            
            # 布林带
            bb_period = 20
            bb_std = 2
            sma = df['close'].rolling(window=bb_period).mean()
            std = df['close'].rolling(window=bb_period).std()
            bb_upper = sma + (std * bb_std)
            bb_lower = sma - (std * bb_std)
            bb_position = (df['close'] - bb_lower) / (bb_upper - bb_lower)
            
            # MACD
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            macd_line = ema_12 - ema_26
            macd_signal = macd_line.ewm(span=9).mean()
            macd_histogram = macd_line - macd_signal
            
            # 成交量比率
            volume_ratio = df['volume'].iloc[-1] / df['volume'].rolling(window=20).mean().iloc[-1]
            
            # 波动率
            volatility = df['close'].pct_change().rolling(window=20).std() * np.sqrt(20)
            
            return {
                'rsi': float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else 50,
                'ema_5': float(ema_5.iloc[-1]) if not pd.isna(ema_5.iloc[-1]) else df['close'].iloc[-1],
                'ema_10': float(ema_10.iloc[-1]) if not pd.isna(ema_10.iloc[-1]) else df['close'].iloc[-1],
                'ema_20': float(ema_20.iloc[-1]) if not pd.isna(ema_20.iloc[-1]) else df['close'].iloc[-1],
                'ema_50': float(ema_50.iloc[-1]) if not pd.isna(ema_50.iloc[-1]) else df['close'].iloc[-1],
                'bb_position': float(bb_position.iloc[-1]) if not pd.isna(bb_position.iloc[-1]) else 0.5,
                'bb_upper': float(bb_upper.iloc[-1]) if not pd.isna(bb_upper.iloc[-1]) else df['close'].iloc[-1] * 1.02,
                'bb_lower': float(bb_lower.iloc[-1]) if not pd.isna(bb_lower.iloc[-1]) else df['close'].iloc[-1] * 0.98,
                'macd_line': float(macd_line.iloc[-1]) if not pd.isna(macd_line.iloc[-1]) else 0,
                'macd_signal': float(macd_signal.iloc[-1]) if not pd.isna(macd_signal.iloc[-1]) else 0,
                'macd_histogram': float(macd_histogram.iloc[-1]) if not pd.isna(macd_histogram.iloc[-1]) else 0,
                'volume_ratio': float(volume_ratio) if not pd.isna(volume_ratio) else 1.0,
                'volatility': float(volatility.iloc[-1]) if not pd.isna(volatility.iloc[-1]) else 0.5,
                'current_price': self.current_price
            }
            
        except Exception as e:
            print(f"❌ DataFrame指标计算失败: {e}")
            return self._get_default_indicators()
    
    def _get_default_indicators(self):
        """获取默认技术指标（当计算失败时）"""
        return {
            'rsi': 50,
            'ema_5': self.current_price,
            'ema_10': self.current_price,
            'ema_20': self.current_price,
            'ema_50': self.current_price,
            'bb_position': 0.5,
            'bb_upper': self.current_price * 1.02,
            'bb_lower': self.current_price * 0.98,
            'macd_line': 0,
            'macd_signal': 0,
            'macd_histogram': 0,
            'volume_ratio': 1.0,
            'volatility': 0.5,
            'current_price': self.current_price
        }


def send_dingtalk_trading_signal(signal_data: Dict, current_price: float, trade_tracker=None) -> bool:
    """
    发送交易信号到DingTalk群组

    Args:
        signal_data: 交易信号数据
        current_price: 当前BTC价格

    Returns:
        bool: 发送是否成功
    """
    if not DINGTALK_AVAILABLE or not config.dingtalk:
        print("⚠️ DingTalk功能不可用或未配置")
        return False

    try:
        # 获取信号信息
        direction = signal_data.get('direction', 'UNKNOWN')
        confidence = signal_data.get('confidence', 0)
        signal_strength = signal_data.get('signal_strength', 'UNKNOWN')
        suggested_amount = signal_data.get('suggested_amount', 0)
        supporting_indicators = signal_data.get('supporting_indicators', [])
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 方向图标和描述
        direction_icon = "🚀" if direction == "UP" else "📉" if direction == "DOWN" else "❓"
        direction_text = "看涨" if direction == "UP" else "看跌" if direction == "DOWN" else "未知"

        # 获取上一个信号结果（新增功能）
        last_signal_info = {'display_text': '首次信号', 'details': '这是系统生成的第一个交易信号'}
        if trade_tracker:
            try:
                last_signal_info = trade_tracker.get_last_signal_details()
            except Exception as e:
                print(f"⚠️ 获取上一个信号结果失败: {e}")

        # 上一个信号结果的图标
        last_result = last_signal_info.get('result', 'FIRST_SIGNAL')
        if last_result == 'WIN':
            last_signal_icon = "✅"
        elif last_result == 'LOSS':
            last_signal_icon = "❌"
        elif last_result == 'PENDING':
            last_signal_icon = "⏳"
        else:
            last_signal_icon = "🆕"

        # 构建消息内容（包含必需的关键词：交易、小火箭）
        content = f"""### 🚀 小火箭交易信号（多头抑制修改测试）

**📊 交易信号详情:**

> **🎯 信号方向:** {direction_icon} {direction_text} ({direction})

> **📈 置信度:** {confidence}%

> **⚡ 信号强度:** {signal_strength}

> **💰 当前BTC价格:** ${current_price:,.2f}

> **💵 建议交易金额:** ${suggested_amount}

> **🔍 技术指标支撑:** {', '.join(supporting_indicators) if supporting_indicators else '无'}

> **📋 上次信号结果:** {last_signal_icon} {last_signal_info.get('display_text', '首次信号')}

> **⏰ 信号生成时间:** {timestamp}

---
🚀 **小火箭交易提醒:**
- 本信号基于技术分析生成
- 请结合自身风险承受能力进行交易决策
- 建议设置止损止盈点位
- 急剧下跌时多头不开单

💡 **交易风险提示:** 数字货币交易存在高风险，请谨慎投资！"""

        # 发送DingTalk消息
        success, error = Dingtalk.markdown(content)

        if success:
            print(f"✅ DingTalk交易信号通知发送成功: {direction} | 置信度: {confidence}%")
            return True
        else:
            print(f"❌ DingTalk交易信号通知发送失败: {error}")
            return False

    except Exception as e:
        print(f"❌ 发送DingTalk交易信号通知时出错: {e}")
        return False





class EventContractSignalGenerator:
    """币安事件合约信号生成器 - 优化版本"""

    def __init__(self, trade_tracker=None):
        self.last_signal_time = {}  # 记录每个方向的最后信号时间
        self.signal_history = deque(maxlen=100)  # 信号历史记录
        self.min_signal_interval = 5 * 60  # 最小信号间隔（优化：从10分钟降至5分钟）
        self._trade_tracker_ref = trade_tracker  # 交易跟踪器引用，用于历史数据分析

        # "画地为牢"交易哲学参数设置
        self.quality_threshold = 60  # 信号质量阈值（优化：从65分降至60分）
        self.min_timeframes = 2      # 最少时间框架确认（保持2个）
        self.confidence_threshold = 85  # 置信度阈值（优化：从90%降至85%）
        self.probability_threshold = 80  # 概率阈值（优化：从85%降至80%）

        # "画地为牢"策略核心参数
        self.favorable_position_threshold = 75  # 有利位置阈值
        self.risk_control_enabled = True  # 风险控制开关
        self.philosophy_mode = "画地为牢"  # 交易哲学模式

        # 新增15分钟K线聚合和六面骰子概率模型
        self.minute_klines = deque(maxlen=15)  # 1分钟K线数据
        self.fifteen_min_klines = deque(maxlen=100)  # 15分钟K线数据
        self.minute_probability_history = {}  # 逐分钟概率分析历史数据

        # 六面骰子概率模型参数
        self.dice_model_params = {
            'probability_weights': {
                1: 1.0,  # 超卖区域做多
                2: 1.0,  # 关键支撑位做多
                3: 1.0,  # 移动均线支撑做多
                4: 1.0,  # 移动均线阻力做空
                5: 1.0,  # 关键阻力位做空
                6: 1.0   # 超买区域做空
            },
            'long_faces': [1, 2, 3],
            'short_faces': [4, 5, 6]
        }

    def generate_signal(self, multi_analysis: Dict, indicators: Dict) -> Dict:
        """
        生成事件合约交易信号

        Args:
            multi_analysis: 多时间周期分析结果
            indicators: 技术指标数据

        Returns:
            交易信号字典
        """
        current_time = datetime.now()

        # 检查是否有有效的多时间周期分析
        if not multi_analysis or 'multi_timeframe_analysis' not in multi_analysis:
            return self._create_no_signal_response("缺少多时间周期分析数据")

        timeframe_data = multi_analysis['multi_timeframe_analysis']
        summary = multi_analysis.get('summary', {})

        # 主信号条件检查
        signal_direction = None
        signal_strength = "WEAK"
        confidence = 0
        supporting_indicators = []

        # 检查各时间周期的信号
        valid_signals = []
        for timeframe in ['5min', '10min', '15min', '30min']:
            if timeframe in timeframe_data:
                tf_data = timeframe_data[timeframe]
                high_prob = tf_data.get('high_probability', 0)
                low_prob = tf_data.get('low_probability', 0)
                tf_confidence = tf_data.get('confidence', 0)

                # 检查是否满足主信号条件（优化：进一步降低要求以增加信号频率）
                if high_prob >= self.probability_threshold and tf_confidence >= self.confidence_threshold:
                    valid_signals.append(('DOWN', high_prob, tf_confidence, timeframe))
                elif low_prob >= self.probability_threshold and tf_confidence >= self.confidence_threshold:
                    valid_signals.append(('UP', low_prob, tf_confidence, timeframe))

        # 优化多周期确认机制：降低要求至少2个时间周期方向一致（提升信号频率）
        if len(valid_signals) >= self.min_timeframes:
            # 检查方向一致性和信号强度
            directions = [signal[0] for signal in valid_signals]
            up_signals = [s for s in valid_signals if s[0] == 'UP']
            down_signals = [s for s in valid_signals if s[0] == 'DOWN']

            if len(up_signals) >= 1:  # 进一步降低要求，只需1个强信号即可
                # 优化验证：放宽信号强度要求以增加频率
                avg_prob = sum([s[1] for s in up_signals]) / len(up_signals)
                min_prob = min([s[1] for s in up_signals])
                if avg_prob >= 87 and min_prob >= 85:  # 优化：平均概率≥87%，最低概率≥85%
                    signal_direction = 'UP'
                    confidence = sum([s[2] for s in up_signals]) / len(up_signals)

            elif len(down_signals) >= 1:  # 进一步降低要求，只需1个强信号即可
                # 优化验证：放宽信号强度要求以增加频率
                avg_prob = sum([s[1] for s in down_signals]) / len(down_signals)
                min_prob = min([s[1] for s in down_signals])
                if avg_prob >= 87 and min_prob >= 85:  # 优化：平均概率≥87%，最低概率≥85%
                    signal_direction = 'DOWN'
                    confidence = sum([s[2] for s in down_signals]) / len(down_signals)

        # 强化技术指标过滤（基于回测结果优化）
        if signal_direction:
            rsi = indicators.get('rsi', 50)
            macd_histogram = indicators.get('macd_histogram', 0)
            bb_position = indicators.get('bb_position', 0.5)
            wrsi = indicators.get('wrsi', 50)  # 加权RSI
            volume_ratio = indicators.get('volume_ratio', 1.0)
            current_price = indicators.get('current_price', 0)  # 获取当前价格

            if signal_direction == 'UP':
                # 改进看涨信号逻辑 - 多重确认机制

                # 🚨 CRITICAL: 实施优先级阻断检查系统
                # 优先级1: 市场崩盘检测 (最高优先级)
                market_crash = indicators.get('market_crash', {})
                if market_crash.get('is_market_crash', False):
                    signal_direction = None
                    return self._create_no_signal_response(
                        f"🚨 市场崩盘期间禁止开多仓: {market_crash.get('crash_level', 'unknown')}"
                    )

                # 优先级2: 急剧下跌检测 (仅在无崩盘时检查)
                sharp_downturn = indicators.get('sharp_downturn', {})
                if sharp_downturn.get('block_long_positions', False):
                    signal_direction = None  # 立即阻断多头信号
                    return self._create_no_signal_response(
                        f"🚨 急剧下跌期间禁止开多仓: {'; '.join(sharp_downturn.get('downturn_signals', []))}"
                    )

                # 💥 CRITICAL: 市场崩盘阻断检查 - 最高优先级
                market_crash = indicators.get('market_crash', {})
                if market_crash.get('is_market_crash', False):
                    import time
                    current_time = time.time()
                    suspend_until = market_crash.get('suspend_long_until', 0)

                    if suspend_until and current_time < suspend_until:
                        remaining_hours = (suspend_until - current_time) / 3600
                        signal_direction = None  # 立即阻断多头信号
                        return self._create_no_signal_response(
                            f"💥 市场崩盘期间禁止开多仓: {market_crash.get('crash_level', '未知')} "
                            f"(剩余冷却时间: {remaining_hours:.1f}小时)"
                        )

                # 🔍 多时间框架看跌共识检查
                bearish_timeframes = []
                bearish_consensus_strength = 0

                for timeframe in ['5min', '10min', '15min', '30min']:
                    if timeframe in timeframe_data:
                        tf_data = timeframe_data[timeframe]
                        high_prob = tf_data.get('high_probability', 0)
                        confidence = tf_data.get('confidence', 0)

                        # 检查该时间框架是否显示看跌信号
                        if high_prob >= 85 and confidence >= 85:  # 强看跌信号
                            bearish_timeframes.append(f"{timeframe}(强势)")
                            bearish_consensus_strength += 2
                        elif high_prob >= 80 and confidence >= 80:  # 中等看跌信号
                            bearish_timeframes.append(f"{timeframe}(中等)")
                            bearish_consensus_strength += 1

                # 如果3个或以上时间框架显示看跌，阻断多头信号
                if len(bearish_timeframes) >= 3 or bearish_consensus_strength >= 5:
                    signal_direction = None  # 阻断多头信号
                    return self._create_no_signal_response(
                        f"📉 多时间框架看跌共识，禁止开多仓: {', '.join(bearish_timeframes)}"
                    )

                # 🛡️ 强势看跌趋势阻断检查
                trend_confirmation = indicators.get('trend_confirmation', {})
                if trend_confirmation.get('is_trend_confirmed', False):
                    trend_direction = trend_confirmation.get('trend_direction', 'neutral')
                    trend_strength = trend_confirmation.get('trend_strength', 0)

                    # 强势看跌趋势时完全阻断多头信号
                    if trend_direction in ['strong_bearish', 'bearish'] and trend_strength >= 50:
                        signal_direction = None  # 阻断多头信号
                        return self._create_no_signal_response(
                            f"🛡️ 强势看跌趋势期间禁止开多仓: 趋势强度{trend_strength}%"
                        )

                    # 中等看跌趋势时需要额外确认
                    elif trend_direction == 'bearish' and trend_strength >= 30:
                        # 需要额外的支撑确认才能开多仓
                        support_resistance = indicators.get('support_resistance', {})
                        if not support_resistance.get('near_support', False):
                            signal_direction = None  # 阻断多头信号
                            return self._create_no_signal_response(
                                f"⚠️ 看跌趋势中需要强支撑位确认才能开多仓"
                            )

                # 1. RSI超卖确认
                if rsi < 25:  # 严格的超卖条件
                    supporting_indicators.append("RSI_oversold")

                # 2. 布林带位置确认
                if bb_position < 0.3:
                    supporting_indicators.append("BB_oversold")

                # 3. 支撑位确认（新增）
                support_resistance = indicators.get('support_resistance', {})
                if support_resistance.get('near_support', False):
                    supporting_indicators.append("Near_support")
                elif support_resistance.get('approaching_support', False):
                    supporting_indicators.append("Approaching_support")

                # 4. 斐波那契支撑确认（新增）
                fibonacci_levels = indicators.get('fibonacci_levels', {})
                if fibonacci_levels:
                    fib_50 = fibonacci_levels.get('fib_50.0', 0)
                    fib_618 = fibonacci_levels.get('fib_61.8', 0)
                    if abs(current_price - fib_618) / current_price < 0.005:  # 接近61.8%回调位
                        supporting_indicators.append("Fib_618_support")
                    elif abs(current_price - fib_50) / current_price < 0.005:  # 接近50%回调位
                        supporting_indicators.append("Fib_50_support")

                # 5. 趋势确认（新增） - 仅在通过阻断检查后添加支撑指标
                if trend_confirmation.get('is_trend_confirmed', False):
                    trend_direction = trend_confirmation.get('trend_direction', 'neutral')
                    if trend_direction in ['bullish', 'strong_bullish']:
                        supporting_indicators.append("Bullish_trend_confirmed")
                    elif trend_direction == 'neutral' and trend_confirmation.get('trend_strength', 0) > 20:
                        supporting_indicators.append("Neutral_trend_support")

                # 6. EMA支撑确认
                if current_price > indicators.get('ema_20', current_price):
                    supporting_indicators.append("EMA_support")

                # 7. 成交量确认（新增）
                if volume_ratio > 1.2:  # 成交量放大
                    supporting_indicators.append("Volume_confirmation")

                # 8. MACD确认（新增）
                macd_histogram = indicators.get('macd_histogram', 0)
                if macd_histogram > 0 or (macd_histogram > -0.01 and macd_histogram < 0):  # MACD转正或接近转正
                    supporting_indicators.append("MACD_support")

                # 多重确认要求：至少需要3个支撑指标（提高信号质量）
                if len(supporting_indicators) >= 3:
                    signal_strength = "STRONG" if len(supporting_indicators) >= 5 else "MEDIUM"
                else:
                    signal_direction = None  # 取消信号

            elif signal_direction == 'DOWN':
                # 改进看跌信号逻辑 - 多重确认机制

                # 1. RSI超买确认
                if rsi > 75:  # 严格的超买条件
                    supporting_indicators.append("RSI_overbought")

                # 2. 布林带位置确认
                if bb_position > 0.7:
                    supporting_indicators.append("BB_overbought")

                # 3. 阻力位确认（新增）
                support_resistance = indicators.get('support_resistance', {})
                if support_resistance.get('near_resistance', False):
                    supporting_indicators.append("Near_resistance")
                elif support_resistance.get('approaching_resistance', False):
                    supporting_indicators.append("Approaching_resistance")

                # 4. 斐波那契阻力确认（新增）
                fibonacci_levels = indicators.get('fibonacci_levels', {})
                if fibonacci_levels:
                    fib_236 = fibonacci_levels.get('fib_23.6', 0)
                    fib_382 = fibonacci_levels.get('fib_38.2', 0)
                    if abs(current_price - fib_236) / current_price < 0.005:  # 接近23.6%回调位
                        supporting_indicators.append("Fib_236_resistance")
                    elif abs(current_price - fib_382) / current_price < 0.005:  # 接近38.2%回调位
                        supporting_indicators.append("Fib_382_resistance")

                # 5. 趋势确认（新增）
                trend_confirmation = indicators.get('trend_confirmation', {})
                if trend_confirmation.get('is_trend_confirmed', False):
                    trend_direction = trend_confirmation.get('trend_direction', 'neutral')
                    if trend_direction in ['bearish', 'strong_bearish']:
                        supporting_indicators.append("Bearish_trend_confirmed")
                    elif trend_direction == 'neutral' and trend_confirmation.get('trend_strength', 0) > 20:
                        supporting_indicators.append("Neutral_trend_resistance")

                # 6. EMA阻力确认
                if current_price < indicators.get('ema_20', current_price):
                    supporting_indicators.append("EMA_resistance")

                # 7. 成交量确认（新增）
                if volume_ratio > 1.2:  # 成交量放大
                    supporting_indicators.append("Volume_confirmation")

                # 8. MACD确认（新增）
                macd_histogram = indicators.get('macd_histogram', 0)
                if macd_histogram < 0 or (macd_histogram < 0.01 and macd_histogram > 0):  # MACD转负或接近转负
                    supporting_indicators.append("MACD_resistance")

                # 多重确认要求：至少需要3个支撑指标（提高信号质量）
                if len(supporting_indicators) >= 3:
                    signal_strength = "STRONG" if len(supporting_indicators) >= 5 else "MEDIUM"
                else:
                    signal_direction = None  # 取消信号

        # 信号频率控制
        if signal_direction:
            last_signal_key = f"last_{signal_direction.lower()}_signal"
            if last_signal_key in self.last_signal_time:
                time_diff = (current_time - self.last_signal_time[last_signal_key]).total_seconds()
                if time_diff < self.min_signal_interval:
                    return self._create_no_signal_response(f"信号间隔不足，需等待{int((self.min_signal_interval - time_diff) / 60)}分钟")

        # 生成信号
        if signal_direction:
            signal_id = f"{signal_direction}_{int(current_time.timestamp())}"
            valid_until = current_time + timedelta(minutes=10)

            # 更新最后信号时间
            self.last_signal_time[f"last_{signal_direction.lower()}_signal"] = current_time

            # 计算预期胜率（基于历史数据）
            expected_win_rate = self._calculate_expected_win_rate(signal_direction, signal_strength)

            # 计算信号质量评分
            quality_score = self._calculate_signal_quality_score(
                signal_direction, confidence, supporting_indicators, valid_signals, indicators
            )

            # "画地为牢"策略完整评估（新版本）
            trading_evaluation = self._evaluate_trading_boundaries(signal_direction, indicators, valid_signals)

            # 检查是否应该避免交易
            if trading_evaluation['trading_decision'] == 'AVOID':
                return self._create_no_signal_response(
                    f"'画地为牢'策略：{trading_evaluation['decision_reason']}"
                )

            # 检查信号强度是否足够
            if trading_evaluation['trading_decision'] in ['WAIT', 'WEAK_SIGNAL']:
                return self._create_no_signal_response(
                    f"'画地为牢'策略：{trading_evaluation['decision_reason']}"
                )

            # 保持原有的质量评分作为补充
            favorable_position_score = self._check_favorable_position(signal_direction, indicators, valid_signals)

            # 综合质量评分（新版本：画地为牢评分占主导）
            final_quality_score = (trading_evaluation['total_score'] * 0.8) + (quality_score * 0.2)

            # 生成"画地为牢"策略说明（增强版）
            strategy_explanation = self._generate_enhanced_strategy_explanation(
                signal_direction, trading_evaluation
            )

            signal = {
                'signal_id': signal_id,
                'direction': signal_direction,
                'confidence': round(confidence, 1),
                'signal_strength': signal_strength,
                'supporting_indicators': supporting_indicators,
                'valid_until': valid_until.isoformat(),
                'expiry_time': '10min',
                'expected_win_rate': expected_win_rate,
                'supporting_timeframes': [s[3] for s in valid_signals if s[0] == signal_direction],
                'generation_time': current_time.isoformat(),
                'quality_score': round(quality_score, 1),
                'final_quality_score': round(final_quality_score, 1),
                'favorable_position_score': round(favorable_position_score, 1),
                'philosophy': self.philosophy_mode,
                'strategy_explanation': strategy_explanation,
                'has_signal': True,
                # 新增：画地为牢策略详细评估
                'trading_evaluation': {
                    'boundary_score': trading_evaluation['boundary_score'],
                    'probability_score': trading_evaluation['probability_score'],
                    'simplicity_score': trading_evaluation['simplicity_score'],
                    'risk_control_score': trading_evaluation['risk_control_score'],
                    'total_score': trading_evaluation['total_score'],
                    'trading_decision': trading_evaluation['trading_decision'],
                    'decision_reason': trading_evaluation['decision_reason'],
                    'boundary_details': trading_evaluation['boundary_details'],
                    'risk_warnings': trading_evaluation['risk_warnings']
                }
            }

            # 保存到历史记录
            self.signal_history.append(signal.copy())

            return signal
        else:
            # 提供更详细的无信号原因
            detailed_reason = self._analyze_no_signal_reason(valid_signals, indicators)
            return self._create_no_signal_response(detailed_reason)

    def _evaluate_trading_boundaries(self, direction: str, indicators: Dict, valid_signals: list) -> Dict:
        """
        "画地为牢"策略核心评估 - 完整版本

        明确边界: 设定清晰的技术指标阈值
        概率优势: 在统计有利的位置进入
        简单有效: 用最少的指标获得最大的效果
        风险控制: 主力资金规避和自动暂停机制

        Args:
            direction: 交易方向 ('UP' 或 'DOWN')
            indicators: 技术指标字典
            valid_signals: 有效信号列表

        Returns:
            包含完整评估结果的字典
        """
        evaluation = {
            'boundary_score': 0,      # 边界设定得分
            'probability_score': 0,   # 概率优势得分
            'simplicity_score': 0,    # 简单有效得分
            'risk_control_score': 0,  # 风险控制得分
            'total_score': 0,         # 总得分
            'trading_decision': 'WAIT',  # 交易决策
            'decision_reason': '',    # 决策原因
            'boundary_details': [],   # 边界详情
            'risk_warnings': []       # 风险警告
        }

        try:
            # 获取关键指标
            rsi = indicators.get('rsi', 50)
            bb_position = indicators.get('bb_position', 0.5)
            current_price = indicators.get('current_price', 0)
            volume_ratio = indicators.get('volume_ratio', 1.0)
            support_resistance = indicators.get('support_resistance', {})
            fibonacci_levels = indicators.get('fibonacci_levels', {})
            trend_confirmation = indicators.get('trend_confirmation', {})
            ema_20 = indicators.get('ema_20', current_price)
            ema_50 = indicators.get('ema_50', current_price)

            # === 1. 边界设定评估 (30分) ===
            boundary_score = 0
            boundary_details = []

            if direction == 'UP':
                # 做多边界检查 - 增强版（更保守）

                # 🚨 首先检查急剧下跌和看跌趋势 - 严重扣分
                sharp_downturn = indicators.get('sharp_downturn', {})
                if sharp_downturn.get('is_sharp_downturn', False):
                    boundary_score -= 20  # 严重扣分
                    boundary_details.append("🚨 急剧下跌环境 (-20分)")

                trend_direction = trend_confirmation.get('trend_direction', 'neutral')
                trend_strength = trend_confirmation.get('trend_strength', 0)

                if trend_direction in ['strong_bearish', 'bearish']:
                    if trend_strength >= 60:
                        boundary_score -= 15  # 强势看跌严重扣分
                        boundary_details.append(f"🛡️ 强势看跌趋势 (-15分, 强度{trend_strength}%)")
                    elif trend_strength >= 40:
                        boundary_score -= 10  # 中等看跌扣分
                        boundary_details.append(f"⚠️ 看跌趋势 (-10分, 强度{trend_strength}%)")

                # RSI边界检查 - 在看跌环境中要求更严格
                if trend_direction in ['strong_bearish', 'bearish']:
                    # 看跌环境中需要更严格的超卖条件
                    if rsi <= 20:  # 极度超卖
                        boundary_score += 8
                        boundary_details.append("✅ 看跌环境中极度超卖 (≤20)")
                    elif rsi <= 25:
                        boundary_score += 5
                        boundary_details.append("⚠️ 看跌环境中严格超卖 (≤25)")
                else:
                    # 正常环境的RSI检查
                    if rsi <= 25:  # 严格超卖边界
                        boundary_score += 10
                        boundary_details.append("✅ RSI严格超卖边界 (≤25)")
                    elif rsi <= 30:
                        boundary_score += 6
                        boundary_details.append("⚠️ RSI中度超卖边界 (≤30)")

                if bb_position <= 0.2:  # 布林带下轨边界
                    boundary_score += 8
                    boundary_details.append("✅ 布林带下轨边界 (≤0.2)")
                elif bb_position <= 0.3:
                    boundary_score += 5
                    boundary_details.append("⚠️ 布林带偏下边界 (≤0.3)")

                # 支撑位检查 - 在看跌环境中要求更强的支撑
                if trend_direction in ['strong_bearish', 'bearish']:
                    # 看跌环境中只认可强支撑
                    if support_resistance.get('near_support', False):
                        boundary_score += 10  # 降低得分
                        boundary_details.append("⚠️ 看跌环境中的支撑位 (降低权重)")
                else:
                    # 正常环境的支撑位检查
                    if support_resistance.get('near_support', False):
                        boundary_score += 12
                        boundary_details.append("✅ 接近关键支撑位")
                    elif support_resistance.get('approaching_support', False):
                        boundary_score += 8
                        boundary_details.append("⚠️ 接近支撑区域")

            elif direction == 'DOWN':
                # 做空边界检查
                if rsi >= 75:  # 严格超买边界
                    boundary_score += 10
                    boundary_details.append("✅ RSI严格超买边界 (≥75)")
                elif rsi >= 70:
                    boundary_score += 6
                    boundary_details.append("⚠️ RSI中度超买边界 (≥70)")

                if bb_position >= 0.8:  # 布林带上轨边界
                    boundary_score += 8
                    boundary_details.append("✅ 布林带上轨边界 (≥0.8)")
                elif bb_position >= 0.7:
                    boundary_score += 5
                    boundary_details.append("⚠️ 布林带偏上边界 (≥0.7)")

                if support_resistance.get('near_resistance', False):
                    boundary_score += 12
                    boundary_details.append("✅ 接近关键阻力位")
                elif support_resistance.get('approaching_resistance', False):
                    boundary_score += 8
                    boundary_details.append("⚠️ 接近阻力区域")

            evaluation['boundary_score'] = boundary_score
            evaluation['boundary_details'] = boundary_details

            # === 2. 概率优势评估 (30分) ===
            probability_score = 0

            # 多时间框架一致性
            if len(valid_signals) >= 3:
                probability_score += 12
                evaluation['boundary_details'].append("✅ 多时间框架强一致性")
            elif len(valid_signals) >= 2:
                probability_score += 8
                evaluation['boundary_details'].append("⚠️ 多时间框架中等一致性")

            # 趋势确认优势 - 增强版（对UP方向更保守）
            if trend_confirmation.get('is_trend_confirmed', False):
                trend_direction = trend_confirmation.get('trend_direction', 'neutral')
                trend_strength = trend_confirmation.get('trend_strength', 0)

                if direction == 'UP':
                    # 做多方向需要更严格的趋势确认
                    if trend_direction in ['strong_bullish']:
                        probability_score += 12
                        evaluation['boundary_details'].append("✅ 强势看涨趋势确认")
                    elif trend_direction in ['bullish']:
                        probability_score += 8
                        evaluation['boundary_details'].append("⚠️ 看涨趋势确认")
                    elif trend_direction == 'neutral' and trend_strength >= 30:
                        probability_score += 3
                        evaluation['boundary_details'].append("⚠️ 中性偏多趋势")
                    elif trend_direction in ['bearish', 'strong_bearish']:
                        probability_score -= 8  # 看跌趋势中做多扣分
                        evaluation['boundary_details'].append("🚨 逆势做多风险 (-8分)")
                    else:
                        probability_score += 1
                        evaluation['boundary_details'].append("⚠️ 趋势不明")

                elif direction == 'DOWN':
                    # 做空方向保持原有逻辑
                    if trend_direction in ['bearish', 'strong_bearish']:
                        probability_score += 10
                        evaluation['boundary_details'].append("✅ 趋势确认优势")
                    else:
                        probability_score += 5
                        evaluation['boundary_details'].append("⚠️ 趋势中性")

            # 斐波那契位置优势
            if fibonacci_levels:
                fib_advantage = False
                if direction == 'UP':
                    fib_618 = fibonacci_levels.get('fib_61.8', 0)
                    fib_50 = fibonacci_levels.get('fib_50.0', 0)
                    if abs(current_price - fib_618) / current_price < 0.01:
                        probability_score += 8
                        fib_advantage = True
                        evaluation['boundary_details'].append("✅ 斐波那契61.8%支撑优势")
                    elif abs(current_price - fib_50) / current_price < 0.01:
                        probability_score += 5
                        fib_advantage = True
                        evaluation['boundary_details'].append("⚠️ 斐波那契50%支撑")
                elif direction == 'DOWN':
                    fib_236 = fibonacci_levels.get('fib_23.6', 0)
                    fib_382 = fibonacci_levels.get('fib_38.2', 0)
                    if abs(current_price - fib_236) / current_price < 0.01:
                        probability_score += 8
                        fib_advantage = True
                        evaluation['boundary_details'].append("✅ 斐波那契23.6%阻力优势")
                    elif abs(current_price - fib_382) / current_price < 0.01:
                        probability_score += 5
                        fib_advantage = True
                        evaluation['boundary_details'].append("⚠️ 斐波那契38.2%阻力")

            evaluation['probability_score'] = probability_score

            # === 3. 简单有效评估 (20分) ===
            simplicity_score = 0

            # 核心指标一致性
            core_indicators_aligned = 0
            if direction == 'UP':
                if rsi <= 30: core_indicators_aligned += 1
                if bb_position <= 0.3: core_indicators_aligned += 1
                if current_price > ema_20: core_indicators_aligned += 1
            elif direction == 'DOWN':
                if rsi >= 70: core_indicators_aligned += 1
                if bb_position >= 0.7: core_indicators_aligned += 1
                if current_price < ema_20: core_indicators_aligned += 1

            simplicity_score = core_indicators_aligned * 7  # 每个核心指标7分
            if core_indicators_aligned >= 3:
                evaluation['boundary_details'].append("✅ 核心指标完全一致")
            elif core_indicators_aligned >= 2:
                evaluation['boundary_details'].append("⚠️ 核心指标部分一致")

            evaluation['simplicity_score'] = simplicity_score

            # === 4. 风险控制评估 (20分) ===
            risk_control_score = 0
            risk_warnings = []

            # 成交量确认
            if volume_ratio > 1.2:
                risk_control_score += 8
                evaluation['boundary_details'].append("✅ 成交量放大确认")
            elif volume_ratio < 0.8:
                risk_warnings.append("⚠️ 成交量萎缩风险")
            else:
                risk_control_score += 4
                evaluation['boundary_details'].append("⚠️ 成交量正常")

            # 波动率检查
            volatility_ratio = indicators.get('volatility_ratio', 1.0)
            if volatility_ratio > 2.0:
                risk_warnings.append("🚨 高波动率风险")
            elif volatility_ratio > 1.5:
                risk_warnings.append("⚠️ 中等波动率")
                risk_control_score += 4
            else:
                risk_control_score += 8
                evaluation['boundary_details'].append("✅ 低波动率环境")

            # 流动性检查
            is_low_liquidity = indicators.get('is_low_liquidity', False)
            if is_low_liquidity:
                risk_warnings.append("🚨 低流动性风险")
            else:
                risk_control_score += 4
                evaluation['boundary_details'].append("✅ 流动性充足")

            evaluation['risk_control_score'] = risk_control_score
            evaluation['risk_warnings'] = risk_warnings

            # === 5. 综合评估 ===
            total_score = boundary_score + probability_score + simplicity_score + risk_control_score
            evaluation['total_score'] = total_score

            # 交易决策 - 对UP方向更保守
            if len(risk_warnings) > 0 and any('🚨' in warning for warning in risk_warnings):
                evaluation['trading_decision'] = 'AVOID'
                evaluation['decision_reason'] = f"高风险环境，避免交易: {'; '.join(risk_warnings)}"
            elif direction == 'UP':
                # 做多方向使用更严格的标准
                sharp_downturn = indicators.get('sharp_downturn', {})
                trend_direction = trend_confirmation.get('trend_direction', 'neutral')

                # 在急剧下跌或看跌趋势中，提高门槛
                if sharp_downturn.get('is_sharp_downturn', False) or trend_direction in ['bearish', 'strong_bearish']:
                    min_score_for_strong = 85  # 提高到85分
                    min_score_for_moderate = 70  # 提高到70分
                    min_score_for_weak = 50  # 提高到50分
                else:
                    min_score_for_strong = 75  # 正常情况下75分
                    min_score_for_moderate = 55  # 正常情况下55分
                    min_score_for_weak = 35  # 正常情况下35分

                if total_score >= min_score_for_strong:
                    evaluation['trading_decision'] = 'STRONG_SIGNAL'
                    evaluation['decision_reason'] = f"强势多头信号，总分{total_score}/100，满足严格标准"
                elif total_score >= min_score_for_moderate:
                    evaluation['trading_decision'] = 'MODERATE_SIGNAL'
                    evaluation['decision_reason'] = f"中等多头信号，总分{total_score}/100，谨慎进入"
                elif total_score >= min_score_for_weak:
                    evaluation['trading_decision'] = 'WEAK_SIGNAL'
                    evaluation['decision_reason'] = f"弱多头信号，总分{total_score}/100，建议等待"
                else:
                    evaluation['trading_decision'] = 'WAIT'
                    evaluation['decision_reason'] = f"多头信号不足，总分{total_score}/100，继续等待"
            else:
                # 做空方向保持原有标准
                if total_score >= 70:
                    evaluation['trading_decision'] = 'STRONG_SIGNAL'
                    evaluation['decision_reason'] = f"强势信号，总分{total_score}/100，满足画地为牢标准"
                elif total_score >= 50:
                    evaluation['trading_decision'] = 'MODERATE_SIGNAL'
                    evaluation['decision_reason'] = f"中等信号，总分{total_score}/100，谨慎进入"
                elif total_score >= 30:
                    evaluation['trading_decision'] = 'WEAK_SIGNAL'
                    evaluation['decision_reason'] = f"弱信号，总分{total_score}/100，建议等待"
                else:
                    evaluation['trading_decision'] = 'WAIT'
                    evaluation['decision_reason'] = f"信号不足，总分{total_score}/100，继续等待"

            return evaluation

        except Exception as e:
            print(f"⚠️ 画地为牢策略评估失败: {e}")
            evaluation['decision_reason'] = f"评估失败: {e}"
            return evaluation

    def _check_favorable_position(self, direction: str, indicators: Dict, valid_signals: list) -> float:
        """
        "画地为牢"策略：检查是否在有利位置

        做多(123点): 在超卖、支撑位、均线支撑的有利位置
        做空(456点): 在超买、阻力位、均线阻力的有利位置
        """
        score = 0
        max_score = 100

        try:
            # 获取关键指标
            rsi = indicators.get('rsi', 50)
            bb_position = indicators.get('bb_position', 0.5)
            ema_trend = indicators.get('ema_trend', 'neutral')
            support_resistance = indicators.get('support_resistance', {})
            volume_profile = indicators.get('volume_profile', 1.0)

            if direction == 'UP':  # 做多(123点)：寻找有利的做多位置
                # 1. 超卖条件检查 (30分) - 优化RSI阈值
                if rsi <= 25:  # 优化：从30调整为25，更严格的超卖条件
                    score += 30  # 强超卖
                elif rsi <= 30:  # 优化：从35调整为30
                    score += 20  # 中度超卖
                elif rsi <= 35:  # 优化：从40调整为35
                    score += 10  # 轻度超卖

                # 2. 布林带位置检查 (25分)
                if bb_position <= 0.2:
                    score += 25  # 接近下轨
                elif bb_position <= 0.3:
                    score += 15  # 偏向下轨

                # 3. 均线支撑检查 (25分)
                if ema_trend == 'bullish_support':
                    score += 25
                elif ema_trend == 'neutral_support':
                    score += 15
                elif ema_trend == 'weak_support':
                    score += 10

                # 4. 支撑位检查 (20分)
                if support_resistance.get('near_support', False):
                    score += 20
                elif support_resistance.get('approaching_support', False):
                    score += 10

            elif direction == 'DOWN':  # 做空(456点)：寻找有利的做空位置
                # 1. 超买条件检查 (30分) - 优化RSI阈值
                if rsi >= 75:  # 优化：从70调整为75，更严格的超买条件
                    score += 30  # 强超买
                elif rsi >= 70:  # 优化：从65调整为70
                    score += 20  # 中度超买
                elif rsi >= 65:  # 优化：从60调整为65
                    score += 10  # 轻度超买

                # 2. 布林带位置检查 (25分)
                if bb_position >= 0.8:
                    score += 25  # 接近上轨
                elif bb_position >= 0.7:
                    score += 15  # 偏向上轨

                # 3. 均线阻力检查 (25分)
                if ema_trend == 'bearish_resistance':
                    score += 25
                elif ema_trend == 'neutral_resistance':
                    score += 15
                elif ema_trend == 'weak_resistance':
                    score += 10

                # 4. 阻力位检查 (20分)
                if support_resistance.get('near_resistance', False):
                    score += 20
                elif support_resistance.get('approaching_resistance', False):
                    score += 10

            # 确保评分在合理范围内
            score = min(score, max_score)

            return score

        except Exception as e:
            print(f"⚠️ 有利位置检查失败: {e}")
            return 50  # 返回中性评分

    def _calculate_expected_win_rate(self, direction: str, strength: str) -> float:
        """
        计算预期胜率 - 改进版本，基于真实历史数据

        Args:
            direction: 交易方向 ('UP' 或 'DOWN')
            strength: 信号强度 ('STRONG', 'MEDIUM', 'WEAK')

        Returns:
            预期胜率（百分比）
        """
        # 获取历史交易数据进行真实胜率计算
        try:
            # 从全局交易跟踪器获取历史数据
            if hasattr(self, '_trade_tracker_ref') and self._trade_tracker_ref:
                historical_data = self._trade_tracker_ref.get_historical_win_rates(
                    direction=direction,
                    strength=strength,
                    lookback_days=30  # 使用30天历史数据
                )

                if historical_data['total_trades'] >= 10:  # 至少10笔交易才使用历史数据
                    # 使用真实历史胜率，但加入置信区间调整
                    historical_win_rate = historical_data['win_rate']
                    confidence_adjustment = self._calculate_confidence_adjustment(
                        historical_data['total_trades'],
                        historical_data['win_rate']
                    )

                    # 应用贝叶斯调整，结合先验知识和历史数据
                    adjusted_rate = self._bayesian_win_rate_adjustment(
                        historical_win_rate,
                        historical_data['total_trades'],
                        strength
                    )

                    return min(90.0, max(30.0, adjusted_rate + confidence_adjustment))
        except Exception as e:
            print(f"⚠️ 历史胜率计算失败，使用保守估计: {e}")

        # 如果没有足够历史数据，使用保守的基础胜率（降低预期）
        conservative_base_rates = {
            'STRONG': 60.0,  # 从75%降低到60%
            'MEDIUM': 52.0,  # 从65%降低到52%
            'WEAK': 45.0     # 从55%降低到45%
        }

        # 根据最近信号表现进行微调
        recent_adjustment = self._calculate_recent_performance_adjustment(direction)

        base_rate = conservative_base_rates.get(strength, 45.0)
        final_rate = base_rate + recent_adjustment

        return min(75.0, max(35.0, final_rate))  # 限制在35%-75%范围内

    def _calculate_confidence_adjustment(self, sample_size: int, win_rate: float) -> float:
        """
        基于样本大小计算置信度调整

        Args:
            sample_size: 样本数量
            win_rate: 历史胜率

        Returns:
            置信度调整值
        """
        import math

        # 计算标准误差
        if sample_size > 0 and 0 < win_rate < 100:
            p = win_rate / 100.0
            standard_error = math.sqrt(p * (1 - p) / sample_size)

            # 95%置信区间的调整
            confidence_interval = 1.96 * standard_error * 100

            # 如果置信区间较大，降低预期胜率（更保守）
            if confidence_interval > 15:  # 置信区间超过15%
                return -confidence_interval * 0.3
            elif confidence_interval > 10:  # 置信区间超过10%
                return -confidence_interval * 0.2
            else:
                return 0

        return -5.0  # 默认保守调整

    def _bayesian_win_rate_adjustment(self, historical_rate: float, sample_size: int, strength: str) -> float:
        """
        贝叶斯胜率调整，结合先验知识和历史数据

        Args:
            historical_rate: 历史胜率
            sample_size: 样本大小
            strength: 信号强度

        Returns:
            调整后的胜率
        """
        # 先验分布参数（基于信号强度）
        prior_params = {
            'STRONG': {'alpha': 12, 'beta': 8},   # 先验胜率约60%
            'MEDIUM': {'alpha': 10, 'beta': 10}, # 先验胜率约50%
            'WEAK': {'alpha': 8, 'beta': 12}     # 先验胜率约40%
        }

        prior = prior_params.get(strength, prior_params['MEDIUM'])

        # 贝叶斯更新
        wins = int(historical_rate * sample_size / 100)
        losses = sample_size - wins

        # 后验参数
        posterior_alpha = prior['alpha'] + wins
        posterior_beta = prior['beta'] + losses

        # 后验均值
        posterior_mean = posterior_alpha / (posterior_alpha + posterior_beta)

        return posterior_mean * 100

    def _calculate_recent_performance_adjustment(self, direction: str) -> float:
        """
        基于最近表现计算调整值

        Args:
            direction: 交易方向

        Returns:
            调整值（-10到+5之间）
        """
        if len(self.signal_history) < 5:
            return -2.0  # 数据不足时保守调整

        # 分析最近10个同方向信号的表现
        recent_signals = list(self.signal_history)[-20:]  # 扩大样本
        direction_signals = [s for s in recent_signals if s.get('direction') == direction]

        if len(direction_signals) < 3:
            return -2.0  # 同方向信号不足时保守调整

        # 这里需要实际的胜负记录，暂时使用模拟逻辑
        # 在实际实现中，应该从交易历史中获取真实结果

        # 基于信号时间间隔的调整（避免过度交易）
        recent_direction_signals = direction_signals[-5:]
        if len(recent_direction_signals) >= 3:
            # 如果最近同方向信号过于频繁，降低预期
            time_intervals = []
            for i in range(1, len(recent_direction_signals)):
                try:
                    prev_time = datetime.fromisoformat(recent_direction_signals[i-1].get('timestamp', ''))
                    curr_time = datetime.fromisoformat(recent_direction_signals[i].get('timestamp', ''))
                    interval = (curr_time - prev_time).total_seconds() / 60  # 分钟
                    time_intervals.append(interval)
                except:
                    continue

            if time_intervals:
                avg_interval = sum(time_intervals) / len(time_intervals)
                if avg_interval < 30:  # 平均间隔小于30分钟
                    return -5.0  # 过于频繁，大幅降低预期
                elif avg_interval < 60:  # 平均间隔小于60分钟
                    return -3.0  # 较为频繁，适度降低预期

        return 0.0  # 默认无调整

    def _calculate_signal_quality_score(self, signal_direction: str, confidence: float,
                                      supporting_indicators: list, valid_signals: list,
                                      indicators: Dict) -> float:
        """
        计算信号质量评分（0-100分）

        Args:
            signal_direction: 信号方向
            confidence: 置信度
            supporting_indicators: 支撑指标列表
            valid_signals: 有效信号列表
            indicators: 技术指标数据

        Returns:
            信号质量评分（0-100）
        """
        score = 0.0

        # 1. 基础置信度评分（30分）- 适度放宽评分标准
        if confidence >= 98:
            score += 30
        elif confidence >= 96:
            score += 27  # 提高96%的评分
        elif confidence >= 95:
            score += 25  # 提高95%的评分
        elif confidence >= 92:
            score += 22  # 提高92%的评分
        elif confidence >= 90:
            score += 18  # 提高90%的评分
        else:
            score += 12  # 提高基础评分

        # 2. 多时间框架一致性评分（25分）- 适度放宽评分标准
        if len(valid_signals) >= 4:  # 所有4个时间框架
            score += 25
        elif len(valid_signals) >= 3:  # 至少3个时间框架
            score += 22  # 提高3个时间框架的评分
        elif len(valid_signals) >= 2:  # 至少2个时间框架
            score += 18  # 提高2个时间框架的评分
        else:
            score += 12  # 提高基础评分

        # 3. 支撑指标质量评分（20分）
        indicator_score = 0
        high_quality_indicators = ['RSI_extreme_oversold', 'RSI_extreme_overbought',
                                 'BB_extreme_oversold', 'BB_extreme_overbought']

        for indicator in supporting_indicators:
            if indicator in high_quality_indicators:
                indicator_score += 5  # 高质量指标
            else:
                indicator_score += 3  # 普通指标

        score += min(20, indicator_score)

        # 4. 技术指标极值评分（15分）
        rsi = indicators.get('rsi', 50)
        bb_position = indicators.get('bb_position', 0.5)
        volume_ratio = indicators.get('volume_ratio', 1.0)

        extreme_score = 0
        if signal_direction == 'UP':
            if rsi < 20:
                extreme_score += 8
            elif rsi < 25:
                extreme_score += 5

            if bb_position < 0.1:
                extreme_score += 7
            elif bb_position < 0.15:
                extreme_score += 4
        else:  # DOWN
            if rsi > 80:
                extreme_score += 8
            elif rsi > 75:
                extreme_score += 5

            if bb_position > 0.9:
                extreme_score += 7
            elif bb_position > 0.85:
                extreme_score += 4

        score += min(15, extreme_score)

        # 5. 成交量确认评分（10分）
        if volume_ratio > 3.0:
            score += 10
        elif volume_ratio > 2.0:
            score += 7
        elif volume_ratio > 1.5:
            score += 5
        else:
            score += 2

        # 6. 历史表现调整（可选，基于历史数据）
        if hasattr(self, '_trade_tracker_ref') and self._trade_tracker_ref:
            try:
                historical_data = self._trade_tracker_ref.get_historical_win_rates(
                    direction=signal_direction, lookback_days=7
                )
                if historical_data['total_trades'] >= 5:
                    recent_win_rate = historical_data['win_rate']
                    if recent_win_rate > 70:
                        score += 5  # 最近表现好
                    elif recent_win_rate < 40:
                        score -= 10  # 最近表现差
            except:
                pass  # 忽略历史数据错误

        return min(100, max(0, score))

    def add_minute_kline_data(self, kline_data: Dict):
        """
        添加1分钟K线数据并聚合成15分钟K线
        
        Args:
            kline_data: 1分钟K线数据字典，包含open, high, low, close, volume, timestamp
        """
        try:
            # 添加1分钟K线数据
            self.minute_klines.append(kline_data)
            
            # 每15分钟聚合一次
            if len(self.minute_klines) >= 15:
                self._aggregate_fifteen_minute_klines()
                
        except Exception as e:
            print(f"⚠️ 添加1分钟K线数据失败: {e}")
    
    def _aggregate_fifteen_minute_klines(self):
        """
        聚合1分钟K线数据为15分钟K线
        """
        try:
            if len(self.minute_klines) < 15:
                return
                
            # 取最近15分钟的数据
            recent_15min = list(self.minute_klines)[-15:]
            
            # 聚合数据
            open_price = recent_15min[0]['open']
            close_price = recent_15min[-1]['close']
            high_price = max(kline['high'] for kline in recent_15min)
            low_price = min(kline['low'] for kline in recent_15min)
            total_volume = sum(kline['volume'] for kline in recent_15min)
            
            # 创建15分钟K线数据
            fifteen_min_kline = {
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': total_volume,
                'timestamp': recent_15min[-1]['timestamp']
            }
            
            # 添加到15分钟K线序列
            self.fifteen_min_klines.append(fifteen_min_kline)
            
            # 清空1分钟K线数据
            self.minute_klines.clear()
            
        except Exception as e:
            print(f"⚠️ 聚合15分钟K线数据失败: {e}")
    
    def calculate_dice_probability(self, current_price: float, indicators: Dict) -> Dict:
        """
        六面骰子概率模型计算
        
        Args:
            current_price: 当前价格
            indicators: 技术指标数据
            
        Returns:
            包含六面骰子概率分析的字典
        """
        try:
            # 六面骰子概率计算
            dice_probabilities = {}
            
            # 获取技术指标
            rsi = indicators.get('rsi', 50)
            bb_position = indicators.get('bb_position', 0.5)
            ema_20 = indicators.get('ema_20', current_price)
            ema_50 = indicators.get('ema_50', current_price)
            support_resistance = indicators.get('support_resistance', {})
            
            # 点数1: 超卖区域做多
            if rsi < 30:
                dice_probabilities[1] = min(90, 60 + (30 - rsi))
            else:
                dice_probabilities[1] = max(10, 60 - (rsi - 30))
            
            # 点数2: 关键支撑位做多
            if support_resistance.get('near_support', False):
                dice_probabilities[2] = 85
            elif support_resistance.get('approaching_support', False):
                dice_probabilities[2] = 70
            else:
                dice_probabilities[2] = 30
            
            # 点数3: 移动均线支撑做多
            if current_price > ema_20 and ema_20 > ema_50:
                dice_probabilities[3] = 80
            elif current_price > ema_20:
                dice_probabilities[3] = 65
            else:
                dice_probabilities[3] = 35
            
            # 点数4: 移动均线阻力做空
            if current_price < ema_20 and ema_20 < ema_50:
                dice_probabilities[4] = 80
            elif current_price < ema_20:
                dice_probabilities[4] = 65
            else:
                dice_probabilities[4] = 35
            
            # 点数5: 关键阻力位做空
            if support_resistance.get('near_resistance', False):
                dice_probabilities[5] = 85
            elif support_resistance.get('approaching_resistance', False):
                dice_probabilities[5] = 70
            else:
                dice_probabilities[5] = 30
            
            # 点数6: 超买区域做空
            if rsi > 70:
                dice_probabilities[6] = min(90, 60 + (rsi - 70))
            else:
                dice_probabilities[6] = max(10, 60 - (70 - rsi))
            
            # 计算做多和做空的总概率
            long_total = sum(dice_probabilities[i] for i in [1, 2, 3])
            short_total = sum(dice_probabilities[i] for i in [4, 5, 6])
            
            # 归一化处理
            total_prob = long_total + short_total
            if total_prob > 0:
                long_probability = (long_total / total_prob) * 100
                short_probability = (short_total / total_prob) * 100
            else:
                long_probability = 50
                short_probability = 50
            
            return {
                'dice_probabilities': dice_probabilities,
                'long_probability': round(long_probability, 2),
                'short_probability': round(short_probability, 2),
                'dominant_direction': 'LONG' if long_probability > short_probability else 'SHORT',
                'confidence': abs(long_probability - short_probability)
            }
            
        except Exception as e:
            print(f"⚠️ 六面骰子概率计算失败: {e}")
            return {
                'dice_probabilities': {i: 50 for i in range(1, 7)},
                'long_probability': 50,
                'short_probability': 50,
                'dominant_direction': 'NEUTRAL',
                'confidence': 0
            }
    
    def analyze_minute_probabilities(self, current_price: float, indicators: Dict) -> Dict:
        """
        逐分钟概率分析 - 对15分钟K线进行1-15分钟颗粒度分析
        
        Args:
            current_price: 当前价格
            indicators: 技术指标数据
            
        Returns:
            包含逐分钟概率分析的字典
        """
        try:
            minute_probabilities = {}
            
            # 获取基础概率
            base_dice_result = self.calculate_dice_probability(current_price, indicators)
            base_long_prob = base_dice_result['long_probability']
            base_short_prob = base_dice_result['short_probability']
            
            # 对每分钟进行概率分析
            for minute in range(1, 16):
                prob_data = self._calculate_minute_probability(minute, base_long_prob, base_short_prob, indicators)
                minute_probabilities[f'minute_{minute}'] = prob_data
            
            # 计算总体概率分布
            summary = self._calculate_minute_summary(minute_probabilities)
            
            # 存储历史数据
            self._store_minute_probability_history(minute_probabilities, summary)
            
            return {
                'minute_probabilities': minute_probabilities,
                'summary': summary,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"⚠️ 逐分钟概率分析失败: {e}")
            return {
                'minute_probabilities': {},
                'summary': {
                    'avg_long_probability': 50,
                    'avg_short_probability': 50,
                    'peak_long_minutes': [],
                    'peak_short_minutes': [],
                    'recommended_entry_minutes': []
                },
                'analysis_timestamp': datetime.now().isoformat()
            }
    
    def _calculate_minute_probability(self, minute: int, base_long_prob: float, base_short_prob: float, indicators: Dict) -> Dict:
        """
        计算特定分钟的入场概率
        
        Args:
            minute: 分钟数 (1-15)
            base_long_prob: 基础做多概率
            base_short_prob: 基础做空概率
            indicators: 技术指标数据
            
        Returns:
            该分钟的概率分析数据
        """
        try:
            # 时间衰减因子 (越接近15分钟，概率调整越大)
            time_factor = minute / 15.0
            
            # 波动性调整
            volatility = indicators.get('volatility', 1.0)
            volatility_adjustment = min(1.2, max(0.8, volatility))
            
            # 成交量调整
            volume_ratio = indicators.get('volume_ratio', 1.0)
            volume_adjustment = min(1.15, max(0.85, volume_ratio))
            
            # 计算调整后的概率
            long_prob = base_long_prob * time_factor * volatility_adjustment * volume_adjustment
            short_prob = base_short_prob * time_factor * volatility_adjustment * volume_adjustment
            
            # 归一化
            total_prob = long_prob + short_prob
            if total_prob > 0:
                long_prob = (long_prob / total_prob) * 100
                short_prob = (short_prob / total_prob) * 100
            else:
                long_prob = 50
                short_prob = 50
            
            return {
                'minute': minute,
                'long_probability': round(long_prob, 2),
                'short_probability': round(short_prob, 2),
                'confidence': abs(long_prob - short_prob),
                'time_factor': time_factor,
                'volatility_adjustment': volatility_adjustment,
                'volume_adjustment': volume_adjustment
            }
            
        except Exception as e:
            print(f"⚠️ 计算分钟{minute}概率失败: {e}")
            return {
                'minute': minute,
                'long_probability': 50,
                'short_probability': 50,
                'confidence': 0,
                'time_factor': 1.0,
                'volatility_adjustment': 1.0,
                'volume_adjustment': 1.0
            }
    
    def _calculate_minute_summary(self, minute_probabilities: Dict) -> Dict:
        """
        计算逐分钟分析的总体概率分布
        
        Args:
            minute_probabilities: 逐分钟概率数据
            
        Returns:
            总体概率分布摘要
        """
        try:
            long_probs = []
            short_probs = []
            peak_long_minutes = []
            peak_short_minutes = []
            
            for minute_key, prob_data in minute_probabilities.items():
                long_prob = prob_data['long_probability']
                short_prob = prob_data['short_probability']
                minute = prob_data['minute']
                
                long_probs.append(long_prob)
                short_probs.append(short_prob)
                
                # 识别峰值分钟
                if long_prob > 70:
                    peak_long_minutes.append(minute)
                if short_prob > 70:
                    peak_short_minutes.append(minute)
            
            # 计算平均概率
            avg_long_prob = sum(long_probs) / len(long_probs) if long_probs else 50
            avg_short_prob = sum(short_probs) / len(short_probs) if short_probs else 50
            
            # 推荐入场分钟
            recommended_minutes = []
            for minute_key, prob_data in minute_probabilities.items():
                if prob_data['confidence'] > 20:  # 置信度大于20%
                    recommended_minutes.append(prob_data['minute'])
            
            return {
                'avg_long_probability': round(avg_long_prob, 2),
                'avg_short_probability': round(avg_short_prob, 2),
                'peak_long_minutes': peak_long_minutes,
                'peak_short_minutes': peak_short_minutes,
                'recommended_entry_minutes': recommended_minutes[:5],  # 最多推荐5个分钟
                'total_minutes_analyzed': len(minute_probabilities)
            }
            
        except Exception as e:
            print(f"⚠️ 计算逐分钟摘要失败: {e}")
            return {
                'avg_long_probability': 50,
                'avg_short_probability': 50,
                'peak_long_minutes': [],
                'peak_short_minutes': [],
                'recommended_entry_minutes': [],
                'total_minutes_analyzed': 0
            }
    
    def _store_minute_probability_history(self, minute_probabilities: Dict, summary: Dict):
        """
        存储逐分钟概率分析历史数据
        
        Args:
            minute_probabilities: 逐分钟概率数据
            summary: 总体概率分布摘要
        """
        try:
            timestamp = datetime.now().isoformat()
            
            # 存储到历史数据
            self.minute_probability_history[timestamp] = {
                'minute_probabilities': minute_probabilities,
                'summary': summary,
                'timestamp': timestamp
            }
            
            # 保持最近100条记录
            if len(self.minute_probability_history) > 100:
                oldest_key = min(self.minute_probability_history.keys())
                del self.minute_probability_history[oldest_key]
                
        except Exception as e:
            print(f"⚠️ 存储逐分钟概率历史失败: {e}")

    def classify_signal_dimension(self, current_price: float, indicators: Dict, multi_analysis: Dict) -> Dict:
        """
        五维度信号分类
        
        Args:
            current_price: 当前价格
            indicators: 技术指标数据
            multi_analysis: 多时间周期分析结果
            
        Returns:
            五维度信号分类结果
        """
        try:
            # 初始化五个维度
            dimensions = {
                'trend_reversal': {'type': 'neutral', 'confidence': 0, 'details': []},
                'trend_continuation_up': {'type': 'neutral', 'confidence': 0, 'details': []},
                'trend_continuation_down': {'type': 'neutral', 'confidence': 0, 'details': []},
                'consolidation': {'type': 'neutral', 'confidence': 0, 'details': []},
                'box_oscillation': {'type': 'neutral', 'confidence': 0, 'details': []}
            }
            
            # 获取技术指标
            rsi = indicators.get('rsi', 50)
            macd_histogram = indicators.get('macd_histogram', 0)
            bb_position = indicators.get('bb_position', 0.5)
            ema_20 = indicators.get('ema_20', current_price)
            ema_50 = indicators.get('ema_50', current_price)
            support_resistance = indicators.get('support_resistance', {})
            
            # 1. 趋势反转信号检测
            reversal_signals = []
            reversal_confidence = 0
            
            # 顶部反转
            if (rsi > 70 and macd_histogram < 0 and 
                support_resistance.get('near_resistance', False)):
                reversal_signals.append('顶部反转信号')
                reversal_confidence += 30
                dimensions['trend_reversal']['type'] = 'top_reversal'
            
            # 底部反转
            if (rsi < 30 and macd_histogram > 0 and 
                support_resistance.get('near_support', False)):
                reversal_signals.append('底部反转信号')
                reversal_confidence += 30
                dimensions['trend_reversal']['type'] = 'bottom_reversal'
            
            dimensions['trend_reversal']['confidence'] = min(100, reversal_confidence)
            dimensions['trend_reversal']['details'] = reversal_signals
            
            # 2. 向上趋势延续检测
            up_continuation_signals = []
            up_continuation_confidence = 0
            
            if (current_price > ema_20 and ema_20 > ema_50 and 
                rsi > 50 and macd_histogram > 0):
                up_continuation_signals.append('向上趋势延续')
                up_continuation_confidence += 40
                dimensions['trend_continuation_up']['type'] = 'bullish_continuation'
            
            dimensions['trend_continuation_up']['confidence'] = min(100, up_continuation_confidence)
            dimensions['trend_continuation_up']['details'] = up_continuation_signals
            
            # 3. 向下趋势延续检测
            down_continuation_signals = []
            down_continuation_confidence = 0
            
            if (current_price < ema_20 and ema_20 < ema_50 and 
                rsi < 50 and macd_histogram < 0):
                down_continuation_signals.append('向下趋势延续')
                down_continuation_confidence += 40
                dimensions['trend_continuation_down']['type'] = 'bearish_continuation'
            
            dimensions['trend_continuation_down']['confidence'] = min(100, down_continuation_confidence)
            dimensions['trend_continuation_down']['details'] = down_continuation_signals
            
            # 4. 整理形态检测
            consolidation_signals = []
            consolidation_confidence = 0
            
            if (abs(current_price - ema_20) / current_price < 0.01 and 
                40 < rsi < 60 and abs(macd_histogram) < 0.01):
                consolidation_signals.append('价格整理形态')
                consolidation_confidence += 35
                dimensions['consolidation']['type'] = 'sideways_consolidation'
            
            dimensions['consolidation']['confidence'] = min(100, consolidation_confidence)
            dimensions['consolidation']['details'] = consolidation_signals
            
            # 5. 箱体震荡检测
            box_oscillation_signals = []
            box_oscillation_confidence = 0
            
            if (0.3 < bb_position < 0.7 and 
                support_resistance.get('support_levels') and 
                support_resistance.get('resistance_levels')):
                box_oscillation_signals.append('箱体震荡区间')
                box_oscillation_confidence += 35
                dimensions['box_oscillation']['type'] = 'range_bound'
            
            dimensions['box_oscillation']['confidence'] = min(100, box_oscillation_confidence)
            dimensions['box_oscillation']['details'] = box_oscillation_signals
            
            # 确定主导维度
            max_confidence = 0
            dominant_dimension = 'neutral'
            for dim_name, dim_data in dimensions.items():
                if dim_data['confidence'] > max_confidence:
                    max_confidence = dim_data['confidence']
                    dominant_dimension = dim_name
            
            return {
                'dimensions': dimensions,
                'dominant_dimension': dominant_dimension,
                'max_confidence': max_confidence,
                'classification_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"⚠️ 五维度信号分类失败: {e}")
            return {
                'dimensions': {
                    'trend_reversal': {'type': 'neutral', 'confidence': 0, 'details': []},
                    'trend_continuation_up': {'type': 'neutral', 'confidence': 0, 'details': []},
                    'trend_continuation_down': {'type': 'neutral', 'confidence': 0, 'details': []},
                    'consolidation': {'type': 'neutral', 'confidence': 0, 'details': []},
                    'box_oscillation': {'type': 'neutral', 'confidence': 0, 'details': []}
                },
                'dominant_dimension': 'neutral',
                'max_confidence': 0,
                'classification_timestamp': datetime.now().isoformat()
            }

    def _analyze_no_signal_reason(self, valid_signals: list, indicators: Dict) -> str:
        """
        分析无信号的原因
        
        Args:
            valid_signals: 有效信号列表
            indicators: 技术指标数据
            
        Returns:
            详细的无信号原因说明
        """
        try:
            reasons = []
            
            # 检查有效信号数量
            if len(valid_signals) == 0:
                reasons.append("所有时间框架都没有达到概率阈值")
            elif len(valid_signals) < self.min_timeframes:
                reasons.append(f"有效信号数量不足（{len(valid_signals)}/{self.min_timeframes}）")
            
            # 检查技术指标条件
            rsi = indicators.get('rsi', 50)
            bb_position = indicators.get('bb_position', 0.5)
            
            if 40 <= rsi <= 60:
                reasons.append("RSI处于中性区域，缺乏极值信号")
            
            if 0.3 <= bb_position <= 0.7:
                reasons.append("布林带位置中性，未达到超买超卖区域")
            
            # 检查支撑阻力位
            support_resistance = indicators.get('support_resistance', {})
            if not support_resistance.get('near_support') and not support_resistance.get('near_resistance'):
                reasons.append("当前价格未接近关键支撑阻力位")
            
            # 检查成交量
            volume_ratio = indicators.get('volume_ratio', 1.0)
            if volume_ratio < 1.1:
                reasons.append("成交量不足，缺乏确认信号")
            
            if not reasons:
                reasons.append("技术条件未满足最低信号要求")
            
            return "; ".join(reasons)
            
        except Exception as e:
            return f"分析失败: {e}"
    
    def _create_no_signal_response(self, reason: str) -> Dict:
        """
        创建无信号响应
        
        Args:
            reason: 无信号的原因
            
        Returns:
            无信号响应字典
        """
        return {
            'has_signal': False,
            'reason': reason,
            'timestamp': datetime.now().isoformat(),
            'philosophy': self.philosophy_mode,
            'suggestion': '继续监控市场，等待更好的入场机会'
        }
    
    def _generate_enhanced_strategy_explanation(self, signal_direction: str, trading_evaluation: Dict) -> str:
        """
        生成增强版策略说明
        
        Args:
            signal_direction: 交易方向
            trading_evaluation: 交易评估结果
            
        Returns:
            策略说明文本
        """
        try:
            explanations = []
            
            # 基础策略说明
            if signal_direction == 'UP':
                explanations.append("🎯 画地为牢做多策略：在技术指标确认的有利位置建立多头仓位")
            else:
                explanations.append("🎯 画地为牢做空策略：在技术指标确认的有利位置建立空头仓位")
            
            # 边界条件说明
            boundary_score = trading_evaluation.get('boundary_score', 0)
            if boundary_score >= 20:
                explanations.append("✅ 边界条件优秀，技术指标强烈支持")
            elif boundary_score >= 10:
                explanations.append("⚠️ 边界条件良好，技术指标适度支持")
            else:
                explanations.append("🚨 边界条件一般，需谨慎操作")
            
            # 概率优势说明
            probability_score = trading_evaluation.get('probability_score', 0)
            if probability_score >= 20:
                explanations.append("📈 概率优势明显，多时间框架共振")
            elif probability_score >= 10:
                explanations.append("📊 概率优势适中，部分时间框架确认")
            else:
                explanations.append("📉 概率优势有限，信号强度不足")
            
            # 风险控制说明
            risk_warnings = trading_evaluation.get('risk_warnings', [])
            if risk_warnings:
                explanations.append(f"⚠️ 风险提示: {'; '.join(risk_warnings)}")
            else:
                explanations.append("✅ 风险控制良好，无特殊风险警告")
            
            return " | ".join(explanations)
            
        except Exception as e:
            return f"策略说明生成失败: {e}"


class RiskManager:
    """风险管理器"""
    
    def __init__(self):
        """初始化风险管理器"""
        self.daily_pnl = 0.0
        self.daily_trades = 0
        self.daily_wins = 0
        self.consecutive_losses = 0
        self.daily_loss_limit = 1000.0
        self.hard_loss_limit = 10000.0
        self.base_position_size = 20.0
        self.last_reset_date = datetime.now().date()
        self.data_file = 'risk_manager.json'
        self.load_data()  # 启动时加载数据

    def record_trade_result(self, amount: float, won: bool):
        """记录交易结果"""
        self._check_daily_reset()

        self.daily_trades += 1
        if won:
            self.daily_wins += 1
            self.daily_pnl += amount * 0.85  # 假设85%的收益率
        else:
            self.daily_pnl -= amount

        self.save_data()  # 自动保存数据

    def _check_daily_reset(self):
        """检查是否需要重置日统计"""
        current_date = datetime.now().date()
        if current_date != self.last_reset_date:
            self.daily_pnl = 0.0
            self.daily_trades = 0
            self.daily_wins = 0
            self.last_reset_date = current_date
            self.save_data()  # 保存重置后的数据

    def _assess_risk_level(self) -> str:
        """评估当前风险等级"""
        if self.daily_pnl <= -800:  # 接近日损失限制
            return 'HIGH'
        elif self.daily_pnl <= -400:
            return 'MEDIUM'
        else:
            return 'LOW'

    def _should_stop_trading(self) -> bool:
        """判断是否应该停止交易"""
        return (self.daily_pnl <= -self.daily_loss_limit or
                self.daily_pnl <= -self.hard_loss_limit or
                self.daily_trades >= 50)  # 单日交易次数限制

    def save_data(self):
        """保存风险管理数据到文件"""
        try:
            data = {
                'daily_pnl': self.daily_pnl,
                'daily_trades': self.daily_trades,
                'daily_wins': self.daily_wins,
                'last_reset_date': self.last_reset_date.isoformat(),
                'daily_loss_limit': self.daily_loss_limit,
                'hard_loss_limit': self.hard_loss_limit,
                'base_position_size': self.base_position_size,
                'last_update': datetime.now().isoformat()
            }

            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"❌ 保存风险管理数据失败: {e}")

    def load_data(self):
        """从文件加载风险管理数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.daily_pnl = data.get('daily_pnl', 0.0)
                self.daily_trades = data.get('daily_trades', 0)
                self.daily_wins = data.get('daily_wins', 0)
                self.daily_loss_limit = data.get('daily_loss_limit', 1000.0)
                self.hard_loss_limit = data.get('hard_loss_limit', 10000.0)
                self.base_position_size = data.get('base_position_size', 20.0)

                # 处理日期
                if 'last_reset_date' in data:
                    self.last_reset_date = datetime.fromisoformat(data['last_reset_date']).date()

                print(f"✅ 加载风险管理数据: 今日盈亏${self.daily_pnl}, 交易{self.daily_trades}次")
            else:
                print("📝 风险管理文件不存在，将创建新文件")

        except Exception as e:
            print(f"❌ 加载风险管理数据失败: {e}")
            # 重置为默认值
            self.daily_pnl = 0.0
            self.daily_trades = 0
            self.daily_wins = 0


class SignalSettlementChecker:
    """信号结算检查器 - 自动检查和结算到期的交易信号"""

    def __init__(self, trade_tracker, risk_manager):
        self.trade_tracker = trade_tracker
        self.risk_manager = risk_manager
        self.last_check_time = datetime.now()

    def check_and_settle_signals(self, current_price: float) -> List[Dict]:
        """
        检查并结算到期的交易信号

        Args:
            current_price: 当前BTC价格

        Returns:
            已结算的交易列表
        """
        current_time = datetime.now()
        settled_trades = []

        try:
            # 查找所有待结算的交易
            pending_trades = [
                trade for trade in self.trade_tracker.trade_history
                if trade['result'] == 'PENDING' and 'expiry_time' in trade
            ]

            for trade in pending_trades:
                # 检查是否到期
                if self._is_trade_expired(trade, current_time):
                    # 执行结算
                    settlement_result = self._settle_trade(trade, current_price, current_time)
                    if settlement_result:
                        settled_trades.append(settlement_result)
                        print(f"🎯 自动结算交易: {trade['trade_id']} | 结果: {settlement_result['result']} | 盈亏: ${settlement_result['pnl']:.2f}")

            self.last_check_time = current_time
            return settled_trades

        except Exception as e:
            print(f"❌ 信号结算检查失败: {e}")
            return []

    def _is_trade_expired(self, trade: Dict, current_time: datetime) -> bool:
        """检查交易是否已到期"""
        try:
            if 'expiry_time' not in trade or not trade['expiry_time']:
                return False

            # 解析到期时间
            expiry_time = datetime.fromisoformat(trade['expiry_time'].replace('Z', '+00:00'))

            # 检查是否已到期（允许30秒的缓冲时间）
            return current_time >= expiry_time

        except Exception as e:
            print(f"❌ 解析到期时间失败: {e}, trade: {trade.get('trade_id')}")
            return False

    def _settle_trade(self, trade: Dict, current_price: float, settlement_time: datetime) -> Dict:
        """
        结算单个交易

        Args:
            trade: 交易记录
            current_price: 当前价格
            settlement_time: 结算时间

        Returns:
            结算结果字典
        """
        try:
            signal_price = trade.get('signal_price', 0)
            direction = trade.get('direction')
            position_size = trade.get('position_size', 0)

            if not signal_price or not direction:
                print(f"❌ 交易数据不完整: {trade.get('trade_id')}")
                return None

            # 判断胜负
            if direction == 'UP':
                is_win = current_price > signal_price
            elif direction == 'DOWN':
                is_win = current_price < signal_price
            else:
                print(f"❌ 未知的交易方向: {direction}")
                return None

            # 计算盈亏
            if is_win:
                # 胜利：获得85%的收益
                pnl = position_size * 0.85
                result = 'WIN'
            else:
                # 失败：损失全部投注金额
                pnl = -position_size
                result = 'LOSS'

            # 更新交易记录
            self.trade_tracker.update_trade_result(
                trade['trade_id'],
                result,
                pnl,
                settlement_price=current_price,
                settlement_time=settlement_time.isoformat(),
                auto_settled=True
            )

            # 更新风险管理统计
            self.risk_manager.record_trade_result(position_size, is_win)

            return {
                'trade_id': trade['trade_id'],
                'direction': direction,
                'signal_price': signal_price,
                'settlement_price': current_price,
                'result': result,
                'pnl': pnl,
                'position_size': position_size,
                'settlement_time': settlement_time.isoformat(),
                'auto_settled': True
            }

        except Exception as e:
            print(f"❌ 结算交易失败: {e}, trade: {trade.get('trade_id')}")
            return None

    def get_pending_trades_count(self) -> int:
        """获取待结算交易数量"""
        return len([
            trade for trade in self.trade_tracker.trade_history
            if trade['result'] == 'PENDING'
        ])

    def get_settlement_stats(self) -> Dict:
        """获取结算统计信息"""
        auto_settled_trades = [
            trade for trade in self.trade_tracker.trade_history
            if trade.get('auto_settled', False)
        ]

        total_auto_settled = len(auto_settled_trades)
        auto_wins = len([t for t in auto_settled_trades if t['result'] == 'WIN'])
        auto_losses = len([t for t in auto_settled_trades if t['result'] == 'LOSS'])

        return {
            'total_auto_settled': total_auto_settled,
            'auto_wins': auto_wins,
            'auto_losses': auto_losses,
            'auto_win_rate': (auto_wins / total_auto_settled * 100) if total_auto_settled > 0 else 0,
            'pending_trades': self.get_pending_trades_count(),
            'last_check_time': self.last_check_time.isoformat()
        }


class TradeHistoryTracker:
    """交易历史跟踪器"""

    def __init__(self):
        self.trade_history = []
        self.signal_performance = {}  # 信号表现统计
        self.data_file = 'trade_history.json'  # 数据持久化文件
        self._trade_counter = 0  # 交易计数器，确保ID唯一
        self.load_data()  # 启动时加载历史数据

    def add_trade_record(self, signal: Dict, position_size: float, signal_price: float, result: str = 'PENDING'):
        """添加交易记录"""
        self._trade_counter += 1
        trade_record = {
            'trade_id': f"trade_{int(datetime.now().timestamp())}_{self._trade_counter}",
            'signal_id': signal.get('signal_id'),
            'timestamp': datetime.now().isoformat(),
            'direction': signal.get('direction'),
            'confidence': signal.get('confidence'),
            'signal_strength': signal.get('signal_strength'),
            'position_size': position_size,
            'supporting_indicators': signal.get('supporting_indicators', []),
            'supporting_timeframes': signal.get('supporting_timeframes', []),
            'expected_win_rate': signal.get('expected_win_rate'),
            'result': result,  # 'PENDING', 'WIN', 'LOSS'
            'actual_pnl': 0.0,
            'expiry_time': signal.get('valid_until'),
            # 新增字段用于自动结算
            'signal_price': signal_price,  # 信号生成时的价格
            'settlement_price': None,      # 结算时的价格
            'settlement_time': None,       # 实际结算时间
            'auto_settled': False          # 是否自动结算
        }

        self.trade_history.append(trade_record)
        self.save_data()  # 自动保存数据
        return trade_record

    def update_trade_result(self, trade_id: str, result: str, pnl: float,
                           settlement_price: float = None, settlement_time: str = None,
                           auto_settled: bool = False):
        """更新交易结果"""
        for trade in self.trade_history:
            if trade['trade_id'] == trade_id:
                trade['result'] = result
                trade['actual_pnl'] = pnl
                trade['completion_time'] = datetime.now().isoformat()

                # 更新新增字段
                if settlement_price is not None:
                    trade['settlement_price'] = settlement_price
                if settlement_time is not None:
                    trade['settlement_time'] = settlement_time
                trade['auto_settled'] = auto_settled

                # 更新信号表现统计
                self._update_signal_performance(trade)
                self.save_data()  # 自动保存数据
                break

    def get_historical_win_rates(self, direction: str = None, strength: str = None,
                                lookback_days: int = 30) -> Dict:
        """
        获取历史胜率数据，用于胜率预测模型校准

        Args:
            direction: 交易方向过滤 ('UP', 'DOWN', None表示所有)
            strength: 信号强度过滤 ('STRONG', 'MEDIUM', 'WEAK', None表示所有)
            lookback_days: 回看天数

        Returns:
            包含历史胜率统计的字典
        """
        cutoff_date = datetime.now() - timedelta(days=lookback_days)

        # 过滤交易记录
        filtered_trades = []
        for trade in self.trade_history:
            try:
                # 检查时间范围
                trade_time = datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00'))
                if trade_time <= cutoff_date:
                    continue

                # 检查交易状态
                if trade['result'] not in ['WIN', 'LOSS']:
                    continue

                # 检查方向过滤
                if direction and trade.get('direction') != direction:
                    continue

                # 检查强度过滤
                if strength and trade.get('signal_strength') != strength:
                    continue

                filtered_trades.append(trade)

            except Exception as e:
                continue  # 跳过有问题的记录

        if not filtered_trades:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'wins': 0,
                'losses': 0,
                'avg_expected_win_rate': 0.0,
                'prediction_accuracy': 0.0
            }

        # 计算统计数据
        wins = [t for t in filtered_trades if t['result'] == 'WIN']
        losses = [t for t in filtered_trades if t['result'] == 'LOSS']

        win_rate = len(wins) / len(filtered_trades) * 100

        # 计算预期胜率的平均值
        expected_rates = [t.get('expected_win_rate', 0) for t in filtered_trades if t.get('expected_win_rate')]
        avg_expected_win_rate = sum(expected_rates) / len(expected_rates) if expected_rates else 0

        # 计算预测准确性（实际胜率与预期胜率的偏差）
        prediction_accuracy = 100 - abs(win_rate - avg_expected_win_rate) if avg_expected_win_rate > 0 else 0

        return {
            'total_trades': len(filtered_trades),
            'win_rate': round(win_rate, 2),
            'wins': len(wins),
            'losses': len(losses),
            'avg_expected_win_rate': round(avg_expected_win_rate, 2),
            'prediction_accuracy': round(prediction_accuracy, 2),
            'direction': direction,
            'strength': strength,
            'lookback_days': lookback_days
        }

    def get_sliding_window_validation(self, window_size: int = 20, step_size: int = 5) -> Dict:
        """
        滑动窗口验证，持续监控预测准确性

        Args:
            window_size: 窗口大小（交易数量）
            step_size: 步长（每次移动的交易数量）

        Returns:
            滑动窗口验证结果
        """
        if len(self.trade_history) < window_size:
            return {
                'validation_results': [],
                'overall_accuracy': 0.0,
                'trend': 'insufficient_data',
                'recommendation': 'need_more_data'
            }

        # 只考虑已完成的交易
        completed_trades = [t for t in self.trade_history if t['result'] in ['WIN', 'LOSS']]

        if len(completed_trades) < window_size:
            return {
                'validation_results': [],
                'overall_accuracy': 0.0,
                'trend': 'insufficient_data',
                'recommendation': 'need_more_data'
            }

        validation_results = []

        # 滑动窗口分析
        for i in range(0, len(completed_trades) - window_size + 1, step_size):
            window_trades = completed_trades[i:i + window_size]

            # 计算窗口内的统计数据
            wins = len([t for t in window_trades if t['result'] == 'WIN'])
            actual_win_rate = wins / len(window_trades) * 100

            # 计算预期胜率的平均值
            expected_rates = [t.get('expected_win_rate', 0) for t in window_trades if t.get('expected_win_rate')]
            avg_expected_win_rate = sum(expected_rates) / len(expected_rates) if expected_rates else 0

            # 计算预测偏差
            prediction_bias = actual_win_rate - avg_expected_win_rate

            # 计算质量评分的平均值（如果有的话）
            quality_scores = [t.get('quality_score', 0) for t in window_trades if t.get('quality_score')]
            avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0

            validation_results.append({
                'window_start': i,
                'window_end': i + window_size - 1,
                'actual_win_rate': round(actual_win_rate, 2),
                'expected_win_rate': round(avg_expected_win_rate, 2),
                'prediction_bias': round(prediction_bias, 2),
                'avg_quality_score': round(avg_quality_score, 2),
                'total_trades': len(window_trades),
                'wins': wins,
                'losses': len(window_trades) - wins
            })

        # 分析整体趋势
        if len(validation_results) >= 3:
            recent_bias = [r['prediction_bias'] for r in validation_results[-3:]]
            avg_recent_bias = sum(recent_bias) / len(recent_bias)

            # 判断趋势
            if abs(avg_recent_bias) <= 5:
                trend = 'stable'
                recommendation = 'maintain_current_settings'
            elif avg_recent_bias > 10:
                trend = 'overestimating'
                recommendation = 'reduce_win_rate_expectations'
            elif avg_recent_bias < -10:
                trend = 'underestimating'
                recommendation = 'increase_win_rate_expectations'
            else:
                trend = 'minor_bias'
                recommendation = 'minor_adjustments_needed'
        else:
            trend = 'insufficient_windows'
            recommendation = 'need_more_data'

        # 计算整体准确性
        all_biases = [abs(r['prediction_bias']) for r in validation_results]
        overall_accuracy = 100 - (sum(all_biases) / len(all_biases)) if all_biases else 0

        return {
            'validation_results': validation_results,
            'overall_accuracy': round(max(0, overall_accuracy), 2),
            'trend': trend,
            'recommendation': recommendation,
            'window_size': window_size,
            'step_size': step_size,
            'total_windows': len(validation_results)
        }

    def get_performance_stats(self, days: int = 30) -> Dict:
        """获取表现统计"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_trades = [
            trade for trade in self.trade_history
            if datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00')) > cutoff_date
            and trade['result'] in ['WIN', 'LOSS']
        ]

        if not recent_trades:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'total_pnl': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0
            }

        wins = [t for t in recent_trades if t['result'] == 'WIN']
        losses = [t for t in recent_trades if t['result'] == 'LOSS']

        total_pnl = sum(trade['actual_pnl'] for trade in recent_trades)
        win_rate = len(wins) / len(recent_trades) if recent_trades else 0

        avg_win = sum(trade['actual_pnl'] for trade in wins) / len(wins) if wins else 0
        avg_loss = sum(trade['actual_pnl'] for trade in losses) / len(losses) if losses else 0

        # 计算最大回撤
        cumulative_pnl = []
        running_total = 0
        for trade in sorted(recent_trades, key=lambda x: x['timestamp']):
            running_total += trade['actual_pnl']
            cumulative_pnl.append(running_total)

        max_drawdown = 0
        if cumulative_pnl:
            peak = cumulative_pnl[0]
            for value in cumulative_pnl:
                if value > peak:
                    peak = value
                drawdown = peak - value
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

        # 简化的夏普比率计算
        if len(recent_trades) > 1:
            returns = [trade['actual_pnl'] for trade in recent_trades]
            avg_return = sum(returns) / len(returns)
            std_return = (sum((r - avg_return) ** 2 for r in returns) / len(returns)) ** 0.5
            sharpe_ratio = avg_return / std_return if std_return > 0 else 0
        else:
            sharpe_ratio = 0

        return {
            'total_trades': len(recent_trades),
            'win_rate': round(win_rate * 100, 2),
            'total_pnl': round(total_pnl, 2),
            'avg_win': round(avg_win, 2),
            'avg_loss': round(avg_loss, 2),
            'max_drawdown': round(max_drawdown, 2),
            'sharpe_ratio': round(sharpe_ratio, 3),
            'wins': len(wins),
            'losses': len(losses)
        }

    def _update_signal_performance(self, trade: Dict):
        """更新信号表现统计"""
        signal_strength = trade['signal_strength']
        if signal_strength not in self.signal_performance:
            self.signal_performance[signal_strength] = {
                'total': 0,
                'wins': 0,
                'total_pnl': 0.0
            }

        self.signal_performance[signal_strength]['total'] += 1
        if trade['result'] == 'WIN':
            self.signal_performance[signal_strength]['wins'] += 1
        self.signal_performance[signal_strength]['total_pnl'] += trade['actual_pnl']

    def export_trade_history(self, start_date: str = None, end_date: str = None) -> List[Dict]:
        """导出交易历史"""
        filtered_history = self.trade_history

        if start_date:
            start_dt = datetime.fromisoformat(start_date)
            filtered_history = [
                trade for trade in filtered_history
                if datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00')) >= start_dt
            ]

        if end_date:
            end_dt = datetime.fromisoformat(end_date)
            filtered_history = [
                trade for trade in filtered_history
                if datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00')) <= end_dt
            ]

        return filtered_history

    def save_data(self):
        """保存数据到文件"""
        try:
            data = {
                'trade_history': self.trade_history,
                'signal_performance': self.signal_performance,
                'last_update': datetime.now().isoformat()
            }

            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"❌ 保存交易历史数据失败: {e}")

    def load_data(self):
        """从文件加载数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.trade_history = data.get('trade_history', [])
                self.signal_performance = data.get('signal_performance', {})

                # 清理最早的两个待结算记录（如果存在）
                self._clean_old_pending_trades()

                print(f"✅ 加载交易历史数据: {len(self.trade_history)}条记录")
            else:
                print("📝 交易历史文件不存在，将创建新文件")

        except Exception as e:
            print(f"❌ 加载交易历史数据失败: {e}")
            self.trade_history = []
            self.signal_performance = {}

    def _clean_old_pending_trades(self):
        """清理最早的两个待结算记录"""
        try:
            # 找到所有待结算的交易，按时间排序
            pending_trades = [
                trade for trade in self.trade_history
                if trade.get('result') == 'PENDING'
            ]

            if len(pending_trades) >= 2:
                # 按时间戳排序，找到最早的两个
                pending_trades.sort(key=lambda x: x.get('timestamp', ''))
                oldest_two = pending_trades[:2]

                # 检查是否是特定的两个待结算记录
                target_ids = ['trade_1751186048_1', 'trade_1751188267_2']

                for trade in oldest_two:
                    if trade.get('trade_id') in target_ids:
                        # 将其标记为已取消，而不是删除
                        trade['result'] = 'CANCELLED'
                        trade['actual_pnl'] = 0.0
                        trade['completion_time'] = datetime.now().isoformat()
                        trade['settlement_note'] = '系统清理：历史待结算记录'
                        print(f"🧹 清理待结算记录: {trade['trade_id']}")

                # 保存更改
                self.save_data()

        except Exception as e:
            print(f"⚠️ 清理待结算记录时出错: {e}")

    def get_last_signal_result(self) -> str:
        """
        获取上一个信号的交易结果

        Returns:
            str: 'WIN', 'LOSS', 'PENDING', 'FIRST_SIGNAL'
        """
        try:
            if not self.trade_history:
                return 'FIRST_SIGNAL'

            # 按时间戳排序，获取最新的交易记录
            sorted_trades = sorted(
                self.trade_history,
                key=lambda x: x.get('timestamp', ''),
                reverse=True
            )

            if not sorted_trades:
                return 'FIRST_SIGNAL'

            # 获取最新的交易结果
            last_trade = sorted_trades[0]
            result = last_trade.get('result', 'PENDING')

            # 如果是取消的交易，查找下一个
            if result == 'CANCELLED' and len(sorted_trades) > 1:
                last_trade = sorted_trades[1]
                result = last_trade.get('result', 'PENDING')

            return result

        except Exception as e:
            print(f"⚠️ 获取上一个信号结果时出错: {e}")
            return 'FIRST_SIGNAL'

    def get_last_signal_details(self) -> Dict:
        """
        获取上一个信号的详细信息

        Returns:
            Dict: 包含上一个信号详细信息的字典
        """
        try:
            if not self.trade_history:
                return {
                    'result': 'FIRST_SIGNAL',
                    'display_text': '首次信号',
                    'details': '这是系统生成的第一个交易信号'
                }

            # 按时间戳排序，获取最新的交易记录
            sorted_trades = sorted(
                self.trade_history,
                key=lambda x: x.get('timestamp', ''),
                reverse=True
            )

            if not sorted_trades:
                return {
                    'result': 'FIRST_SIGNAL',
                    'display_text': '首次信号',
                    'details': '这是系统生成的第一个交易信号'
                }

            # 获取最新的交易
            last_trade = sorted_trades[0]
            result = last_trade.get('result', 'PENDING')

            # 如果是取消的交易，查找下一个
            if result == 'CANCELLED' and len(sorted_trades) > 1:
                last_trade = sorted_trades[1]
                result = last_trade.get('result', 'PENDING')

            # 构建显示信息
            if result == 'WIN':
                pnl = last_trade.get('actual_pnl', 0)
                display_text = f'WIN (+${pnl:.2f})'
                details = f"上次交易盈利 ${pnl:.2f}"
            elif result == 'LOSS':
                pnl = last_trade.get('actual_pnl', 0)
                display_text = f'LOSS (${pnl:.2f})'
                details = f"上次交易亏损 ${abs(pnl):.2f}"
            elif result == 'PENDING':
                display_text = 'PENDING'
                details = "上一个信号仍在等待结算"
            else:
                display_text = '待定'
                details = "上一个信号状态未知"

            return {
                'result': result,
                'display_text': display_text,
                'details': details,
                'trade_id': last_trade.get('trade_id', ''),
                'direction': last_trade.get('direction', ''),
                'timestamp': last_trade.get('timestamp', '')
            }

        except Exception as e:
            print(f"⚠️ 获取上一个信号详细信息时出错: {e}")
            return {
                'result': 'FIRST_SIGNAL',
                'display_text': '首次信号',
                'details': '这是系统生成的第一个交易信号'
            }

    @staticmethod
    def calculate_fibonacci_levels(highs: List[float], lows: List[float], period: int = 20) -> Dict:
        """
        计算斐波那契回调位

        Args:
            highs: 最高价序列
            lows: 最低价序列
            period: 计算周期

        Returns:
            包含斐波那契回调位的字典
        """
        if len(highs) < period or len(lows) < period:
            return {
                'high': 0,
                'low': 0,
                'fib_23.6': 0,
                'fib_38.2': 0,
                'fib_50.0': 0,
                'fib_61.8': 0,
                'fib_78.6': 0,
                'trend_direction': 'neutral'
            }

        # 获取最近周期内的最高点和最低点
        recent_highs = highs[-period:]
        recent_lows = lows[-period:]

        swing_high = max(recent_highs)
        swing_low = min(recent_lows)

        # 计算价格范围
        price_range = swing_high - swing_low

        if price_range == 0:
            return {
                'high': swing_high,
                'low': swing_low,
                'fib_23.6': swing_high,
                'fib_38.2': swing_high,
                'fib_50.0': swing_high,
                'fib_61.8': swing_high,
                'fib_78.6': swing_high,
                'trend_direction': 'neutral'
            }

        # 斐波那契回调位计算
        fib_levels = {
            'high': swing_high,
            'low': swing_low,
            'fib_23.6': swing_high - (price_range * 0.236),
            'fib_38.2': swing_high - (price_range * 0.382),
            'fib_50.0': swing_high - (price_range * 0.500),
            'fib_61.8': swing_high - (price_range * 0.618),
            'fib_78.6': swing_high - (price_range * 0.786),
        }

        # 判断趋势方向（基于最近价格相对于斐波那契中位的位置）
        current_price = highs[-1] if highs else swing_high
        if current_price > fib_levels['fib_50.0']:
            trend_direction = 'bullish'
        elif current_price < fib_levels['fib_50.0']:
            trend_direction = 'bearish'
        else:
            trend_direction = 'neutral'

        fib_levels['trend_direction'] = trend_direction

        return fib_levels

    @staticmethod
    def identify_support_resistance_levels(highs: List[float], lows: List[float], closes: List[float],
                                         period: int = 20, tolerance: float = 0.002) -> Dict:
        """
        识别支撑阻力位

        Args:
            highs: 最高价序列
            lows: 最低价序列
            closes: 收盘价序列
            period: 分析周期
            tolerance: 价格容忍度（百分比）

        Returns:
            包含支撑阻力位信息的字典
        """
        if len(highs) < period or len(lows) < period or len(closes) < period:
            return {
                'support_levels': [],
                'resistance_levels': [],
                'near_support': False,
                'near_resistance': False,
                'approaching_support': False,
                'approaching_resistance': False,
                'support_strength': 0,
                'resistance_strength': 0
            }

        current_price = closes[-1]
        recent_highs = highs[-period:]
        recent_lows = lows[-period:]

        # 寻找局部高点和低点
        resistance_levels = []
        support_levels = []

        # 识别前期高点作为阻力位
        for i in range(2, len(recent_highs) - 2):
            if (recent_highs[i] > recent_highs[i-1] and recent_highs[i] > recent_highs[i-2] and
                recent_highs[i] > recent_highs[i+1] and recent_highs[i] > recent_highs[i+2]):
                resistance_levels.append(recent_highs[i])

        # 识别前期低点作为支撑位
        for i in range(2, len(recent_lows) - 2):
            if (recent_lows[i] < recent_lows[i-1] and recent_lows[i] < recent_lows[i-2] and
                recent_lows[i] < recent_lows[i+1] and recent_lows[i] < recent_lows[i+2]):
                support_levels.append(recent_lows[i])

        # 去重并排序
        resistance_levels = sorted(list(set(resistance_levels)), reverse=True)
        support_levels = sorted(list(set(support_levels)))

        # 检查当前价格是否接近支撑阻力位
        near_support = False
        near_resistance = False
        approaching_support = False
        approaching_resistance = False
        support_strength = 0
        resistance_strength = 0

        # 检查支撑位
        for support in support_levels:
            distance_pct = abs(current_price - support) / current_price
            if distance_pct <= tolerance:  # 非常接近
                near_support = True
                support_strength += 1
            elif distance_pct <= tolerance * 2:  # 接近
                approaching_support = True

        # 检查阻力位
        for resistance in resistance_levels:
            distance_pct = abs(current_price - resistance) / current_price
            if distance_pct <= tolerance:  # 非常接近
                near_resistance = True
                resistance_strength += 1
            elif distance_pct <= tolerance * 2:  # 接近
                approaching_resistance = True

        return {
            'support_levels': support_levels[-3:],  # 最近3个支撑位
            'resistance_levels': resistance_levels[:3],  # 最近3个阻力位
            'near_support': near_support,
            'near_resistance': near_resistance,
            'approaching_support': approaching_support,
            'approaching_resistance': approaching_resistance,
            'support_strength': support_strength,
            'resistance_strength': resistance_strength
        }

    @staticmethod
    def detect_sharp_downturn(closes: List[float], volumes: List[float], macd_line: float,
                            macd_signal: float, ema_20: float, ema_50: float,
                            current_price: float) -> Dict:
        """
        检测急剧下跌行情 - 用于阻止在暴跌期间开多仓

        Args:
            closes: 收盘价序列
            volumes: 成交量序列
            macd_line: MACD线值
            macd_signal: MACD信号线值
            ema_20: EMA20值
            ema_50: EMA50值
            current_price: 当前价格

        Returns:
            包含急剧下跌检测结果的字典
        """
        downturn_signals = []
        downturn_severity = 0
        is_sharp_downturn = False

        if len(closes) < 10 or len(volumes) < 10:
            return {
                'is_sharp_downturn': False,
                'downturn_severity': 0,
                'downturn_signals': [],
                'block_long_positions': False,
                'cooling_off_minutes': 0
            }

        try:
            # 1. 检测快速价格下跌 (5分钟内跌幅 > 3% - 优化后阈值)
            if len(closes) >= 10:  # 5分钟 = 10个30秒周期
                price_5min_ago = closes[-10]
                price_decline_5min = (current_price - price_5min_ago) / price_5min_ago * 100

                if price_decline_5min < -3.0:  # 优化: 提高到3%减少误触发
                    downturn_signals.append(f'🔴 5分钟急跌 {price_decline_5min:.2f}%')
                    downturn_severity += 30
                elif price_decline_5min < -1.0:  # 5分钟内跌超1%
                    downturn_signals.append(f'⚠️ 5分钟快跌 {price_decline_5min:.2f}%')
                    downturn_severity += 15

            # 2. 检测连续下跌蜡烛
            if len(closes) >= 6:
                consecutive_red = 0
                for i in range(1, min(6, len(closes))):
                    if closes[-i] < closes[-i-1]:
                        consecutive_red += 1
                    else:
                        break

                if consecutive_red >= 5:  # 连续5根阴线
                    downturn_signals.append(f'📉 连续{consecutive_red}根阴线')
                    downturn_severity += 25
                elif consecutive_red >= 3:  # 连续3根阴线
                    downturn_signals.append(f'📉 连续{consecutive_red}根阴线')
                    downturn_severity += 15

            # 3. MACD强势看跌确认
            macd_histogram = macd_line - macd_signal
            if macd_line < macd_signal and macd_histogram < -0.01:
                if macd_line < 0:  # 零轴下方死叉
                    downturn_signals.append('💥 MACD零轴下方强势死叉')
                    downturn_severity += 25
                else:
                    downturn_signals.append('📉 MACD死叉确认')
                    downturn_severity += 15

            # 4. EMA死叉 + 价格跌破确认
            if ema_20 < ema_50 and current_price < ema_20:
                downturn_signals.append('⚡ EMA死叉+价格跌破')
                downturn_severity += 20
            elif current_price < ema_20:
                downturn_signals.append('📊 价格跌破EMA20')
                downturn_severity += 10

            # 5. 成交量放大确认 (恐慌性抛售)
            if len(volumes) >= 5:
                recent_avg_volume = np.mean(volumes[-5:])
                historical_avg_volume = np.mean(volumes[-20:-5]) if len(volumes) >= 20 else np.mean(volumes[:-5])

                if historical_avg_volume > 0:
                    volume_ratio = recent_avg_volume / historical_avg_volume
                    if volume_ratio > 2.0:  # 成交量放大2倍以上
                        downturn_signals.append(f'🚨 恐慌性抛售 (量比{volume_ratio:.1f})')
                        downturn_severity += 20
                    elif volume_ratio > 1.5:  # 成交量放大1.5倍以上
                        downturn_signals.append(f'⚠️ 成交量放大 (量比{volume_ratio:.1f})')
                        downturn_severity += 10

            # 6. 波动率异常检测
            if len(closes) >= 20:
                recent_volatility = np.std(closes[-10:]) / np.mean(closes[-10:]) * 100
                historical_volatility = np.std(closes[-20:-10]) / np.mean(closes[-20:-10]) * 100

                if historical_volatility > 0:
                    volatility_ratio = recent_volatility / historical_volatility
                    if volatility_ratio > 2.5:  # 波动率异常放大
                        downturn_signals.append(f'🌪️ 波动率异常 (比值{volatility_ratio:.1f})')
                        downturn_severity += 15

            # 综合判断 - 优化冷却时间
            if downturn_severity >= 70:
                is_sharp_downturn = True
                cooling_off_minutes = 20  # 优化: 严重下跌，冷却20分钟
            elif downturn_severity >= 50:
                is_sharp_downturn = True
                cooling_off_minutes = 10  # 优化: 中等下跌，冷却10分钟
            else:
                cooling_off_minutes = 0

            return {
                'is_sharp_downturn': is_sharp_downturn,
                'downturn_severity': downturn_severity,
                'downturn_signals': downturn_signals,
                'block_long_positions': downturn_severity >= 50,  # 优化: 提高到50分减少过度阻断
                'cooling_off_minutes': cooling_off_minutes,
                'price_decline_5min': price_decline_5min if 'price_decline_5min' in locals() else 0
            }

        except Exception as e:
            print(f"⚠️ 急剧下跌检测失败: {e}")
            return {
                'is_sharp_downturn': False,
                'downturn_severity': 0,
                'downturn_signals': [f'检测失败: {e}'],
                'block_long_positions': False,
                'cooling_off_minutes': 0
            }

    @staticmethod
    def detect_market_recovery(closes: List[float], volumes: List[float],
                             current_price: float, crash_timestamp: float = None) -> Dict:
        """
        检测市场恢复状态 - 用于提前结束冷却期

        Args:
            closes: 收盘价序列
            volumes: 成交量序列
            current_price: 当前价格
            crash_timestamp: 崩盘开始时间戳

        Returns:
            包含市场恢复检测结果的字典
        """
        recovery_signals = []
        recovery_strength = 0
        is_recovering = False

        if len(closes) < 10:
            return {
                'is_recovering': False,
                'recovery_strength': 0,
                'recovery_signals': [],
                'early_exit_cooling': False
            }

        try:
            # 1. 检测连续上涨信号
            if len(closes) >= 5:
                consecutive_green = 0
                for i in range(1, min(5, len(closes))):
                    if closes[-i] > closes[-i-1]:
                        consecutive_green += 1
                    else:
                        break

                if consecutive_green >= 3:  # 连续3根阳线
                    recovery_signals.append(f'📈 连续{consecutive_green}根阳线')
                    recovery_strength += 20

            # 2. 检测价格恢复幅度
            if len(closes) >= 20:
                recent_low = min(closes[-10:])
                recovery_pct = (current_price - recent_low) / recent_low * 100

                if recovery_pct > 2.0:  # 恢复超过2%
                    recovery_signals.append(f'🚀 价格恢复 {recovery_pct:.1f}%')
                    recovery_strength += 25
                elif recovery_pct > 1.0:  # 恢复超过1%
                    recovery_signals.append(f'📊 价格回升 {recovery_pct:.1f}%')
                    recovery_strength += 15

            # 3. 检测成交量稳定化
            if len(volumes) >= 10:
                recent_avg_volume = np.mean(volumes[-5:])
                previous_avg_volume = np.mean(volumes[-10:-5])

                if previous_avg_volume > 0:
                    volume_ratio = recent_avg_volume / previous_avg_volume
                    if volume_ratio < 0.8:  # 成交量回落，恐慌情绪缓解
                        recovery_signals.append(f'🔽 成交量回落 (比值{volume_ratio:.1f})')
                        recovery_strength += 15

            # 4. 检测波动率降低
            if len(closes) >= 20:
                recent_volatility = np.std(closes[-10:]) / np.mean(closes[-10:]) * 100
                historical_volatility = np.std(closes[-20:-10]) / np.mean(closes[-20:-10]) * 100

                if historical_volatility > 0:
                    volatility_ratio = recent_volatility / historical_volatility
                    if volatility_ratio < 0.7:  # 波动率降低
                        recovery_signals.append(f'🌊 波动率降低 (比值{volatility_ratio:.1f})')
                        recovery_strength += 10

            # 5. 检测技术指标改善
            if len(closes) >= 5:
                # 简单的趋势检测
                trend_improving = all(closes[-i] >= closes[-i-1] for i in range(1, 4))
                if trend_improving:
                    recovery_signals.append('📈 短期趋势改善')
                    recovery_strength += 15

            # 综合判断恢复状态
            if recovery_strength >= 50:
                is_recovering = True
                early_exit_cooling = True
            elif recovery_strength >= 30:
                is_recovering = True
                early_exit_cooling = False
            else:
                early_exit_cooling = False

            return {
                'is_recovering': is_recovering,
                'recovery_strength': recovery_strength,
                'recovery_signals': recovery_signals,
                'early_exit_cooling': early_exit_cooling
            }

        except Exception as e:
            print(f"⚠️ 市场恢复检测失败: {e}")
            return {
                'is_recovering': False,
                'recovery_strength': 0,
                'recovery_signals': [f'检测失败: {e}'],
                'early_exit_cooling': False
            }


class WebPricePredictor:
    """Web价格预测器"""
    
    def __init__(self):
        self.host = '127.0.0.1'
        self.port = 5000
        self.debug = True
        
        # 初始化各种管理器
        self.risk_manager = RiskManager()
        self.trade_tracker = TradeHistoryTracker()
        self.signal_generator = EventContractSignalGenerator(self.trade_tracker)
        self.signal_checker = SignalSettlementChecker(self.trade_tracker, self.risk_manager)
        
        # 新增：实时数据管理器
        self.data_manager = RealTimeDataManager()
        
        # 初始化Flask应用
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'your_secret_key_here'
        
        # 设置路由
        self._setup_routes()
        
        # 启动实时数据更新
        self.data_manager.start_real_time_updates()
        
    def _setup_routes(self):
        """设置Web路由"""
        @self.app.route('/')
        def index():
            return render_template('predictor_dashboard.html')
            
        @self.app.route('/api/status')
        def status():
            return jsonify({
                'status': 'running',
                'timestamp': datetime.now().isoformat(),
                'system': '币安事件合约信号生成系统'
            })
        
        @self.app.route('/api/real_time_data')
        def get_real_time_data():
            """获取实时数据流"""
            try:
                # 获取实时数据
                current_data = self.data_manager.get_current_data()
                
                # 计算技术指标
                indicators = self.data_manager.calculate_technical_indicators()
                
                # 扩展指标数据
                extended_indicators = self._extend_indicators(indicators, current_data)
                
                # 构造多时间框架分析（模拟）
                multi_analysis = self._create_multi_timeframe_analysis(indicators)
                
                return jsonify({
                    'success': True,
                    'data': {
                        'market_data': current_data,
                        'technical_indicators': extended_indicators,
                        'multi_timeframe_analysis': multi_analysis,
                        'data_source': current_data.get('data_source', '未知'),
                        'is_real_data': current_data.get('is_real_data', False),
                        'update_timestamp': datetime.now().isoformat()
                    }
                })
                
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': f'获取实时数据失败: {str(e)}',
                    'timestamp': datetime.now().isoformat()
                }), 500
        
        @self.app.route('/api/generate_live_signal', methods=['POST'])
        def generate_live_signal():
            """基于实时数据生成交易信号"""
            try:
                # 获取实时数据
                current_data = self.data_manager.get_current_data()
                if current_data['current_price'] == 0:
                    return jsonify({
                        'success': False,
                        'error': '实时数据未准备好，请稍后再试',
                        'timestamp': datetime.now().isoformat()
                    }), 400
                
                # 计算技术指标
                indicators = self.data_manager.calculate_technical_indicators()
                extended_indicators = self._extend_indicators(indicators, current_data)
                
                # 构造多时间框架分析
                multi_analysis = self._create_multi_timeframe_analysis(indicators)
                
                # 生成交易信号
                signal_result = self.signal_generator.generate_signal(
                    {'multi_timeframe_analysis': multi_analysis, 'summary': {}}, 
                    extended_indicators
                )
                
                # 如果有有效信号，记录到交易历史
                if signal_result.get('has_signal', False):
                    # 添加交易记录
                    position_size = 20.0  # 默认仓位大小
                    trade_record = self.trade_tracker.add_trade_record(
                        signal_result, 
                        position_size, 
                        current_data['current_price']
                    )
                    
                    # 发送DingTalk通知
                    if signal_result and signal_result.get('has_signal'):
                        try:
                            send_dingtalk_trading_signal(
                                signal_result, 
                                current_data['current_price'], 
                                self.trade_tracker
                            )
                        except Exception as e:
                            print(f"⚠️ DingTalk通知发送失败: {e}")
                    
                    signal_result['trade_record'] = trade_record
                
                # 组合响应
                response = {
                    'success': True,
                    'signal': signal_result,
                    'market_data': current_data,
                    'technical_indicators': extended_indicators,
                    'dice_probability': self.signal_generator.calculate_dice_probability(
                        current_data['current_price'], extended_indicators
                    ),
                    'timestamp': datetime.now().isoformat()
                }
                
                return jsonify(response)
                
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': f'生成实时信号失败: {str(e)}',
                    'timestamp': datetime.now().isoformat()
                }), 500
        
        @self.app.route('/api/market_status')
        def get_market_status():
            """获取市场状态摘要"""
            try:
                current_data = self.data_manager.get_current_data()
                indicators = self.data_manager.calculate_technical_indicators()
                
                # 计算市场状态
                market_status = self._analyze_market_status(indicators, current_data)
                
                return jsonify({
                    'success': True,
                    'market_status': market_status,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': f'获取市场状态失败: {str(e)}',
                    'timestamp': datetime.now().isoformat()
                }), 500
        
        @self.app.route('/api/event_contract_signal', methods=['POST'])
        def generate_event_contract_signal():
            """生成事件合约交易信号"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': '缺少请求数据'}), 400
                
                # 获取必要的参数
                current_price = data.get('current_price')
                indicators = data.get('indicators', {})
                multi_analysis = data.get('multi_analysis', {})
                
                if not current_price:
                    return jsonify({'error': '缺少当前价格'}), 400
                
                # 生成信号
                signal_result = self.signal_generator.generate_signal(multi_analysis, indicators)
                
                # 添加额外的分析
                dice_result = self.signal_generator.calculate_dice_probability(current_price, indicators)
                minute_analysis = self.signal_generator.analyze_minute_probabilities(current_price, indicators)
                dimension_classification = self.signal_generator.classify_signal_dimension(current_price, indicators, multi_analysis)
                
                # 组合完整的响应
                response = {
                    'signal': signal_result,
                    'dice_probability': dice_result,
                    'minute_analysis': minute_analysis,
                    'dimension_classification': dimension_classification,
                    'timestamp': datetime.now().isoformat()
                }
                
                return jsonify(response)
                
            except Exception as e:
                return jsonify({'error': f'生成信号失败: {str(e)}'}), 500
        
        @self.app.route('/api/dice_probability', methods=['POST'])
        def calculate_dice_probability():
            """计算六面骰子概率"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': '缺少请求数据'}), 400
                
                current_price = data.get('current_price')
                indicators = data.get('indicators', {})
                
                if not current_price:
                    return jsonify({'error': '缺少当前价格'}), 400
                
                result = self.signal_generator.calculate_dice_probability(current_price, indicators)
                return jsonify(result)
                
            except Exception as e:
                return jsonify({'error': f'计算概率失败: {str(e)}'}), 500
        
        @self.app.route('/api/minute_analysis', methods=['POST'])
        def analyze_minute_probabilities():
            """逐分钟概率分析"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': '缺少请求数据'}), 400
                
                current_price = data.get('current_price')
                indicators = data.get('indicators', {})
                
                if not current_price:
                    return jsonify({'error': '缺少当前价格'}), 400
                
                result = self.signal_generator.analyze_minute_probabilities(current_price, indicators)
                return jsonify(result)
                
            except Exception as e:
                return jsonify({'error': f'分析失败: {str(e)}'}), 500
        
        @self.app.route('/api/dimension_classification', methods=['POST'])
        def classify_signal_dimension():
            """五维度信号分类"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': '缺少请求数据'}), 400
                
                current_price = data.get('current_price')
                indicators = data.get('indicators', {})
                multi_analysis = data.get('multi_analysis', {})
                
                if not current_price:
                    return jsonify({'error': '缺少当前价格'}), 400
                
                result = self.signal_generator.classify_signal_dimension(current_price, indicators, multi_analysis)
                return jsonify(result)
                
            except Exception as e:
                return jsonify({'error': f'分类失败: {str(e)}'}), 500
        
        @self.app.route('/api/add_kline', methods=['POST'])
        def add_minute_kline():
            """添加1分钟K线数据"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': '缺少K线数据'}), 400
                
                # 验证必要字段
                required_fields = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
                if not all(field in data for field in required_fields):
                    return jsonify({'error': f'缺少必要字段: {required_fields}'}), 400
                
                self.signal_generator.add_minute_kline_data(data)
                
                return jsonify({
                    'success': True,
                    'message': '1分钟K线数据已添加',
                    'fifteen_min_klines_count': len(self.signal_generator.fifteen_min_klines)
                })
                
            except Exception as e:
                return jsonify({'error': f'添加K线数据失败: {str(e)}'}), 500
        
        @self.app.route('/api/trade_history')
        def get_trade_history():
            """获取交易历史"""
            try:
                days = request.args.get('days', 30, type=int)
                
                # 获取交易历史
                history = self.trade_tracker.get_performance_stats(days)
                
                return jsonify({
                    'trade_history': history,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                return jsonify({'error': f'获取交易历史失败: {str(e)}'}), 500
        
        @self.app.route('/api/risk_status')
        def get_risk_status():
            """获取风险管理状态"""
            try:
                risk_level = self.risk_manager._assess_risk_level()
                should_stop = self.risk_manager._should_stop_trading()
                
                return jsonify({
                    'risk_level': risk_level,
                    'should_stop_trading': should_stop,
                    'daily_pnl': self.risk_manager.daily_pnl,
                    'daily_trades': self.risk_manager.daily_trades,
                    'consecutive_losses': self.risk_manager.consecutive_losses,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                return jsonify({'error': f'获取风险状态失败: {str(e)}'}), 500
        
        @self.app.route('/api/export_history')
        def export_trade_history():
            """导出交易历史"""
            try:
                start_date = request.args.get('start_date')
                end_date = request.args.get('end_date')
                
                history = self.trade_tracker.export_trade_history(start_date, end_date)
                
                return jsonify({
                    'trade_history': history,
                    'total_records': len(history),
                    'export_timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                return jsonify({'error': f'导出交易历史失败: {str(e)}'}), 500
        
        @self.app.route('/api/signal_performance')
        def get_signal_performance():
            """获取信号表现统计"""
            try:
                direction = request.args.get('direction')
                strength = request.args.get('strength')
                days = request.args.get('days', 30, type=int)
                
                win_rates = self.trade_tracker.get_historical_win_rates(
                    direction=direction, 
                    strength=strength, 
                    lookback_days=days
                )
                
                return jsonify({
                    'win_rates': win_rates,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                return jsonify({'error': f'获取信号表现失败: {str(e)}'}), 500
        
        @self.app.route('/api/minute_probability_history')
        def get_minute_probability_history():
            """获取逐分钟概率分析历史"""
            try:
                limit = request.args.get('limit', 10, type=int)
                
                # 获取最近的概率分析历史
                history = dict(list(self.signal_generator.minute_probability_history.items())[-limit:])
                
                return jsonify({
                    'probability_history': history,
                    'total_records': len(history),
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                return jsonify({'error': f'获取概率历史失败: {str(e)}'}), 500
        
        @self.app.route('/dashboard')
        def dashboard():
            """事件合约信号生成控制面板"""
            return render_template('predictor_dashboard.html')
        
        @self.app.route('/api/comprehensive_analysis', methods=['POST'])
        def comprehensive_analysis():
            """综合分析接口 - 包含所有分析功能"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': '缺少请求数据'}), 400
                
                current_price = data.get('current_price')
                indicators = data.get('indicators', {})
                multi_analysis = data.get('multi_analysis', {})
                
                if not current_price:
                    return jsonify({'error': '缺少当前价格'}), 400
                
                # 执行所有分析
                signal_result = self.signal_generator.generate_signal(multi_analysis, indicators)
                dice_result = self.signal_generator.calculate_dice_probability(current_price, indicators)
                minute_analysis = self.signal_generator.analyze_minute_probabilities(current_price, indicators)
                dimension_classification = self.signal_generator.classify_signal_dimension(current_price, indicators, multi_analysis)
                
                # 获取风险状态
                risk_level = self.risk_manager._assess_risk_level()
                should_stop = self.risk_manager._should_stop_trading()
                
                # 获取最近的交易结果
                last_signal_result = self.trade_tracker.get_last_signal_result()
                
                # 组合完整的响应
                response = {
                    'signal': signal_result,
                    'dice_probability': dice_result,
                    'minute_analysis': minute_analysis,
                    'dimension_classification': dimension_classification,
                    'risk_status': {
                        'risk_level': risk_level,
                        'should_stop_trading': should_stop,
                        'daily_pnl': self.risk_manager.daily_pnl,
                        'daily_trades': self.risk_manager.daily_trades,
                        'consecutive_losses': self.risk_manager.consecutive_losses
                    },
                    'last_signal_result': last_signal_result,
                    'system_status': {
                        'fifteen_min_klines_count': len(self.signal_generator.fifteen_min_klines),
                        'minute_probability_history_count': len(self.signal_generator.minute_probability_history),
                        'trade_history_count': len(self.trade_tracker.trade_history)
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
                return jsonify(response)
                
            except Exception as e:
                return jsonify({'error': f'综合分析失败: {str(e)}'}), 500

    def _extend_indicators(self, indicators: Dict, current_data: Dict) -> Dict:
        """扩展技术指标数据"""
        try:
            extended = indicators.copy()
            
            # 添加额外的分析数据
            current_price = current_data.get('current_price', 0)
            
            # 计算支撑阻力位
            if len(self.data_manager.kline_history) >= 20:
                klines = list(self.data_manager.kline_history)[-20:]
                highs = [k['high'] for k in klines]
                lows = [k['low'] for k in klines]
                closes = [k['close'] for k in klines]
                
                support_resistance = TradeHistoryTracker.identify_support_resistance_levels(
                    highs, lows, closes, period=20
                )
                extended['support_resistance'] = support_resistance
                
                # 计算斐波那契回调位
                fibonacci_levels = TradeHistoryTracker.calculate_fibonacci_levels(
                    highs, lows, period=20
                )
                extended['fibonacci_levels'] = fibonacci_levels
                
                # 急剧下跌检测
                volumes = [k['volume'] for k in klines]
                sharp_downturn = TradeHistoryTracker.detect_sharp_downturn(
                    closes, volumes, 
                    indicators.get('macd_line', 0),
                    indicators.get('macd_signal', 0),
                    indicators.get('ema_20', current_price),
                    indicators.get('ema_50', current_price),
                    current_price
                )
                extended['sharp_downturn'] = sharp_downturn
            else:
                # 默认值
                extended['support_resistance'] = {
                    'support_levels': [], 'resistance_levels': [],
                    'near_support': False, 'near_resistance': False,
                    'approaching_support': False, 'approaching_resistance': False,
                    'support_strength': 0, 'resistance_strength': 0
                }
                extended['fibonacci_levels'] = {
                    'high': current_price * 1.02, 'low': current_price * 0.98,
                    'fib_23.6': current_price * 1.015, 'fib_38.2': current_price * 1.01,
                    'fib_50.0': current_price, 'fib_61.8': current_price * 0.99,
                    'fib_78.6': current_price * 0.985, 'trend_direction': 'neutral'
                }
                extended['sharp_downturn'] = {
                    'is_sharp_downturn': False, 'downturn_severity': 0,
                    'downturn_signals': [], 'block_long_positions': False,
                    'cooling_off_minutes': 0
                }
            
            # 趋势确认
            ema_20 = indicators.get('ema_20', current_price)
            ema_50 = indicators.get('ema_50', current_price)
            
            if ema_20 > ema_50 * 1.02:
                trend_direction = 'strong_bullish'
                trend_strength = 80
            elif ema_20 > ema_50:
                trend_direction = 'bullish'
                trend_strength = 60
            elif ema_20 < ema_50 * 0.98:
                trend_direction = 'strong_bearish'
                trend_strength = 80
            elif ema_20 < ema_50:
                trend_direction = 'bearish'
                trend_strength = 60
            else:
                trend_direction = 'neutral'
                trend_strength = 20
            
            extended['trend_confirmation'] = {
                'is_trend_confirmed': True,
                'trend_direction': trend_direction,
                'trend_strength': trend_strength
            }
            
            # 市场崩盘检测（简化版）
            rsi = indicators.get('rsi', 50)
            volume_ratio = indicators.get('volume_ratio', 1.0)
            
            is_market_crash = (rsi < 20 and volume_ratio > 3.0 and 
                             extended['sharp_downturn']['downturn_severity'] > 70)
            
            extended['market_crash'] = {
                'is_market_crash': is_market_crash,
                'crash_level': 'severe' if is_market_crash else 'none',
                'suspend_long_until': time.time() + 3600 if is_market_crash else 0  # 1小时冷却
            }
            
            return extended
            
        except Exception as e:
            print(f"❌ 扩展技术指标失败: {e}")
            return indicators
    
    def _create_multi_timeframe_analysis(self, indicators: Dict) -> Dict:
        """创建多时间框架分析"""
        try:
            current_price = indicators.get('current_price', 0)
            rsi = indicators.get('rsi', 50)
            bb_position = indicators.get('bb_position', 0.5)
            macd_histogram = indicators.get('macd_histogram', 0)
            
            # 基于技术指标模拟多时间框架分析
            base_high_prob = max(0, min(100, (100 - rsi) * 1.2))  # RSI越小，做空概率越高
            base_low_prob = max(0, min(100, rsi * 1.2 - 20))      # RSI越大，做多概率越高
            
            # 调整因子
            macd_factor = 1.0 + (macd_histogram * 10)  # MACD影响
            bb_factor = 1.0 + ((0.5 - bb_position) * 0.5)  # 布林带位置影响
            
            timeframes = {}
            for timeframe in ['5min', '10min', '15min', '30min']:
                # 为每个时间框架添加一些随机变化
                noise = np.random.normal(0, 5)  # 5%的噪音
                
                high_prob = max(10, min(95, base_high_prob * macd_factor * bb_factor + noise))
                low_prob = max(10, min(95, base_low_prob * bb_factor * macd_factor + noise))
                
                confidence = max(70, min(98, 85 + np.random.normal(0, 3)))
                
                timeframes[timeframe] = {
                    'high_probability': round(high_prob, 1),
                    'low_probability': round(low_prob, 1),
                    'confidence': round(confidence, 1),
                    'dominant_direction': 'DOWN' if high_prob > low_prob else 'UP'
                }
            
            return timeframes
            
        except Exception as e:
            print(f"❌ 创建多时间框架分析失败: {e}")
            return {
                '5min': {'high_probability': 50, 'low_probability': 50, 'confidence': 75, 'dominant_direction': 'NEUTRAL'},
                '10min': {'high_probability': 50, 'low_probability': 50, 'confidence': 75, 'dominant_direction': 'NEUTRAL'},
                '15min': {'high_probability': 50, 'low_probability': 50, 'confidence': 75, 'dominant_direction': 'NEUTRAL'},
                '30min': {'high_probability': 50, 'low_probability': 50, 'confidence': 75, 'dominant_direction': 'NEUTRAL'}
            }
    
    def _analyze_market_status(self, indicators: Dict, current_data: Dict) -> Dict:
        """分析市场状态"""
        try:
            rsi = indicators.get('rsi', 50)
            bb_position = indicators.get('bb_position', 0.5)
            volume_ratio = indicators.get('volume_ratio', 1.0)
            volatility = indicators.get('volatility', 0.5)
            
            # 确定市场状态
            if rsi < 30 and bb_position < 0.3:
                market_sentiment = '极度恐慌'
                sentiment_score = 20
            elif rsi < 40 and bb_position < 0.4:
                market_sentiment = '恐慌'
                sentiment_score = 35
            elif rsi > 70 and bb_position > 0.7:
                market_sentiment = '极度贪婪'
                sentiment_score = 80
            elif rsi > 60 and bb_position > 0.6:
                market_sentiment = '贪婪'
                sentiment_score = 65
            else:
                market_sentiment = '中性'
                sentiment_score = 50
            
            # 波动率状态
            if volatility > 1.0:
                volatility_status = '高波动'
            elif volatility > 0.5:
                volatility_status = '中等波动'
            else:
                volatility_status = '低波动'
            
            # 成交量状态
            if volume_ratio > 2.0:
                volume_status = '成交量暴增'
            elif volume_ratio > 1.5:
                volume_status = '成交量放大'
            elif volume_ratio < 0.8:
                volume_status = '成交量萎缩'
            else:
                volume_status = '成交量正常'
            
            return {
                'market_sentiment': market_sentiment,
                'sentiment_score': sentiment_score,
                'volatility_status': volatility_status,
                'volume_status': volume_status,
                'current_price': current_data.get('current_price', 0),
                'price_change_24h': np.random.uniform(-3, 3),  # 模拟24小时涨跌幅
                'data_freshness': '实时' if current_data.get('is_real_data') else '模拟',
                'last_update': current_data.get('last_update_time'),
                'trading_recommendation': self._get_trading_recommendation(sentiment_score, volatility)
            }
            
        except Exception as e:
            print(f"❌ 分析市场状态失败: {e}")
            return {
                'market_sentiment': '未知',
                'sentiment_score': 50,
                'volatility_status': '未知',
                'volume_status': '未知',
                'current_price': 0,
                'price_change_24h': 0,
                'data_freshness': '错误',
                'last_update': None,
                'trading_recommendation': '等待'
            }
    
    def _get_trading_recommendation(self, sentiment_score: float, volatility: float) -> str:
        """获取交易建议"""
        try:
            if sentiment_score <= 25 and volatility < 0.8:
                return '考虑抄底'
            elif sentiment_score <= 35:
                return '谨慎做多'
            elif sentiment_score >= 75 and volatility < 0.8:
                return '考虑做空'
            elif sentiment_score >= 65:
                return '谨慎做空'
            elif volatility > 1.2:
                return '避免交易'
            else:
                return '观望等待'
        except:
            return '等待'

    def run(self):
        """启动Web服务器"""
        print(f"🚀 启动币安事件合约信号生成系统...")
        print(f"📍 访问地址: http://{self.host}:{self.port}")
        
        try:
            self.app.run(host=self.host, port=self.port, debug=self.debug)
        except KeyboardInterrupt:
            print("\n⏹️  系统已停止")
        except Exception as e:
            print(f"❌ 系统启动失败: {e}")


if __name__ == '__main__':
    predictor = WebPricePredictor()
    predictor.run()
