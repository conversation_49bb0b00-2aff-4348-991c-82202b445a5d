#!/usr/bin/env python3
"""
结算检查器集成演示
展示结算检查器与其他模块的完整集成使用
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import time
from datetime import datetime
from quant.config import config
from quant.strategies.event_contract_settlement_checker import EventContractSettlementChecker
from quant.strategies.event_contract_decision_engine import EventContractDecisionEngine
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier
from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple


class IntegratedTradingSystem:
    """集成交易系统演示"""
    
    def __init__(self):
        """初始化系统"""
        print("🚀 初始化集成交易系统...")
        
        # 加载配置
        config.loads("config.json")
        
        # 创建各个模块
        self.signal_generator = EventContractSignalGeneratorSimple()
        self.decision_engine = EventContractDecisionEngine()
        self.dingtalk_notifier = EventContractDingtalkNotifier()
        self.settlement_checker = EventContractSettlementChecker(
            dingtalk_notifier=self.dingtalk_notifier,
            check_interval=10  # 10秒检查一次
        )
        
        # 设置回调
        self.settlement_checker.add_settlement_callback(self.on_settlement_completed)
        
        print("✅ 集成交易系统初始化完成")
    
    def on_settlement_completed(self, contract):
        """结算完成回调"""
        print(f"📊 结算完成回调: {contract.order_id} - {contract.result} - PnL: {contract.pnl:.2f} USDT")
        
        # 这里可以添加额外的逻辑，比如：
        # 1. 更新交易策略参数
        # 2. 发送额外通知
        # 3. 记录到数据库
        # 4. 风险管理检查
    
    async def simulate_trading_session(self, num_trades: int = 5):
        """模拟交易会话"""
        print(f"\n🎯 开始模拟交易会话 ({num_trades} 笔交易)")
        print("=" * 60)
        
        # 开始结算监控
        self.settlement_checker.start_monitoring()
        
        # 模拟市场数据
        market_data = {
            "current_price": 67500.0,
            "volume": 1250000,
            "volatility": 0.025,
            "trend_strength": 0.8,
            "price_change_24h": 0.035
        }
        
        # 模拟K线数据
        base_price = 67500.0
        for i in range(100):  # 添加一些历史K线
            timestamp = int(time.time() * 1000) - (100 - i) * 60000
            price = base_price + (i % 10 - 5) * 50
            self.signal_generator.add_kline_data(
                timestamp=timestamp,
                open_price=price,
                high_price=price + 25,
                low_price=price - 25,
                close_price=price + (i % 3 - 1) * 10,
                volume=100000 + i * 1000
            )
        
        for trade_num in range(1, num_trades + 1):
            print(f"\n📈 交易 {trade_num}/{num_trades}")
            print("-" * 40)
            
            # 1. 生成信号
            signal_result = self.signal_generator.generate_signal()
            print(f"🎯 信号生成: {signal_result.direction} - 信心度: {signal_result.confidence:.1f}%")
            
            if not signal_result.has_signal:
                print("⚠️ 无有效信号，跳过交易")
                continue
            
            # 2. 决策引擎分析
            decision = self.decision_engine.make_trading_decision(
                signal_result=signal_result,
                current_balance=10000.0,
                market_data=market_data
            )
            
            print(f"🤖 决策结果: {'执行' if decision.should_trade else '跳过'}")
            print(f"   - 方向: {decision.direction}")
            print(f"   - 投注金额: {decision.bet_amount:.2f} USDT")
            print(f"   - 风险等级: {decision.risk_level.value}")
            print(f"   - 决策原因: {decision.reason}")
            
            if not decision.should_trade:
                print("❌ 决策引擎建议不交易")
                continue
            
            # 3. 执行交易（模拟）
            order_id = f"ORDER_{trade_num:03d}_{int(time.time())}"
            current_price = market_data["current_price"] + (trade_num % 5 - 2) * 25
            
            print(f"💰 模拟下单: {order_id} - 当前价格: {current_price:.2f}")
            
            # 4. 记录交易
            self.decision_engine.record_trade(
                decision=decision,
                order_id=order_id,
                executed_price=current_price,
                executed_amount=decision.bet_amount
            )
            
            # 5. 发送交易信号通知
            success, error = self.dingtalk_notifier.send_trading_signal(
                decision=decision,
                signal_result=signal_result,
                market_data=market_data
            )
            
            if success:
                print("✅ 交易信号通知已发送")
            else:
                print(f"❌ 交易信号通知发送失败: {error}")
            
            # 6. 添加到结算检查器
            self.settlement_checker.add_contract(
                order_id=order_id,
                symbol="BTCUSDT",
                direction=decision.direction,
                bet_amount=decision.bet_amount,
                predicted_price=current_price,
                decision=decision,
                expiry_minutes=0.5  # 30秒到期，快速演示
            )
            
            # 等待一段时间再进行下一笔交易
            await asyncio.sleep(5)
        
        # 等待所有合约结算完成
        print("\n⏳ 等待合约结算完成...")
        await asyncio.sleep(60)  # 等待60秒
        
        # 停止监控
        self.settlement_checker.stop_monitoring()
        
        # 显示最终统计
        await self.show_final_statistics()
    
    async def show_final_statistics(self):
        """显示最终统计"""
        print("\n" + "=" * 60)
        print("📊 最终统计报告")
        print("=" * 60)
        
        # 结算检查器统计
        settlement_stats = self.settlement_checker.get_statistics()
        print(f"🎯 结算统计:")
        print(f"   总交易数: {settlement_stats.total_trades}")
        print(f"   胜利次数: {settlement_stats.wins}")
        print(f"   失败次数: {settlement_stats.losses}")
        print(f"   胜率: {settlement_stats.win_rate:.1%}")
        print(f"   总盈亏: {settlement_stats.total_pnl:.2f} USDT")
        print(f"   总投入: {settlement_stats.total_invested:.2f} USDT")
        print(f"   平均投注: {settlement_stats.avg_bet_amount:.2f} USDT")
        print(f"   当前连胜/连败: {settlement_stats.current_streak}")
        print(f"   最大连胜: {settlement_stats.max_consecutive_wins}")
        print(f"   最大连败: {settlement_stats.max_consecutive_losses}")
        
        # 决策引擎统计
        decision_stats = self.decision_engine.get_daily_stats()
        print(f"\n🤖 决策引擎统计:")
        print(f"   今日交易数: {decision_stats.get('total_trades', 0)}")
        print(f"   今日胜率: {decision_stats.get('win_rate', 0):.1%}")
        print(f"   今日盈亏: {decision_stats.get('total_pnl', 0):.2f} USDT")
        print(f"   今日投入: {decision_stats.get('total_invested', 0):.2f} USDT")
        
        # 通知统计
        notification_stats = self.dingtalk_notifier.get_notification_stats()
        print(f"\n📱 通知统计:")
        print(f"   今日发送: {notification_stats['total_today']}")
        print(f"   剩余额度: {notification_stats['remaining_quota']}")
        print(f"   消息类型: {notification_stats['types_today']}")
        
        # 已结算合约详情
        settled_contracts = self.settlement_checker.get_settled_contracts()
        print(f"\n📋 已结算合约详情:")
        for contract in settled_contracts:
            print(f"   {contract.order_id}: {contract.result} - "
                  f"投注: {contract.bet_amount:.2f} USDT - "
                  f"盈亏: {contract.pnl:.2f} USDT - "
                  f"收益率: {contract.return_rate:.1%}")
        
        # 导出交易历史
        export_path = self.settlement_checker.export_trading_history()
        print(f"\n💾 交易历史已导出到: {export_path}")
        
        # 发送每日总结
        risk_summary = self.decision_engine.get_risk_summary()
        success, error = self.dingtalk_notifier.send_daily_summary(
            stats=decision_stats,
            risk_summary=risk_summary
        )
        
        if success:
            print("✅ 每日总结已发送到钉钉")
        else:
            print(f"❌ 每日总结发送失败: {error}")
    
    async def run_demo(self):
        """运行演示"""
        print("🎬 开始集成交易系统演示")
        print("=" * 60)
        
        try:
            # 运行模拟交易会话
            await self.simulate_trading_session(num_trades=5)
            
            print("\n🎉 演示完成！")
            print("📱 请检查您的钉钉群是否收到了相关通知")
            
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 确保停止监控
            if self.settlement_checker.is_running:
                self.settlement_checker.stop_monitoring()


async def main():
    """主函数"""
    system = IntegratedTradingSystem()
    await system.run_demo()


if __name__ == "__main__":
    asyncio.run(main()) 