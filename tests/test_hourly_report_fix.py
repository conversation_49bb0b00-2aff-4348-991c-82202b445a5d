#!/usr/bin/env python3
"""
测试小时报告修复
验证信号统计和数据显示是否正确
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from quant.strategies.event_contract_main_strategy import EventContractMainStrategy
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier
from quant.strategies.event_contract_signal_generator_simple import SignalResult


def test_signal_statistics():
    """测试信号统计功能"""
    print("🔍 测试信号统计功能...")
    
    # 创建钉钉通知器
    notifier = EventContractDingtalkNotifier()
    
    # 模拟添加不同类型的通知历史
    now = datetime.now()
    
    # 添加一些测试通知记录
    notifier.notification_history = [
        {
            'timestamp': now - timedelta(hours=2),
            'type': 'pending_signal',
            'content': 'DOWN 方向信号'
        },
        {
            'timestamp': now - timedelta(hours=1, minutes=30),
            'type': 'recommendation',
            'content': 'UP 推荐信号'
        },
        {
            'timestamp': now - timedelta(hours=1),
            'type': 'pending_signal',
            'content': 'UP 方向信号'
        },
        {
            'timestamp': now - timedelta(minutes=30),
            'type': 'recommendation',
            'content': 'DOWN 推荐信号'
        }
    ]
    
    # 创建主策略实例
    try:
        strategy = EventContractMainStrategy()
        strategy.dingtalk_notifier = notifier
        
        # 获取信号历史
        signal_history = strategy.get_daily_signal_history()
        
        print(f"📊 信号统计结果:")
        print(f"总信号数: {signal_history['total_signals']}")
        print(f"潜在信号: {signal_history['summary']['pending_signals']}")
        print(f"推荐信号: {signal_history['summary']['recommendation_signals']}")
        print(f"看涨信号: {signal_history['summary']['up_signals']}")
        print(f"看跌信号: {signal_history['summary']['down_signals']}")
        
        # 验证结果
        assert signal_history['total_signals'] == 4, f"总信号数应该为4，实际为{signal_history['total_signals']}"
        assert signal_history['summary']['pending_signals'] == 2, f"潜在信号应该为2，实际为{signal_history['summary']['pending_signals']}"
        assert signal_history['summary']['recommendation_signals'] == 2, f"推荐信号应该为2，实际为{signal_history['summary']['recommendation_signals']}"
        
        print("✅ 信号统计测试通过！")
        
    except Exception as e:
        print(f"❌ 信号统计测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_hourly_report_format():
    """测试小时报告格式"""
    print("\n🔍 测试小时报告格式...")
    
    try:
        # 创建钉钉通知器并添加测试数据
        notifier = EventContractDingtalkNotifier()
        now = datetime.now()
        
        notifier.notification_history = [
            {
                'timestamp': now - timedelta(hours=1),
                'type': 'pending_signal',
                'content': 'DOWN 方向信号'
            },
            {
                'timestamp': now - timedelta(minutes=30),
                'type': 'recommendation',
                'content': 'UP 推荐信号'
            }
        ]
        
        # 创建主策略实例
        strategy = EventContractMainStrategy()
        strategy.dingtalk_notifier = notifier
        
        # 获取信号历史
        signal_history = strategy.get_daily_signal_history()
        
        # 计算信号触发率
        pending_count = signal_history['summary']['pending_signals']
        recommendation_count = signal_history['summary']['recommendation_signals']
        signal_trigger_rate = (recommendation_count / pending_count) if pending_count > 0 else 0.0
        
        print(f"📊 小时报告数据:")
        print(f"潜在信号: {pending_count}")
        print(f"推荐信号: {recommendation_count}")
        print(f"信号触发率: {signal_trigger_rate:.1%}")
        
        # 验证触发率计算
        expected_rate = 1.0 if pending_count > 0 else 0.0
        assert abs(signal_trigger_rate - expected_rate) < 0.1, f"信号触发率计算错误"
        
        print("✅ 小时报告格式测试通过！")
        
    except Exception as e:
        print(f"❌ 小时报告格式测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("🚀 开始测试小时报告修复...")
    
    test_signal_statistics()
    test_hourly_report_format()
    
    print("\n✅ 所有测试完成！")


if __name__ == "__main__":
    main()