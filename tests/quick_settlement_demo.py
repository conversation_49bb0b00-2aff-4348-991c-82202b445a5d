#!/usr/bin/env python3
"""
快速结算检查器演示
展示结算检查器的主要功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
from datetime import datetime
from quant.config import config
from quant.strategies.event_contract_settlement_checker import EventContractSettlementChecker
from quant.strategies.event_contract_decision_engine import TradingDecision, RiskLevel, MarketCondition
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier


async def quick_demo():
    """快速演示结算检查器功能"""
    print("🚀 快速结算检查器演示")
    print("=" * 50)
    
    # 加载配置
    config.loads("config.json")
    
    # 创建组件
    dingtalk_notifier = EventContractDingtalkNotifier()
    settlement_checker = EventContractSettlementChecker(
        dingtalk_notifier=dingtalk_notifier,
        check_interval=5  # 5秒检查一次
    )
    
    # 添加回调
    def on_settlement(contract):
        print(f"📊 结算完成: {contract.order_id} - {contract.result} - PnL: {contract.pnl:.2f} USDT")
    
    settlement_checker.add_settlement_callback(on_settlement)
    
    print("✅ 结算检查器初始化完成")
    
    # 创建测试决策
    decision = TradingDecision(
        should_trade=True,
        direction="UP",
        bet_amount=20.0,
        confidence=85.0,
        risk_level=RiskLevel.MEDIUM,
        market_condition=MarketCondition.TRENDING,
        reason="演示用测试决策",
        timestamp=datetime.now(),
        max_loss_allowed=980.0,
        current_daily_loss=20.0,
        position_size_ratio=0.02,
        market_score=85.0,
        signal_strength=85.0,
        entry_timing="良好"
    )
    
    # 添加测试合约
    test_contracts = [
        ("DEMO_001", "UP", 20.0, 67500.0),
        ("DEMO_002", "DOWN", 25.0, 67800.0),
        ("DEMO_003", "UP", 30.0, 67600.0),
    ]
    
    print("\n📈 添加测试合约:")
    for order_id, direction, bet_amount, predicted_price in test_contracts:
        settlement_checker.add_contract(
            order_id=order_id,
            symbol="BTCUSDT",
            direction=direction,
            bet_amount=bet_amount,
            predicted_price=predicted_price,
            decision=decision,
            expiry_minutes=0.2  # 12秒到期
        )
        print(f"   {order_id}: {direction} - {bet_amount} USDT - 预测价格: {predicted_price}")
    
    # 开始监控
    print("\n🔍 开始监控结算...")
    settlement_checker.start_monitoring()
    
    # 等待结算完成
    await asyncio.sleep(30)  # 等待30秒
    
    # 停止监控
    settlement_checker.stop_monitoring()
    
    # 显示统计结果
    print("\n📊 统计结果:")
    stats = settlement_checker.get_statistics()
    print(f"   总交易数: {stats.total_trades}")
    print(f"   胜利次数: {stats.wins}")
    print(f"   失败次数: {stats.losses}")
    print(f"   胜率: {stats.win_rate:.1%}")
    print(f"   总盈亏: {stats.total_pnl:.2f} USDT")
    print(f"   总投入: {stats.total_invested:.2f} USDT")
    print(f"   当前连胜/连败: {stats.current_streak}")
    
    # 显示已结算合约
    settled_contracts = settlement_checker.get_settled_contracts()
    print(f"\n📋 已结算合约:")
    for contract in settled_contracts:
        print(f"   {contract.order_id}: {contract.result} - "
              f"预测: {contract.predicted_price:.2f} - "
              f"实际: {contract.final_price:.2f} - "
              f"PnL: {contract.pnl:.2f} USDT")
    
    # 导出数据
    export_path = settlement_checker.export_trading_history("tests/demo_trading_history.json")
    print(f"\n💾 交易历史已导出到: {export_path}")
    
    # 通知统计
    notification_stats = dingtalk_notifier.get_notification_stats()
    print(f"\n📱 通知统计:")
    print(f"   今日发送: {notification_stats['total_today']}")
    print(f"   剩余额度: {notification_stats['remaining_quota']}")
    print(f"   消息类型: {notification_stats['types_today']}")
    
    print("\n🎉 演示完成！")
    print("📱 请检查您的钉钉群是否收到了结算通知")


if __name__ == "__main__":
    asyncio.run(quick_demo()) 