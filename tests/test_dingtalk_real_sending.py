#!/usr/bin/env python3
"""
钉钉消息发送实际测试
测试真实的钉钉API消息发送功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
from quant.config import config
from quant.utils.dingtalk import Dingtalk
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier
from quant.strategies.event_contract_decision_engine import TradingDecision, RiskLevel, MarketCondition
from quant.strategies.event_contract_signal_generator_simple import SignalResult

def test_basic_dingtalk_sending():
    """测试基础钉钉消息发送"""
    print("=" * 50)
    print("测试基础钉钉消息发送")
    print("=" * 50)
    
    # 加载配置
    config.loads("config.json")
    print(f"钉钉配置: {config.dingtalk}")
    
    # 发送测试消息
    test_message = "### 🚀 钉钉API测试消息\n\n> **测试时间:** {}\n\n> **测试内容:** 币安事件合约**交易**系统测试 🚀\n\n> **关键词:** 交易、小火箭\n\n祝**交易**顺利！🚀**小火箭**起飞！".format(
        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )
    
    success, error = Dingtalk.markdown(content=test_message, token=config.dingtalk)
    
    if success:
        print("✅ 基础钉钉消息发送成功！")
        print(f"响应: {success}")
    else:
        print("❌ 基础钉钉消息发送失败！")
        print(f"错误: {error}")
    
    return success is not None

def test_event_contract_notifications():
    """测试事件合约通知功能"""
    print("\n" + "=" * 50)
    print("测试事件合约通知功能")
    print("=" * 50)
    
    # 加载配置
    config.loads("config.json")
    
    # 创建通知器
    notifier = EventContractDingtalkNotifier()
    
    # 创建模拟交易决策
    decision = TradingDecision(
        should_trade=True,
        direction="UP",
        bet_amount=20.0,
        confidence=85.5,
        risk_level=RiskLevel.MEDIUM,
        market_condition=MarketCondition.TRENDING,
        reason="技术指标看涨：RSI=35.2，MACD金叉，价格突破15分钟阻力位",
        timestamp=datetime.now(),
        max_loss_allowed=980.0,
        current_daily_loss=20.0,
        position_size_ratio=0.02,
        market_score=85.5,
        signal_strength=85.5,
        entry_timing="良好"
    )
    
    # 创建模拟信号结果
    signal_result = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=85.5,
        bullish_probability=85.5,
        bearish_probability=14.5,
        supporting_timeframes=["15m", "30m", "1h"],
        technical_score=8.5,
        risk_level="MEDIUM",
        timeframe_analysis={
            "15m": {"trend": "upward", "strength": "strong"},
            "30m": {"trend": "upward", "strength": "medium"},
            "1h": {"trend": "upward", "strength": "strong"}
        },
        user_reminder="技术指标显示强烈看涨信号，建议入场",
        market_status="正常监控"
    )
    
    # 测试交易信号通知
    print("\n📢 发送交易信号通知...")
    success, error = notifier.send_trading_signal(
        decision=decision,
        signal_result=signal_result,
        market_data={"current_price": 67500.0, "volume": 1250000}
    )
    
    if success:
        print("✅ 交易信号通知发送成功！")
    else:
        print(f"❌ 交易信号通知发送失败: {error}")
    
    # 测试结算通知
    print("\n💰 发送结算通知...")
    success, error = notifier.send_settlement_notification(
        order_id="TEST_ORDER_001",
        result="win",
        pnl=8.0,
        decision=decision
    )
    
    if success:
        print("✅ 结算通知发送成功！")
    else:
        print(f"❌ 结算通知发送失败: {error}")
    
    # 测试风险提醒
    print("\n⚠️ 发送风险提醒...")
    success, error = notifier.send_risk_alert(
        risk_type="连续亏损",
        message="连续3次亏损，建议暂停交易",
        current_loss=60.0,
        loss_ratio=0.06
    )
    
    if success:
        print("✅ 风险提醒发送成功！")
    else:
        print(f"❌ 风险提醒发送失败: {error}")
    
    # 测试每日总结
    print("\n📊 发送每日总结...")
    stats = {
        "total_trades": 15,
        "win_rate": 0.67,
        "total_pnl": 125.6,
        "avg_bet": 20.0,
        "current_streak": 2
    }
    
    risk_summary = {
        "daily_loss": 45.0,
        "daily_loss_ratio": 0.045,
        "remaining_soft_limit": 955.0
    }
    
    success, error = notifier.send_daily_summary(stats, risk_summary)
    
    if success:
        print("✅ 每日总结发送成功！")
    else:
        print(f"❌ 每日总结发送失败: {error}")
    
    # 获取通知统计
    print("\n📈 通知统计:")
    stats = notifier.get_notification_stats()
    print(f"今日发送: {stats['total_today']}")
    print(f"剩余额度: {stats['remaining_quota']}")
    print(f"各类型统计: {stats['types_today']}")

def main():
    """主函数"""
    print("🚀 开始钉钉消息发送测试...")
    
    try:
        # 测试1：基础钉钉消息发送
        basic_success = test_basic_dingtalk_sending()
        
        # 测试2：事件合约通知功能
        if basic_success:
            test_event_contract_notifications()
        else:
            print("❌ 基础测试失败，跳过事件合约通知测试")
        
        print("\n" + "=" * 50)
        print("✅ 钉钉消息发送测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 