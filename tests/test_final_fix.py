#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的交易执行
验证SignalResult对象能否正确传递给_execute_trade方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_signal_generator import EventContractSignalGenerator, SignalResult
from quant.strategies.event_contract_decision_engine import EventContractDecisionEngine
from quant.strategies.event_contract_main_strategy import EventContractMainStrategy
import asyncio
import time

async def test_final_fix():
    """测试修复后的完整交易流程"""
    print("🚀 测试修复后的完整交易流程...")
    
    # 创建主策略实例
    strategy = EventContractMainStrategy()
    
    # 创建一个模拟的SignalResult对象
    test_signal = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=75.0,
        technical_score=85.0,
        bullish_probability=75.0,
        bearish_probability=25.0,
        risk_level="LOW"
    )
    
    # 手动添加current_price属性
    test_signal.current_price = 50000.0
    
    print(f"📊 测试信号:")
    print(f"   方向: {test_signal.direction}")
    print(f"   置信度: {test_signal.confidence}%")
    print(f"   技术评分: {test_signal.technical_score}")
    print(f"   当前价格: {test_signal.current_price}")
    
    # 创建决策对象
    class MockDecision:
        def __init__(self):
            self.should_trade = True
            self.amount = 20.0
            self.risk_level = "LOW"
        
        def get(self, key, default=None):
            return getattr(self, key, default)
    
    test_decision = MockDecision()
    
    print(f"📋 决策信息:")
    print(f"   应该交易: {test_decision.should_trade}")
    print(f"   交易金额: {test_decision.amount} USDT")
    print(f"   风险等级: {test_decision.risk_level}")
    
    # 测试_execute_trade方法
    print("\n🔄 测试交易执行...")
    try:
        # 这里会测试SignalResult对象是否能正确处理
        await strategy._execute_trade(test_signal, test_decision)
        print("✅ 交易执行测试通过！")
    except Exception as e:
        print(f"❌ 交易执行测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_final_fix()) 