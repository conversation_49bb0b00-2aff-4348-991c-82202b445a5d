#!/usr/bin/env python3
"""
信号时机优化简单测试
验证系统基本功能
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import asyncio

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from quant.strategies.signal_timing_optimizer import SignalTimingOptimizer, TimingTestConfig
from quant.strategies.delayed_signal_generator import DelayedSignalGenerator, DelayedSignalConfig
from quant.strategies.event_contract_signal_generator_simple import SignalResult


def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("信号时机优化系统基本功能测试")
    print("=" * 60)
    
    # 1. 测试配置创建
    print("1. 测试配置创建...")
    config = TimingTestConfig(
        delay_minutes_range=[0, 1, 2, 3],
        test_days=1,
        min_signals_per_delay=5,
        output_dir="tests/simple_test_output"
    )
    print(f"   ✓ 配置创建成功: 延迟范围 {config.delay_minutes_range}")
    
    # 2. 测试优化器初始化
    print("2. 测试优化器初始化...")
    optimizer = SignalTimingOptimizer(config)
    print("   ✓ 优化器初始化成功")
    
    # 3. 测试延迟信号生成器
    print("3. 测试延迟信号生成器...")
    delay_config = DelayedSignalConfig(delay_minutes=2)
    generator = DelayedSignalGenerator(delay_config)
    print("   ✓ 延迟信号生成器创建成功")
    
    # 4. 测试信号记录添加
    print("4. 测试信号记录添加...")
    
    # 创建模拟信号
    mock_signal = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=75.0,
        technical_score=80.0,
        risk_level="MEDIUM"
    )
    
    # 添加信号记录
    signal_id = optimizer.add_signal_record(
        delay_minutes=2,
        signal_result=mock_signal,
        signal_price=50000.0,
        timestamp=datetime.now()
    )
    print(f"   ✓ 信号记录添加成功: {signal_id}")
    
    # 5. 测试信号结算
    print("5. 测试信号结算...")
    settlement_time = datetime.now() + timedelta(minutes=15)
    settlement_price = 50100.0  # 上涨0.2%
    
    success = optimizer.settle_signal(signal_id, settlement_price, settlement_time)
    print(f"   ✓ 信号结算成功: {success}")
    
    # 6. 测试统计计算
    print("6. 测试统计计算...")
    statistics = optimizer.calculate_all_statistics()
    print(f"   ✓ 统计计算完成，延迟数量: {len(statistics)}")
    
    # 显示统计结果
    for delay, stats in statistics.items():
        if stats.total_signals > 0:
            print(f"     延迟{delay}分钟: {stats.total_signals}个信号, 胜率{stats.win_rate:.1f}%")
    
    # 7. 测试报告生成
    print("7. 测试报告生成...")
    report = optimizer.generate_comparison_report()
    print("   ✓ 报告生成成功")
    
    # 显示报告片段
    report_lines = report.split('\n')
    print("   报告预览:")
    for line in report_lines[:10]:  # 显示前10行
        print(f"     {line}")
    print("     ...")
    
    print("\n" + "=" * 60)
    print("基本功能测试完成！")
    print("=" * 60)
    
    return True


def test_multiple_signals():
    """测试多信号处理"""
    print("\n测试多信号处理...")
    
    config = TimingTestConfig(
        delay_minutes_range=[0, 2, 5],
        min_signals_per_delay=3,
        output_dir="tests/multi_signal_test"
    )
    
    optimizer = SignalTimingOptimizer(config)
    
    # 生成多个测试信号
    test_signals = [
        {"direction": "UP", "confidence": 70.0, "price": 50000.0, "settlement": 50200.0},
        {"direction": "DOWN", "confidence": 65.0, "price": 50200.0, "settlement": 50000.0},
        {"direction": "UP", "confidence": 80.0, "price": 50000.0, "settlement": 49900.0},  # 亏损
        {"direction": "DOWN", "confidence": 75.0, "price": 50100.0, "settlement": 49800.0},
        {"direction": "UP", "confidence": 60.0, "price": 50000.0, "settlement": 50050.0},
    ]
    
    signal_ids = []
    
    # 为每个延迟添加信号
    for delay in config.delay_minutes_range:
        for i, signal_data in enumerate(test_signals):
            mock_signal = SignalResult(
                has_signal=True,
                direction=signal_data["direction"],
                confidence=signal_data["confidence"],
                technical_score=signal_data["confidence"] + 5,
                risk_level="MEDIUM"
            )
            
            timestamp = datetime.now() + timedelta(minutes=i)
            signal_id = optimizer.add_signal_record(
                delay_minutes=delay,
                signal_result=mock_signal,
                signal_price=signal_data["price"],
                timestamp=timestamp
            )
            
            # 立即结算
            settlement_time = timestamp + timedelta(minutes=15)
            optimizer.settle_signal(signal_id, signal_data["settlement"], settlement_time)
            
            signal_ids.append(signal_id)
    
    print(f"   ✓ 生成了 {len(signal_ids)} 个测试信号")
    
    # 计算统计
    statistics = optimizer.calculate_all_statistics()
    
    print("   统计结果:")
    for delay, stats in statistics.items():
        print(f"     延迟{delay}分钟: {stats.total_signals}个信号, "
              f"胜率{stats.win_rate:.1f}%, 平均PnL{stats.avg_pnl*100:.2f}%")
    
    # 找出最佳时机
    best_delay, best_stats = optimizer.get_best_timing()
    print(f"   ✓ 最佳时机: 延迟{best_delay}分钟, 胜率{best_stats.win_rate:.1f}%")
    
    return True


async def test_data_simulator():
    """测试数据模拟器"""
    print("\n测试数据模拟器...")
    
    from quant.strategies.backtest_data_simulator import BacktestDataSimulator, BacktestConfig
    
    config = BacktestConfig(
        start_date="2024-01-01",
        end_date="2024-01-02",  # 1天数据
        data_source="mock"
    )
    
    simulator = BacktestDataSimulator(config)
    
    # 加载数据
    success = await simulator.load_historical_data()
    if not success:
        print("   ✗ 数据加载失败")
        return False
    
    data_info = simulator.get_data_info()
    print(f"   ✓ 数据加载成功: {data_info['total_klines']} 根K线")
    print(f"     时间范围: {data_info['first_time']} ~ {data_info['last_time']}")
    
    # 测试批量获取
    batch = simulator.get_kline_batch(10)
    print(f"   ✓ 批量获取测试: 获得 {len(batch)} 根K线")
    
    # 显示前几根K线
    print("     前3根K线:")
    for i, kline in enumerate(batch[:3]):
        timestamp = datetime.fromtimestamp(kline['timestamp'] / 1000)
        print(f"       {i+1}. {timestamp.strftime('%H:%M:%S')} "
              f"O:{kline['open']:.2f} H:{kline['high']:.2f} "
              f"L:{kline['low']:.2f} C:{kline['close']:.2f}")
    
    return True


async def main():
    """主测试函数"""
    try:
        print("开始信号时机优化系统测试\n")
        
        # 创建输出目录
        os.makedirs("tests/simple_test_output", exist_ok=True)
        os.makedirs("tests/multi_signal_test", exist_ok=True)
        
        # 运行测试
        success1 = test_basic_functionality()
        success2 = test_multiple_signals()
        success3 = await test_data_simulator()
        
        if success1 and success2 and success3:
            print("\n🎉 所有测试通过！")
            print("系统已准备好进行完整的时机优化测试。")
            print("\n下一步:")
            print("1. 运行完整测试: python3 tests/signal_timing_optimization_test.py")
            print("2. 或者使用真实数据进行测试")
            return True
        else:
            print("\n❌ 部分测试失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
