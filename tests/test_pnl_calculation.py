#!/usr/bin/env python3
"""
测试修正后的盈亏计算规则
"""
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_settlement_checker import EventContractSettlementChecker
from quant.strategies.event_contract_decision_engine import TradingDecision, MarketCondition, RiskLevel
from datetime import datetime

def test_pnl_calculation():
    """测试盈亏计算"""
    print("🧮 测试币安事件合约盈亏计算规则")
    print("=" * 50)
    
    checker = EventContractSettlementChecker()
    
    # 创建一个模拟决策
    decision = TradingDecision(
        should_trade=True,
        direction="UP",
        bet_amount=20.0,
        confidence=75.0,
        risk_level=RiskLevel.LOW,
        market_condition=MarketCondition.TRENDING,
        reason="测试用例",
        timestamp=datetime.now(),
        max_loss_allowed=1000.0,
        current_daily_loss=0.0,
        position_size_ratio=0.1,
        market_score=75.0,
        signal_strength=75.0,
        entry_timing="良好"
    )
    
    # 测试场景
    test_cases = [
        {
            "name": "预测正确 - 上涨",
            "direction": "UP",
            "predicted_price": 100.0,
            "final_price": 105.0,
            "expected_result": "win",
            "expected_pnl": 16.0  # 20 * 0.8
        },
        {
            "name": "预测正确 - 下跌",
            "direction": "DOWN",
            "predicted_price": 100.0,
            "final_price": 95.0,
            "expected_result": "win",
            "expected_pnl": 16.0  # 20 * 0.8
        },
        {
            "name": "预测错误 - 上涨但实际下跌",
            "direction": "UP",
            "predicted_price": 100.0,
            "final_price": 98.0,
            "expected_result": "loss",
            "expected_pnl": -20.0  # 损失全部投入
        },
        {
            "name": "预测错误 - 下跌但实际上涨",
            "direction": "DOWN",
            "predicted_price": 100.0,
            "final_price": 102.0,
            "expected_result": "loss",
            "expected_pnl": -20.0  # 损失全部投入
        },
        {
            "name": "价格相等 - 平局",
            "direction": "UP",
            "predicted_price": 100.0,
            "final_price": 100.0,
            "expected_result": "tie",
            "expected_pnl": 0.0  # 回本
        }
    ]
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {case['name']}")
        print(f"  方向: {case['direction']}")
        print(f"  预测价格: {case['predicted_price']:.2f}")
        print(f"  最终价格: {case['final_price']:.2f}")
        
        # 创建合约记录
        from quant.strategies.event_contract_settlement_checker import ContractRecord
        contract = ContractRecord(
            order_id=f"TEST_{i:03d}",
            symbol="BTCUSDT",
            direction=case['direction'],
            bet_amount=20.0,
            predicted_price=case['predicted_price'],
            entry_time=datetime.now(),
            expiry_time=datetime.now(),
            decision=decision
        )
        
        # 测试结果判断
        result = checker._determine_result(contract, case['final_price'])
        pnl = checker._calculate_pnl(contract, result)
        
        print(f"  实际结果: {result}")
        print(f"  实际盈亏: {pnl:.2f} USDT")
        print(f"  预期结果: {case['expected_result']}")
        print(f"  预期盈亏: {case['expected_pnl']:.2f} USDT")
        
        # 验证结果
        result_correct = result == case['expected_result']
        pnl_correct = abs(pnl - case['expected_pnl']) < 0.01
        
        if result_correct and pnl_correct:
            print("  ✅ 通过")
        else:
            print("  ❌ 失败")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试用例通过！盈亏计算规则正确。")
    else:
        print("❌ 部分测试用例失败，请检查代码。")
    
    # 测试统计功能
    print("\n📊 测试统计功能")
    print("-" * 30)
    
    # 模拟一些交易结果
    test_results = [
        ("WIN_001", "win", 16.0),
        ("LOSS_001", "loss", -20.0),
        ("TIE_001", "tie", 0.0),
        ("WIN_002", "win", 16.0),
        ("LOSS_002", "loss", -20.0),
    ]
    
    for order_id, result, pnl in test_results:
        contract = ContractRecord(
            order_id=order_id,
            symbol="BTCUSDT",
            direction="UP",
            bet_amount=20.0,
            predicted_price=100.0,
            entry_time=datetime.now(),
            expiry_time=datetime.now(),
            decision=decision
        )
        contract.result = result
        contract.pnl = pnl
        contract.settlement_time = datetime.now()
        
        checker.settled_contracts[order_id] = contract
        checker._update_statistics(contract)
    
    stats = checker.get_statistics()
    print(f"总交易: {stats.total_trades}")
    print(f"胜利: {stats.wins}")
    print(f"失败: {stats.losses}")
    print(f"平局: {stats.ties}")
    print(f"胜率: {stats.win_rate:.1%}")
    print(f"总盈亏: {stats.total_pnl:.2f} USDT")
    
    print("\n✅ 盈亏计算规则修正完成！")

if __name__ == "__main__":
    test_pnl_calculation() 