#!/usr/bin/env python3
"""
决策引擎测试脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
from quant.strategies.event_contract_decision_engine import (
    EventContractDecisionEngine,
    RiskManagementConfig,
    RiskLevel,
    MarketCondition
)
from quant.strategies.event_contract_signal_generator_simple import (
    SignalResult,
    EventContractSignalGeneratorSimple
)

def test_basic_decision_engine():
    """测试基本决策引擎功能"""
    print("=== 测试基本决策引擎功能 ===")
    
    # 创建决策引擎
    config = RiskManagementConfig(
        base_bet_amount=20.0,
        daily_soft_limit=100.0,  # 测试用小限制
        daily_hard_limit=200.0,
        max_bet_amount=50.0,
        min_bet_amount=5.0
    )
    
    engine = EventContractDecisionEngine(config)
    
    # 创建测试信号
    signal = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=85.0,
        technical_score=75.0,
        market_status="正常",
        user_reminder="测试信号"
    )
    
    # 测试决策
    decision = engine.make_trading_decision(
        signal_result=signal,
        current_balance=1000.0,
        market_data={
            'volatility': 0.03,
            'trend_strength': 0.8,
            'price_change_24h': 0.05
        }
    )
    
    print(f"决策结果:")
    print(f"- 是否交易: {decision.should_trade}")
    print(f"- 投注金额: {decision.bet_amount:.2f} USDT")
    print(f"- 方向: {decision.direction}")
    print(f"- 信心度: {decision.confidence:.1f}%")
    print(f"- 风险等级: {decision.risk_level.value}")
    print(f"- 市场条件: {decision.market_condition.value}")
    print(f"- 原因: {decision.reason}")
    print(f"- 仓位比例: {decision.position_size_ratio:.2%}")
    
    return decision

def test_risk_management():
    """测试风险管理功能"""
    print("\n=== 测试风险管理功能 ===")
    
    config = RiskManagementConfig(
        base_bet_amount=20.0,
        daily_soft_limit=100.0,
        daily_hard_limit=200.0
    )
    
    engine = EventContractDecisionEngine(config)
    
    # 模拟一些损失交易
    engine.daily_trades = [
        {'date': datetime.now().date(), 'pnl': -30.0, 'bet_amount': 20.0},
        {'date': datetime.now().date(), 'pnl': -25.0, 'bet_amount': 20.0},
        {'date': datetime.now().date(), 'pnl': -20.0, 'bet_amount': 20.0},
    ]
    
    # 测试当前状态
    daily_loss = engine._calculate_daily_loss()
    print(f"当前损失: {daily_loss:.2f} USDT")
    
    # 测试高风险信号
    high_risk_signal = SignalResult(
        has_signal=True,
        direction="DOWN",
        confidence=65.0,  # 较低信心
        technical_score=55.0,
        market_status="谨慎",
        user_reminder="风险较高"
    )
    
    decision = engine.make_trading_decision(
        signal_result=high_risk_signal,
        current_balance=1000.0
    )
    
    print(f"高风险信号决策:")
    print(f"- 是否交易: {decision.should_trade}")
    print(f"- 风险等级: {decision.risk_level.value}")
    print(f"- 投注金额: {decision.bet_amount:.2f} USDT")
    print(f"- 原因: {decision.reason}")
    
    # 测试停止交易条件
    should_stop, reason = engine.should_stop_trading()
    print(f"是否应该停止交易: {should_stop}")
    if should_stop:
        print(f"停止原因: {reason}")
    
    return decision

def test_market_conditions():
    """测试不同市场条件"""
    print("\n=== 测试不同市场条件 ===")
    
    engine = EventContractDecisionEngine()
    
    signal = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=80.0,
        technical_score=70.0,
        market_status="正常",
        user_reminder="测试"
    )
    
    # 测试不同市场条件
    market_conditions = [
        {'name': '趋势市场', 'data': {'volatility': 0.02, 'trend_strength': 0.8, 'price_change_24h': 0.08}},
        {'name': '横盘市场', 'data': {'volatility': 0.01, 'trend_strength': 0.3, 'price_change_24h': 0.005}},
        {'name': '波动市场', 'data': {'volatility': 0.08, 'trend_strength': 0.5, 'price_change_24h': 0.03}},
    ]
    
    for condition in market_conditions:
        decision = engine.make_trading_decision(
            signal_result=signal,
            current_balance=1000.0,
            market_data=condition['data']
        )
        
        print(f"{condition['name']}:")
        print(f"- 市场条件: {decision.market_condition.value}")
        print(f"- 投注金额: {decision.bet_amount:.2f} USDT")
        print(f"- 风险等级: {decision.risk_level.value}")
        print()

def test_trade_recording():
    """测试交易记录功能"""
    print("\n=== 测试交易记录功能 ===")
    
    engine = EventContractDecisionEngine()
    
    # 创建决策
    signal = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=85.0,
        technical_score=75.0,
        market_status="正常",
        user_reminder="测试"
    )
    
    decision = engine.make_trading_decision(
        signal_result=signal,
        current_balance=1000.0
    )
    
    # 记录交易
    engine.record_trade(
        decision=decision,
        order_id="test_order_001",
        executed_price=42000.0,
        executed_amount=20.0
    )
    
    # 更新结果
    engine.update_trade_result(
        order_id="test_order_001",
        result="win",
        pnl=18.0
    )
    
    # 获取统计
    stats = engine.get_daily_stats()
    print(f"当日统计:")
    print(f"- 总交易数: {stats['total_trades']}")
    print(f"- 胜率: {stats['win_rate']:.1%}")
    print(f"- 总盈亏: {stats['total_pnl']:.2f} USDT")
    print(f"- 平均投注: {stats['avg_bet']:.2f} USDT")
    print(f"- 连胜/连败: {stats['current_streak']}")
    
    # 获取风险摘要
    risk_summary = engine.get_risk_summary()
    print(f"\n风险摘要:")
    print(f"- 当日损失: {risk_summary['daily_loss']:.2f} USDT")
    print(f"- 损失比例: {risk_summary['daily_loss_ratio']:.1%}")
    print(f"- 剩余软限制: {risk_summary['remaining_soft_limit']:.2f} USDT")
    print(f"- 当前胜率: {risk_summary['current_win_rate']:.1%}")
    print(f"- 今日胜率: {risk_summary['today_win_rate']:.1%}")

def main():
    """主函数"""
    print("事件合约决策引擎测试")
    print("=" * 50)
    
    try:
        test_basic_decision_engine()
        test_risk_management()
        test_market_conditions()
        test_trade_recording()
        
        print("\n✅ 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()