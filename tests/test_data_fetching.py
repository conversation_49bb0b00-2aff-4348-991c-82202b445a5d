#!/usr/bin/env python3
"""
测试数据获取功能
"""

import asyncio
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.platform.binance_spot import BinanceSpotRestApi
from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple
from quant import const


async def test_data_fetching():
    """测试数据获取功能"""
    print("🔄 开始测试数据获取功能...")
    
    # 加载配置
    with open("config.json", "r") as f:
        config = json.load(f)
    
    # 创建API客户端
    spot_api = BinanceSpotRestApi(
        access_key=config["PLATFORMS"]["binance"]["access_key"],
        secret_key=config["PLATFORMS"]["binance"]["secret_key"]
    )
    
    # 获取1分钟K线数据
    print("📊 获取BTCUSDT 1分钟K线数据...")
    success, error = spot_api.get_kline("BTC/USDT", const.KLINE_1M)
    
    if success:
        print(f"✅ 成功获取 {len(success)} 根K线数据")
        
        # 创建信号生成器
        signal_generator = EventContractSignalGeneratorSimple()
        
        # 只使用最近50根K线数据
        recent_klines = success[-50:]
        
        # 将数据输入信号生成器
        print("📈 将数据输入信号生成器...")
        for i, kline in enumerate(recent_klines):
            timestamp = int(kline[0])
            open_price = float(kline[1])
            high_price = float(kline[2])
            low_price = float(kline[3])
            close_price = float(kline[4])
            volume = float(kline[5])
            
            signal_generator.add_kline_data(
                timestamp=timestamp,
                open_price=open_price,
                high_price=high_price,
                low_price=low_price,
                close_price=close_price,
                volume=volume
            )
            
            if i < 5 or i >= len(recent_klines) - 5:  # 只显示前5个和后5个
                print(f"  {i+1}. 时间: {timestamp}, 价格: {close_price:.2f}")
            elif i == 5:
                print("  ... (省略中间数据)")
        
        # 检查数据状态
        print("\n📊 检查数据状态:")
        klines_1m = list(signal_generator.klines['1m'])
        klines_5m = list(signal_generator.klines['5m'])
        klines_15m = list(signal_generator.klines['15m'])
        
        print(f"  1分钟K线: {len(klines_1m)} 根")
        print(f"  5分钟K线: {len(klines_5m)} 根")
        print(f"  15分钟K线: {len(klines_15m)} 根")
        
        # 生成信号测试
        print("\n🎯 测试信号生成...")
        signal = signal_generator.generate_signal()
        print(f"  信号结果: {signal.has_signal}")
        print(f"  方向: {signal.direction}")
        print(f"  置信度: {signal.confidence:.2f}%")
        print(f"  市场状态: {signal.market_status}")
        
        # 显示最新价格
        if recent_klines:
            latest_price = float(recent_klines[-1][4])
            print(f"  最新价格: {latest_price:.2f} USDT")
        
    else:
        print(f"❌ 获取K线数据失败: {error}")


if __name__ == "__main__":
    asyncio.run(test_data_fetching()) 