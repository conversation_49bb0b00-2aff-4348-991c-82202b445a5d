#!/usr/bin/env python3
"""
信号时机优化测试主程序
对比不同时间延迟对交易信号胜率的影响
"""

import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'tests/timing_optimization_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

from quant.strategies.signal_timing_optimizer import SignalTimingOptimizer, TimingTestConfig
from quant.strategies.delayed_signal_generator import MultiDelaySignalManager
from quant.strategies.backtest_data_simulator import BacktestDataSimulator, BacktestConfig


class SignalTimingOptimizationTest:
    """信号时机优化测试类"""
    
    def __init__(self):
        """初始化测试"""
        # 测试配置
        self.timing_config = TimingTestConfig(
            delay_minutes_range=list(range(0, 10)),  # 0-9分钟延迟
            test_days=7,  # 测试7天数据
            min_signals_per_delay=20,  # 每个延迟至少20个信号
            settlement_minutes=15,  # 15分钟后结算
            win_threshold=0.001,  # 0.1%胜利阈值
            output_dir="tests/timing_optimization_results"
        )
        
        # 回测配置
        self.backtest_config = BacktestConfig(
            symbol="BTC/USDT",
            start_date="2024-01-15",  # 使用最近的数据
            end_date="2024-01-22",    # 7天数据
            timeframe="1m",
            data_source="mock",  # 使用模拟数据进行测试
            simulation_speed=1.0
        )
        
        # 初始化组件
        self.optimizer = SignalTimingOptimizer(self.timing_config)
        self.signal_manager = MultiDelaySignalManager(self.timing_config.delay_minutes_range)
        self.data_simulator = BacktestDataSimulator(self.backtest_config)
        
        # 结算跟踪
        self.pending_settlements = {}  # {signal_id: settlement_time}
        
        logger.info("信号时机优化测试初始化完成")
    
    async def run_test(self):
        """运行完整测试"""
        try:
            logger.info("=" * 60)
            logger.info("开始信号时机优化测试")
            logger.info("=" * 60)
            
            # 1. 加载历史数据
            logger.info("步骤 1: 加载历史数据...")
            if not await self.data_simulator.load_historical_data():
                logger.error("历史数据加载失败")
                return False
            
            data_info = self.data_simulator.get_data_info()
            logger.info(f"数据加载完成: {data_info['total_klines']} 根K线")
            logger.info(f"时间范围: {data_info['first_time']} ~ {data_info['last_time']}")
            
            # 2. 运行回测
            logger.info("步骤 2: 运行回测...")
            await self._run_backtest()
            
            # 3. 计算统计数据
            logger.info("步骤 3: 计算统计数据...")
            statistics = self.optimizer.calculate_all_statistics()
            
            # 4. 生成报告
            logger.info("步骤 4: 生成分析报告...")
            report = self.optimizer.generate_comparison_report()
            print("\n" + report)
            
            # 5. 保存详细结果
            logger.info("步骤 5: 保存详细结果...")
            result_file = self.optimizer.save_detailed_results()
            
            # 6. 显示最佳时机
            best_delay, best_stats = self.optimizer.get_best_timing()
            logger.info("=" * 60)
            logger.info("测试完成！")
            logger.info(f"最佳信号时机: 延迟 {best_delay} 分钟")
            logger.info(f"最高胜率: {best_stats.win_rate:.1f}%")
            logger.info(f"信号数量: {best_stats.total_signals}")
            logger.info(f"详细结果已保存到: {result_file}")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"测试运行异常: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    async def _run_backtest(self):
        """运行回测"""
        processed_klines = 0
        generated_signals = 0
        settled_signals = 0
        
        # 重置数据指针
        self.data_simulator.reset()
        
        # 逐个处理K线数据
        while self.data_simulator.has_more_data():
            # 获取批量K线数据
            klines = self.data_simulator.get_kline_batch(batch_size=50)
            
            for kline in klines:
                processed_klines += 1
                
                # 添加K线数据到所有延迟生成器
                self.signal_manager.add_kline_data(
                    timestamp=kline['timestamp'],
                    open_price=kline['open'],
                    high_price=kline['high'],
                    low_price=kline['low'],
                    close_price=kline['close'],
                    volume=kline['volume']
                )
                
                # 检查触发的信号
                triggered_signals = self.signal_manager.get_all_triggered_signals()
                
                for delay_minutes, signal_data in triggered_signals.items():
                    if signal_data is not None:
                        signal_result, trigger_time, signal_price = signal_data
                        
                        # 记录信号
                        signal_id = self.optimizer.add_signal_record(
                            delay_minutes=delay_minutes,
                            signal_result=signal_result,
                            signal_price=signal_price,
                            timestamp=trigger_time
                        )
                        
                        # 计算结算时间
                        settlement_time = trigger_time + timedelta(minutes=self.timing_config.settlement_minutes)
                        self.pending_settlements[signal_id] = settlement_time
                        
                        generated_signals += 1
                
                # 检查需要结算的信号
                current_time = datetime.fromtimestamp(kline['timestamp'] / 1000)
                settled_count = self._check_settlements(current_time, kline['close'])
                settled_signals += settled_count
                
                # 定期显示进度
                if processed_klines % 1000 == 0:
                    logger.info(f"处理进度: {processed_klines} 根K线, "
                               f"生成信号: {generated_signals}, 结算信号: {settled_signals}")
        
        logger.info(f"回测完成: 处理 {processed_klines} 根K线, "
                   f"生成 {generated_signals} 个信号, 结算 {settled_signals} 个信号")
    
    def _check_settlements(self, current_time: datetime, current_price: float) -> int:
        """检查并处理信号结算"""
        settled_count = 0
        to_settle = []
        
        # 查找需要结算的信号
        for signal_id, settlement_time in self.pending_settlements.items():
            if current_time >= settlement_time:
                to_settle.append(signal_id)
        
        # 执行结算
        for signal_id in to_settle:
            settlement_time = self.pending_settlements[signal_id]
            
            if self.optimizer.settle_signal(signal_id, current_price, settlement_time):
                settled_count += 1
            
            # 从待结算列表中移除
            del self.pending_settlements[signal_id]
        
        return settled_count
    
    def print_test_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 80)
        print("信号时机优化测试摘要")
        print("=" * 80)
        print(f"测试配置:")
        print(f"  - 延迟范围: {self.timing_config.delay_minutes_range} 分钟")
        print(f"  - 测试天数: {self.timing_config.test_days} 天")
        print(f"  - 结算时间: {self.timing_config.settlement_minutes} 分钟")
        print(f"  - 胜利阈值: {self.timing_config.win_threshold * 100:.1f}%")
        print(f"  - 数据源: {self.backtest_config.data_source}")
        print(f"  - 交易对: {self.backtest_config.symbol}")
        print("")
        
        # 显示各延迟的信号数量
        status = self.signal_manager.get_overall_status()
        print("各延迟生成器状态:")
        for delay, stat in status.items():
            print(f"  - {delay}分钟延迟: 待触发 {stat['pending_signals']}, "
                  f"已触发 {stat['triggered_signals']}")
        
        print("=" * 80)


async def main():
    """主函数"""
    try:
        # 创建测试实例
        test = SignalTimingOptimizationTest()
        
        # 打印测试摘要
        test.print_test_summary()
        
        # 运行测试
        success = await test.run_test()
        
        if success:
            logger.info("测试成功完成！")
            return 0
        else:
            logger.error("测试失败！")
            return 1
            
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        return 1
    except Exception as e:
        logger.error(f"测试异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    # 创建输出目录
    os.makedirs("tests/timing_optimization_results", exist_ok=True)
    
    # 运行测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
