#!/usr/bin/env python3
"""
快速钉钉测试脚本
用于快速测试钉钉消息发送功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
from quant.config import config
from quant.utils.dingtalk import Dingtalk

def quick_test():
    """快速测试钉钉消息发送"""
    print("🚀 快速钉钉测试")
    print("-" * 30)
    
    # 加载配置
    config.loads("config.json")
    
    # 发送测试消息
    test_message = f"""### 🚀 钉钉**交易**系统测试

> **测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

> **测试内容**: 币安事件合约**交易**系统正常运行

> **状态**: 系统正常 ✅

祝**交易**顺利！🚀**小火箭**起飞！"""
    
    success, error = Dingtalk.markdown(content=test_message, token=config.dingtalk)
    
    if success:
        print("✅ 钉钉消息发送成功！")
        print(f"响应: {success}")
        print("📱 请检查您的钉钉群是否收到消息")
    else:
        print("❌ 钉钉消息发送失败！")
        print(f"错误: {error}")

if __name__ == "__main__":
    quick_test() 