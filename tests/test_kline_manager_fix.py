#!/usr/bin/env python3
"""
测试kline_manager属性修复
验证主策略不再尝试访问signal_generator.kline_manager
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.utils import logger


def test_signal_generator_simple_attributes():
    """测试EventContractSignalGeneratorSimple的属性"""
    logger.info("🧪 测试EventContractSignalGeneratorSimple的属性...")
    
    try:
        from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple
        
        # 创建信号生成器实例
        signal_generator = EventContractSignalGeneratorSimple()
        
        # 检查是否有klines属性
        assert hasattr(signal_generator, 'klines'), "signal_generator缺少klines属性"
        logger.info("✅ signal_generator有klines属性")
        
        # 检查klines的结构
        assert isinstance(signal_generator.klines, dict), "klines不是字典类型"
        assert '1m' in signal_generator.klines, "klines缺少1m键"
        logger.info("✅ klines结构正确")
        
        # 检查是否没有kline_manager属性
        has_kline_manager = hasattr(signal_generator, 'kline_manager')
        logger.info(f"📊 signal_generator有kline_manager属性: {has_kline_manager}")
        
        if has_kline_manager:
            logger.warning("⚠️ signal_generator仍然有kline_manager属性，这可能不是问题")
        else:
            logger.info("✅ signal_generator没有kline_manager属性，符合预期")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_main_strategy_code_fix():
    """测试主策略代码修复"""
    logger.info("🧪 测试主策略代码是否已修复...")
    
    try:
        # 读取主策略文件内容
        main_strategy_file = "quant/strategies/event_contract_main_strategy.py"
        
        with open(main_strategy_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有.kline_manager.get_klines的调用
        if '.kline_manager.get_klines' in content:
            logger.error("❌ 主策略文件中仍然包含.kline_manager.get_klines调用")
            return False
        else:
            logger.info("✅ 主策略文件中已经没有.kline_manager.get_klines调用")
        
        # 检查是否有正确的klines访问
        if "list(self.signal_generator.klines['1m'])" in content:
            logger.info("✅ 主策略文件中包含正确的klines访问方式")
        else:
            logger.warning("⚠️ 主策略文件中可能没有正确的klines访问方式")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pending_signal_evaluation_code():
    """测试pending signal评估代码"""
    logger.info("🧪 测试pending signal评估代码...")
    
    try:
        # 读取主策略文件内容
        main_strategy_file = "quant/strategies/event_contract_main_strategy.py"
        
        with open(main_strategy_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 找到_evaluate_pending_signal方法
        in_method = False
        method_lines = []
        
        for line in lines:
            if 'def _evaluate_pending_signal(self):' in line:
                in_method = True
            elif in_method and line.startswith('    def '):
                break
            elif in_method:
                method_lines.append(line)
        
        if not method_lines:
            logger.error("❌ 找不到_evaluate_pending_signal方法")
            return False
        
        method_content = ''.join(method_lines)
        
        # 检查关键修复点
        if "list(self.signal_generator.klines['1m'])" in method_content:
            logger.info("✅ _evaluate_pending_signal方法使用正确的klines访问方式")
        else:
            logger.error("❌ _evaluate_pending_signal方法没有正确的klines访问方式")
            return False
        
        # 检查是否有长度保护
        if "len(klines_1m_all)" in method_content:
            logger.info("✅ _evaluate_pending_signal方法包含长度保护")
        else:
            logger.warning("⚠️ _evaluate_pending_signal方法可能缺少长度保护")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始kline_manager属性修复测试")
    logger.info("=" * 60)
    
    test_results = []
    
    # 运行测试
    test_results.append(test_signal_generator_simple_attributes())
    test_results.append(test_main_strategy_code_fix())
    test_results.append(test_pending_signal_evaluation_code())
    
    # 汇总结果
    passed = sum(test_results)
    total = len(test_results)
    
    logger.info(f"\\n🎯 测试结果汇总:")
    logger.info(f"✅ 通过: {passed}/{total}")
    logger.info(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！kline_manager属性修复成功")
        logger.info("\\n💡 修复总结:")
        logger.info("   - 修复了主策略中的.kline_manager.get_klines调用")
        logger.info("   - 改为使用list(self.signal_generator.klines['1m'])")
        logger.info("   - 添加了长度保护以防止索引错误")
        logger.info("   - 主策略应该不再抛出AttributeError")
        return True
    else:
        logger.error("⚠️ 部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\\n🎉 kline_manager属性修复测试全部通过！")
        print("\\n🔧 修复内容:")
        print("   - 主策略不再尝试访问signal_generator.kline_manager")
        print("   - 改为直接访问signal_generator.klines['1m']")
        print("   - 添加了数据长度保护")
        print("\\n✅ 原错误已修复，系统应该可以正常运行")
    else:
        print("\\n⚠️ kline_manager属性修复测试存在问题，请检查实现")