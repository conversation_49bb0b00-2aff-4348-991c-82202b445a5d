#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版信号生成器测试脚本

测试不依赖logger的简化版信号生成器
"""

import time
import random
from datetime import datetime, timedelta

# 导入简化版信号生成器
from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple


def generate_mock_kline_data(base_price=95000, volatility=0.02):
    """生成模拟K线数据"""
    # 生成随机价格变化
    change_pct = (random.random() - 0.5) * volatility  # -1% 到 +1%
    
    open_price = base_price
    close_price = open_price * (1 + change_pct)
    
    # 计算高低价
    high_price = max(open_price, close_price) * (1 + random.random() * 0.005)  # 上影线
    low_price = min(open_price, close_price) * (1 - random.random() * 0.005)   # 下影线
    
    # 随机成交量
    volume = 1000 + random.random() * 2000
    
    return open_price, high_price, low_price, close_price, volume


def test_signal_generator():
    """测试信号生成器"""
    print("🚀 开始测试简化版事件合约信号生成器")
    print("=" * 60)
    
    # 初始化信号生成器
    generator = EventContractSignalGeneratorSimple(
        signal_threshold=85.0,      # 降低阈值以便测试
        min_timeframe_consensus=1,   # 降低共识要求
        confidence_threshold=70.0    # 降低置信度要求
    )
    
    print(f"✅ 信号生成器初始化完成")
    print(f"📊 配置: 信号阈值={generator.signal_threshold}%, "
          f"最少共识={generator.min_timeframe_consensus}, "
          f"置信度阈值={generator.confidence_threshold}%")
    
    # 生成测试数据 (模拟120分钟的1分钟K线数据)
    print("\n📈 开始生成模拟K线数据...")
    
    base_price = 95000
    start_time = int(datetime.now().timestamp() * 1000)
    
    # 生成足够的历史数据以便计算技术指标
    for i in range(240):  # 生成240分钟的数据（4小时），确保足够的15分钟K线
        timestamp = start_time + i * 60 * 1000  # 每分钟增加
        
        # 生成模拟K线数据
        open_price, high_price, low_price, close_price, volume = generate_mock_kline_data(base_price, 0.01)
        
        # 添加到信号生成器
        generator.add_kline_data(
            timestamp=timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume
        )
        
        # 更新基准价格（模拟价格趋势）
        base_price = close_price
        
        # 每10分钟显示一次进度
        if (i + 1) % 10 == 0:
            print(f"   📊 已生成 {i + 1} 分钟数据, 当前价格: ${close_price:,.2f}")
    
    print(f"✅ K线数据生成完成: 共{120}分钟")
    
    # 检查数据状态
    print("\n📋 检查各时间周期数据状态:")
    status = generator.get_status()
    
    for timeframe, info in status.items():
        count = info['total_klines']
        latest = info['latest_kline']
        if latest and latest['timestamp']:
            latest_time = datetime.fromtimestamp(latest['timestamp'] / 1000)
            kline_type = "阳线" if latest['is_bullish'] else "阴线" if latest['is_bearish'] else "十字"
            print(f"   {timeframe}: {count}根K线, 最新: {latest_time.strftime('%H:%M:%S')} "
                  f"{kline_type} ${latest['close']:,.2f}")
        else:
            print(f"   {timeframe}: {count}根K线 (无数据)")
    
    # 生成交易信号
    print("\n🎯 开始生成交易信号...")
    print("-" * 60)
    
    signal_result = generator.generate_signal()
    
    print("-" * 60)
    print("📊 信号生成结果:")
    
    if signal_result.has_signal:
        direction_emoji = "🚀" if signal_result.direction == "UP" else "📉"
        print(f"   ✅ 有效信号: {direction_emoji} {signal_result.direction}")
        print(f"   📊 置信度: {signal_result.confidence:.1f}%")
        print(f"   📈 看涨概率: {signal_result.bullish_probability:.1f}%")
        print(f"   📉 看跌概率: {signal_result.bearish_probability:.1f}%")
        print(f"   🔧 技术评分: {signal_result.technical_score:.1f}/100")
        print(f"   ⚠️ 风险等级: {signal_result.risk_level}")
        print(f"   🎯 支持周期: {', '.join(signal_result.supporting_timeframes)}")
    else:
        print(f"   ❌ 无有效信号")
        print(f"   📈 看涨概率: {signal_result.bullish_probability:.1f}%")
        print(f"   📉 看跌概率: {signal_result.bearish_probability:.1f}%")
        print(f"   📝 原因: 概率或置信度不满足阈值要求")
    
    # 显示详细的时间周期分析
    if signal_result.timeframe_analysis:
        print("\n📈 详细时间周期分析:")
        for timeframe, analysis in signal_result.timeframe_analysis.items():
            direction = analysis['dominant_direction']
            strength = analysis['signal_strength']
            confidence = analysis['confidence']
            bullish = analysis['bullish_probability']
            bearish = analysis['bearish_probability']
            
            direction_emoji = "🚀" if direction == "UP" else "📉"
            print(f"   {timeframe}: {direction_emoji} {direction} "
                  f"(强度:{strength:.1f}%, 置信:{confidence:.1f}%, "
                  f"看涨:{bullish:.1f}%, 看跌:{bearish:.1f}%)")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")
    
    return signal_result


if __name__ == "__main__":
    try:
        test_signal_generator()
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()