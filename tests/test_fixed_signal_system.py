#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的信号系统
验证信号生成和决策引擎是否能正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_signal_generator import EventContractSignalGenerator
from quant.strategies.event_contract_decision_engine import EventContractDecisionEngine
import time
import random

def test_fixed_signal_system():
    """测试修复后的信号系统"""
    print("🚀 测试修复后的信号系统...")
    
    # 创建信号生成器（使用新的低阈值参数）
    signal_generator = EventContractSignalGenerator(
        signal_threshold=60.0,      # 降低到60%
        min_timeframe_consensus=1,   # 降低到1个时间周期
        confidence_threshold=50.0    # 降低到50%
    )
    
    # 创建决策引擎
    decision_engine = EventContractDecisionEngine()
    
    # 模拟添加一些K线数据
    print("📊 添加模拟K线数据...")
    base_price = 50000.0
    
    for i in range(100):
        # 模拟价格波动
        price_change = random.uniform(-0.01, 0.01)  # ±1%波动
        current_price = base_price * (1 + price_change)
        
        # 模拟K线数据
        open_price = base_price
        high_price = max(open_price, current_price) * (1 + random.uniform(0, 0.005))
        low_price = min(open_price, current_price) * (1 - random.uniform(0, 0.005))
        close_price = current_price
        volume = random.uniform(100, 1000)
        
        timestamp = int(time.time() * 1000) + i * 60000  # 每分钟一根K线
        
        signal_generator.add_kline_data(
            timestamp=timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume
        )
        
        base_price = current_price
    
    # 尝试生成信号
    print("🔍 尝试生成交易信号...")
    signal_result = signal_generator.generate_signal()
    
    print(f"📈 信号生成结果:")
    print(f"   有信号: {signal_result.has_signal}")
    print(f"   方向: {signal_result.direction}")
    print(f"   置信度: {signal_result.confidence:.1f}%")
    print(f"   看涨概率: {signal_result.bullish_probability:.1f}%")
    print(f"   看跌概率: {signal_result.bearish_probability:.1f}%")
    print(f"   技术评分: {signal_result.technical_score:.1f}")
    print(f"   风险等级: {signal_result.risk_level}")
    
    # 测试决策引擎
    if signal_result.has_signal:
        print("🤖 测试决策引擎...")
        current_balance = 1000.0  # 模拟账户余额
        
        decision = decision_engine.make_trading_decision(
            signal_result=signal_result,
            current_balance=current_balance
        )
        
        print(f"💼 交易决策结果:")
        print(f"   应该交易: {decision.should_trade}")
        print(f"   投注金额: {decision.bet_amount:.2f} USDT")
        print(f"   方向: {decision.direction}")
        print(f"   置信度: {decision.confidence:.1f}%")
        print(f"   风险等级: {decision.risk_level.value}")
        print(f"   市场条件: {decision.market_condition.value}")
        print(f"   决策原因: {decision.reason}")
        
        if decision.should_trade:
            print("✅ 成功！系统现在可以生成交易信号了！")
        else:
            print("⚠️  信号生成但决策引擎仍然拒绝交易，可能需要进一步调整")
    else:
        print("❌ 仍然无法生成信号，需要进一步调整参数")
    
    # 显示信号生成器状态
    print("\n📊 信号生成器状态:")
    status = signal_generator.get_status()
    for timeframe, info in status.items():
        print(f"   {timeframe}: {info['total_klines']}根K线")

if __name__ == "__main__":
    test_fixed_signal_system()