#!/usr/bin/env python3
"""
简化版信号结算检查器测试
只测试核心逻辑，不依赖完整的模块结构
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
import re


@dataclass
class SignalForSettlement:
    """用于结算的信号数据结构"""
    signal_id: str
    direction: str  # UP/DOWN
    confidence: float
    stake: float
    signal_price: float
    signal_timestamp: datetime
    expiry_timestamp: datetime
    source: str  # 'recommendation' 或 'pending_signal'
    raw_content: str  # 原始钉钉消息内容
    kline_sequence: int  # K线序号
    
    def is_expired(self, current_time: datetime) -> bool:
        """检查信号是否已到期"""
        return current_time >= self.expiry_timestamp


@dataclass
class SettlementResult:
    """结算结果数据结构"""
    signal_id: str
    direction: str
    signal_price: float
    settlement_price: float
    signal_timestamp: datetime
    settlement_timestamp: datetime
    result: str  # 'WIN', 'LOSS', 'TIE'
    pnl: float
    stake: float
    confidence: float
    auto_settled: bool = True


class SimpleSignalSettlementChecker:
    """简化版信号结算检查器"""
    
    def __init__(self):
        self.win_rate_multiplier = 0.85  # 胜利时获得85%收益
        self.loss_multiplier = 1.0       # 失败时损失100%本金
        self.signal_expiry_minutes = 15
        self.settlement_cache = {}
        
    def parse_recommendation_notification(self, notification: Dict) -> Optional[SignalForSettlement]:
        """解析推荐通知，提取信号信息"""
        try:
            content = notification['content']
            timestamp = notification['timestamp']
            
            # 解析方向
            direction = None
            if 'UP' in content:
                direction = 'UP'
            elif 'DOWN' in content:
                direction = 'DOWN'
            
            if not direction:
                print(f"无法解析信号方向: {content}")
                return None
            
            # 解析投注金额
            stake_match = re.search(r'建议投入.*?(\d+\.?\d*)\s*USDT', content)
            stake = float(stake_match.group(1)) if stake_match else 20.0
            
            # 解析置信度
            confidence_match = re.search(r'置信度.*?(\d+\.?\d*)%', content)
            confidence = float(confidence_match.group(1)) if confidence_match else 50.0
            
            # 模拟历史价格
            signal_price = 50000.0  # 简化处理
            
            # 计算到期时间
            expiry_time = timestamp + timedelta(minutes=self.signal_expiry_minutes)
            
            # 生成信号ID
            signal_id = f"REC_{timestamp.strftime('%Y%m%d_%H%M%S')}"
            
            # 提取K线序号
            kline_sequence = (timestamp.hour * 4) + (timestamp.minute // 15) + 1
            
            return SignalForSettlement(
                signal_id=signal_id,
                direction=direction,
                confidence=confidence,
                stake=stake,
                signal_price=signal_price,
                signal_timestamp=timestamp,
                expiry_timestamp=expiry_time,
                source='recommendation',
                raw_content=content,
                kline_sequence=kline_sequence
            )
            
        except Exception as e:
            print(f"解析推荐通知失败: {e}")
            return None
    
    def settle_signal(self, signal: SignalForSettlement, current_price: float, settlement_time: datetime) -> Optional[SettlementResult]:
        """结算单个信号"""
        try:
            # 判断胜负
            if signal.direction == 'UP':
                is_win = current_price > signal.signal_price
            elif signal.direction == 'DOWN':
                is_win = current_price < signal.signal_price
            else:
                print(f"未知的交易方向: {signal.direction}")
                return None
            
            # 计算盈亏
            if is_win:
                pnl = signal.stake * self.win_rate_multiplier
                result = 'WIN'
            else:
                pnl = -signal.stake * self.loss_multiplier
                result = 'LOSS'
            
            # 特殊情况：价格相等视为平局
            if abs(current_price - signal.signal_price) < 0.01:
                pnl = 0.0
                result = 'TIE'
            
            # 创建结算结果
            settlement_result = SettlementResult(
                signal_id=signal.signal_id,
                direction=signal.direction,
                signal_price=signal.signal_price,
                settlement_price=current_price,
                signal_timestamp=signal.signal_timestamp,
                settlement_timestamp=settlement_time,
                result=result,
                pnl=pnl,
                stake=signal.stake,
                confidence=signal.confidence
            )
            
            return settlement_result
            
        except Exception as e:
            print(f"结算信号失败: {e}")
            return None
    
    def calculate_statistics(self, settlement_results: List[SettlementResult]) -> Dict[str, Any]:
        """计算结算统计信息"""
        if not settlement_results:
            return {
                'total_settlements': 0,
                'wins': 0,
                'losses': 0,
                'ties': 0,
                'win_rate': 0.0,
                'total_pnl': 0.0,
                'avg_pnl': 0.0
            }
        
        total_settlements = len(settlement_results)
        wins = len([r for r in settlement_results if r.result == 'WIN'])
        losses = len([r for r in settlement_results if r.result == 'LOSS'])
        ties = len([r for r in settlement_results if r.result == 'TIE'])
        
        win_rate = (wins / total_settlements) * 100 if total_settlements > 0 else 0.0
        total_pnl = sum(r.pnl for r in settlement_results)
        avg_pnl = total_pnl / total_settlements if total_settlements > 0 else 0.0
        
        return {
            'total_settlements': total_settlements,
            'wins': wins,
            'losses': losses,
            'ties': ties,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'avg_pnl': avg_pnl
        }


def test_signal_parsing():
    """测试信号解析功能"""
    print("🔍 测试信号解析功能...")
    
    checker = SimpleSignalSettlementChecker()
    
    # 测试推荐通知
    now = datetime.now()
    test_notification = {
        'timestamp': now - timedelta(minutes=10),
        'type': 'recommendation',
        'content': '''### 事件合约交易推荐 🚀
- 方向: **UP**
- 建议投入: **25.50 USDT**
- 置信度: **72.5%**
- 因子得分: **68.3 /100**
- 剩余时间: 580s
- 生成时间: 14:30
---
> 该推荐仅供参考，实际操作请自行评估风控。'''
    }
    
    # 解析信号
    signal = checker.parse_recommendation_notification(test_notification)
    
    if signal:
        print(f"✅ 成功解析信号:")
        print(f"  信号ID: {signal.signal_id}")
        print(f"  方向: {signal.direction}")
        print(f"  置信度: {signal.confidence}%")
        print(f"  投注金额: {signal.stake} USDT")
        print(f"  是否到期: {signal.is_expired(now)}")
        print(f"  K线序号: {signal.kline_sequence}")
        
        # 验证解析结果
        assert signal.direction == 'UP', f"方向解析错误"
        assert signal.confidence == 72.5, f"置信度解析错误"
        assert signal.stake == 25.50, f"投注金额解析错误"
        
        print("✅ 信号解析测试通过！")
        return signal
    else:
        print("❌ 信号解析失败")
        return None


def test_settlement_logic():
    """测试结算逻辑"""
    print("\n🔍 测试结算逻辑...")
    
    checker = SimpleSignalSettlementChecker()
    
    # 测试案例
    test_cases = [
        {
            'name': '看涨胜利',
            'direction': 'UP',
            'signal_price': 50000.0,
            'current_price': 51000.0,
            'expected_result': 'WIN',
            'stake': 20.0
        },
        {
            'name': '看涨失败',
            'direction': 'UP',
            'signal_price': 50000.0,
            'current_price': 49000.0,
            'expected_result': 'LOSS',
            'stake': 20.0
        },
        {
            'name': '看跌胜利',
            'direction': 'DOWN',
            'signal_price': 50000.0,
            'current_price': 49000.0,
            'expected_result': 'WIN',
            'stake': 20.0
        },
        {
            'name': '看跌失败',
            'direction': 'DOWN',
            'signal_price': 50000.0,
            'current_price': 51000.0,
            'expected_result': 'LOSS',
            'stake': 20.0
        },
        {
            'name': '平局',
            'direction': 'UP',
            'signal_price': 50000.0,
            'current_price': 50000.0,
            'expected_result': 'TIE',
            'stake': 20.0
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n  测试: {test_case['name']}")
        
        # 创建测试信号
        signal = SignalForSettlement(
            signal_id=f"TEST_{test_case['name']}",
            direction=test_case['direction'],
            confidence=75.0,
            stake=test_case['stake'],
            signal_price=test_case['signal_price'],
            signal_timestamp=datetime.now() - timedelta(minutes=20),
            expiry_timestamp=datetime.now() - timedelta(minutes=5),
            source='recommendation',
            raw_content=f"测试信号 - {test_case['name']}",
            kline_sequence=80
        )
        
        # 执行结算
        result = checker.settle_signal(signal, test_case['current_price'], datetime.now())
        
        if result:
            results.append(result)
            print(f"    结果: {result.result}")
            print(f"    盈亏: {result.pnl:.2f} USDT")
            
            # 验证结果
            assert result.result == test_case['expected_result'], f"结算结果错误: 期望 {test_case['expected_result']}, 实际 {result.result}"
            
            # 验证盈亏计算
            if test_case['expected_result'] == 'WIN':
                expected_pnl = test_case['stake'] * 0.85
                assert abs(result.pnl - expected_pnl) < 0.01, f"胜利盈亏计算错误"
            elif test_case['expected_result'] == 'LOSS':
                expected_pnl = -test_case['stake']
                assert abs(result.pnl - expected_pnl) < 0.01, f"失败盈亏计算错误"
            elif test_case['expected_result'] == 'TIE':
                assert abs(result.pnl - 0.0) < 0.01, f"平局盈亏应该为0"
            
            print(f"    ✅ 验证通过")
        else:
            print(f"    ❌ 结算失败")
    
    print(f"\n✅ 结算逻辑测试完成，共测试 {len(results)} 个案例")
    return results


def test_statistics_calculation():
    """测试统计计算功能"""
    print("\n🔍 测试统计计算功能...")
    
    checker = SimpleSignalSettlementChecker()
    
    # 创建测试结算结果
    settlement_results = [
        SettlementResult(
            signal_id="TEST_1",
            direction="UP",
            signal_price=50000.0,
            settlement_price=51000.0,
            signal_timestamp=datetime.now() - timedelta(hours=1),
            settlement_timestamp=datetime.now(),
            result="WIN",
            pnl=17.0,
            stake=20.0,
            confidence=75.0
        ),
        SettlementResult(
            signal_id="TEST_2",
            direction="DOWN",
            signal_price=50000.0,
            settlement_price=51000.0,
            signal_timestamp=datetime.now() - timedelta(hours=1),
            settlement_timestamp=datetime.now(),
            result="LOSS",
            pnl=-25.0,
            stake=25.0,
            confidence=65.0
        ),
        SettlementResult(
            signal_id="TEST_3",
            direction="UP",
            signal_price=50000.0,
            settlement_price=50000.0,
            signal_timestamp=datetime.now() - timedelta(hours=1),
            settlement_timestamp=datetime.now(),
            result="TIE",
            pnl=0.0,
            stake=30.0,
            confidence=80.0
        )
    ]
    
    # 计算统计信息
    stats = checker.calculate_statistics(settlement_results)
    
    print(f"📊 统计结果:")
    print(f"  总结算数: {stats['total_settlements']}")
    print(f"  胜利数: {stats['wins']}")
    print(f"  失败数: {stats['losses']}")
    print(f"  平局数: {stats['ties']}")
    print(f"  胜率: {stats['win_rate']:.1f}%")
    print(f"  总盈亏: {stats['total_pnl']:.2f} USDT")
    print(f"  平均盈亏: {stats['avg_pnl']:.2f} USDT")
    
    # 验证统计结果
    assert stats['total_settlements'] == 3, f"总结算数错误"
    assert stats['wins'] == 1, f"胜利数错误"
    assert stats['losses'] == 1, f"失败数错误"
    assert stats['ties'] == 1, f"平局数错误"
    assert abs(stats['win_rate'] - 33.3) < 0.1, f"胜率计算错误"
    assert abs(stats['total_pnl'] - (-8.0)) < 0.1, f"总盈亏计算错误"
    
    print("✅ 统计计算测试通过！")
    return stats


def test_complete_workflow():
    """测试完整工作流程"""
    print("\n🔍 测试完整工作流程...")
    
    checker = SimpleSignalSettlementChecker()
    
    # 模拟通知历史
    now = datetime.now()
    notifications = [
        {
            'timestamp': now - timedelta(minutes=20),
            'type': 'recommendation',
            'content': '''### 事件合约交易推荐 🚀
- 方向: **UP**
- 建议投入: **30.00 USDT**
- 置信度: **78.5%**
- 因子得分: **82.1 /100**
- 剩余时间: 600s
- 生成时间: 14:30
---
> 该推荐仅供参考，实际操作请自行评估风控。'''
        },
        {
            'timestamp': now - timedelta(minutes=18),
            'type': 'recommendation',
            'content': '''### 事件合约交易推荐 📉
- 方向: **DOWN**
- 建议投入: **25.00 USDT**
- 置信度: **65.2%**
- 因子得分: **71.8 /100**
- 剩余时间: 520s
- 生成时间: 14:32
---
> 该推荐仅供参考，实际操作请自行评估风控。'''
        }
    ]
    
    # 1. 解析信号
    signals = []
    for notification in notifications:
        signal = checker.parse_recommendation_notification(notification)
        if signal:
            signals.append(signal)
    
    print(f"📊 解析到 {len(signals)} 个信号")
    
    # 2. 模拟结算
    current_price = 52000.0  # 模拟价格上涨
    settlement_results = []
    
    for signal in signals:
        if signal.is_expired(now):
            result = checker.settle_signal(signal, current_price, now)
            if result:
                settlement_results.append(result)
                print(f"  结算信号 {signal.signal_id}: {result.result}, 盈亏: {result.pnl:.2f} USDT")
    
    # 3. 计算统计
    stats = checker.calculate_statistics(settlement_results)
    
    print(f"\n📈 完整流程统计:")
    print(f"  已结算信号: {stats['total_settlements']}")
    print(f"  胜率: {stats['win_rate']:.1f}%")
    print(f"  总盈亏: {stats['total_pnl']:.2f} USDT")
    
    # 验证结果
    assert len(settlement_results) == 2, f"应该结算2个信号"
    assert settlement_results[0].result == 'WIN', f"第一个信号（UP）应该胜利"
    assert settlement_results[1].result == 'LOSS', f"第二个信号（DOWN）应该失败"
    
    print("✅ 完整工作流程测试通过！")
    return settlement_results, stats


def main():
    """主测试函数"""
    print("🚀 开始测试简化版信号结算检查器...")
    
    try:
        # 运行所有测试
        test_signal_parsing()
        test_settlement_logic()
        test_statistics_calculation()
        test_complete_workflow()
        
        print("\n🎉 所有测试完成！")
        print("\n💡 信号结算检查器功能验证:")
        print("1. ✅ 信号解析功能正常")
        print("2. ✅ 结算逻辑正确")
        print("3. ✅ 统计计算准确")
        print("4. ✅ 完整工作流程正常")
        
        print("\n🔧 核心功能说明:")
        print("- 🔍 从钉钉通知中解析推荐信号")
        print("- ⏰ 检查信号到期时间（15分钟）")
        print("- 🎯 根据价格变化判断胜负")
        print("- 💰 计算盈亏（胜利+85%，失败-100%）")
        print("- 📊 统计胜率和总盈亏")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()