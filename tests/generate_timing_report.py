#!/usr/bin/env python3
"""
信号时机优化报告生成器
分析测试结果并生成可视化报告
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime

# 可选导入
try:
    import matplotlib.pyplot as plt
    import numpy as np
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def load_test_results(json_file_path: str) -> dict:
    """加载测试结果"""
    with open(json_file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def generate_summary_report(results: dict) -> str:
    """生成摘要报告"""
    config = results['config']
    statistics = results['statistics']
    
    report = []
    report.append("=" * 80)
    report.append("🎯 信号时机优化测试结果摘要")
    report.append("=" * 80)
    report.append("")
    
    # 测试配置
    report.append("📋 测试配置:")
    report.append(f"   • 延迟范围: {config['delay_minutes_range']} 分钟")
    report.append(f"   • 结算时间: {config['settlement_minutes']} 分钟")
    report.append(f"   • 胜利阈值: {config['win_threshold'] * 100:.1f}%")
    report.append(f"   • 最小信号数: {config['min_signals_per_delay']}")
    report.append("")
    
    # 核心发现
    report.append("🔍 核心发现:")
    
    # 找出最佳延迟
    best_delay = max(statistics.keys(), key=lambda k: statistics[k]['win_rate'])
    best_stats = statistics[best_delay]
    worst_delay = min(statistics.keys(), key=lambda k: statistics[k]['win_rate'])
    worst_stats = statistics[worst_delay]
    
    report.append(f"   🏆 最佳延迟: {best_delay} 分钟")
    report.append(f"      - 胜率: {best_stats['win_rate']:.1f}%")
    report.append(f"      - 信号数: {best_stats['total_signals']}")
    report.append(f"      - 平均收益: {best_stats['avg_pnl'] * 100:.3f}%")
    report.append("")
    
    report.append(f"   📉 最差延迟: {worst_delay} 分钟")
    report.append(f"      - 胜率: {worst_stats['win_rate']:.1f}%")
    report.append(f"      - 胜率差异: {best_stats['win_rate'] - worst_stats['win_rate']:.1f}%")
    report.append("")
    
    # 胜率趋势分析
    delays = sorted([int(k) for k in statistics.keys()])
    win_rates = [statistics[str(d)]['win_rate'] for d in delays]
    
    if win_rates[-1] > win_rates[0]:
        trend = "📈 上升趋势"
        recommendation = "建议使用较长延迟时间"
    elif win_rates[-1] < win_rates[0]:
        trend = "📉 下降趋势"
        recommendation = "建议使用较短延迟时间"
    else:
        trend = "➡️ 平稳趋势"
        recommendation = "延迟时间对胜率影响较小"
    
    report.append(f"   📊 胜率趋势: {trend}")
    report.append(f"   💡 建议: {recommendation}")
    report.append("")
    
    # 详细统计表
    report.append("📈 详细统计:")
    report.append("-" * 80)
    report.append(f"{'延迟':<6} {'信号数':<8} {'胜率':<8} {'胜/负/平':<12} {'平均PnL':<10} {'置信度':<8}")
    report.append("-" * 80)
    
    for delay in delays:
        stats = statistics[str(delay)]
        win_loss_tie = f"{stats['win_count']}/{stats['loss_count']}/{stats['tie_count']}"
        report.append(f"{delay}分钟   {stats['total_signals']:<8} "
                     f"{stats['win_rate']:<8.1f} {win_loss_tie:<12} "
                     f"{stats['avg_pnl']*100:<10.3f} {stats['avg_confidence']:<8.1f}")
    
    report.append("-" * 80)
    report.append("")
    
    # 关键洞察
    report.append("💡 关键洞察:")
    
    # 计算胜率改进
    improvement = best_stats['win_rate'] - worst_stats['win_rate']
    if improvement > 2:
        report.append(f"   • 最优时机可提升胜率 {improvement:.1f}%，具有实际意义")
    elif improvement > 1:
        report.append(f"   • 最优时机可提升胜率 {improvement:.1f}%，有一定改进空间")
    else:
        report.append(f"   • 不同延迟的胜率差异较小（{improvement:.1f}%），时机影响有限")
    
    # 信号质量分析
    avg_confidence = sum(stats['avg_confidence'] for stats in statistics.values()) / len(statistics)
    if avg_confidence > 70:
        report.append(f"   • 信号质量较高（平均置信度 {avg_confidence:.1f}%）")
    elif avg_confidence > 60:
        report.append(f"   • 信号质量中等（平均置信度 {avg_confidence:.1f}%）")
    else:
        report.append(f"   • 信号质量偏低（平均置信度 {avg_confidence:.1f}%），需要优化策略")
    
    # 收益分析
    avg_pnl = sum(stats['avg_pnl'] for stats in statistics.values()) / len(statistics)
    if avg_pnl > 0:
        report.append(f"   • 整体策略盈利（平均收益 {avg_pnl*100:.3f}%）")
    else:
        report.append(f"   • 整体策略亏损（平均收益 {avg_pnl*100:.3f}%），需要进一步优化")
    
    report.append("")
    
    # 实施建议
    report.append("🚀 实施建议:")
    report.append(f"   1. 将信号延迟时间设置为 {best_delay} 分钟")
    report.append(f"   2. 预期胜率提升至 {best_stats['win_rate']:.1f}%")
    
    if best_stats['win_rate'] > 50:
        report.append("   3. 胜率超过50%，策略具有正期望值")
    else:
        report.append("   3. 胜率仍低于50%，建议结合其他优化措施")
    
    report.append("   4. 持续监控实际效果，根据市场变化调整")
    report.append("")
    
    report.append("=" * 80)
    report.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("=" * 80)
    
    return "\n".join(report)


def create_visualization(results: dict, output_dir: str):
    """创建可视化图表"""
    if not HAS_MATPLOTLIB:
        print("⚠️ matplotlib未安装，跳过图表生成")
        return None

    statistics = results['statistics']
    delays = sorted([int(k) for k in statistics.keys()])

    # 准备数据
    win_rates = [statistics[str(d)]['win_rate'] for d in delays]
    signal_counts = [statistics[str(d)]['total_signals'] for d in delays]
    avg_pnls = [statistics[str(d)]['avg_pnl'] * 100 for d in delays]

    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Signal Timing Optimization Results', fontsize=16, fontweight='bold')
    
    # 1. 胜率对比
    ax1.bar(delays, win_rates, color='skyblue', alpha=0.7)
    ax1.set_title('Win Rate by Delay Time')
    ax1.set_xlabel('Delay Time (minutes)')
    ax1.set_ylabel('Win Rate (%)')
    ax1.grid(True, alpha=0.3)
    
    # 标注最佳点
    best_idx = win_rates.index(max(win_rates))
    ax1.annotate(f'Best: {delays[best_idx]}min\n{win_rates[best_idx]:.1f}%',
                xy=(delays[best_idx], win_rates[best_idx]),
                xytext=(delays[best_idx], win_rates[best_idx] + 1),
                arrowprops=dict(arrowstyle='->', color='red'),
                ha='center', fontweight='bold', color='red')

    # 2. 信号数量
    ax2.plot(delays, signal_counts, marker='o', linewidth=2, markersize=6)
    ax2.set_title('Signal Count by Delay Time')
    ax2.set_xlabel('Delay Time (minutes)')
    ax2.set_ylabel('Signal Count')
    ax2.grid(True, alpha=0.3)

    # 3. 平均收益
    colors = ['green' if pnl >= 0 else 'red' for pnl in avg_pnls]
    ax3.bar(delays, avg_pnls, color=colors, alpha=0.7)
    ax3.set_title('Average PnL by Delay Time')
    ax3.set_xlabel('Delay Time (minutes)')
    ax3.set_ylabel('Average PnL (%)')
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax3.grid(True, alpha=0.3)

    # 4. 胜率趋势线
    ax4.plot(delays, win_rates, marker='o', linewidth=2, markersize=6, color='blue')
    ax4.fill_between(delays, win_rates, alpha=0.3, color='blue')
    ax4.set_title('Win Rate Trend Analysis')
    ax4.set_xlabel('Delay Time (minutes)')
    ax4.set_ylabel('Win Rate (%)')
    ax4.grid(True, alpha=0.3)
    
    # 添加趋势线
    z = np.polyfit(delays, win_rates, 1)
    p = np.poly1d(z)
    ax4.plot(delays, p(delays), "--", alpha=0.8, color='red',
             label=f'Trend Line (slope: {z[0]:.2f})')
    ax4.legend()

    plt.tight_layout()

    # 保存图表
    chart_path = os.path.join(output_dir, 'timing_optimization_charts.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📊 可视化图表已保存到: {chart_path}")
    return chart_path


def main():
    """主函数"""
    # 查找最新的结果文件
    results_dir = Path("tests/timing_optimization_results")
    if not results_dir.exists():
        print("❌ 结果目录不存在，请先运行测试")
        return 1
    
    json_files = list(results_dir.glob("timing_optimization_*.json"))
    if not json_files:
        print("❌ 未找到测试结果文件")
        return 1
    
    # 使用最新的文件
    latest_file = max(json_files, key=lambda f: f.stat().st_mtime)
    print(f"📁 使用结果文件: {latest_file}")
    
    # 加载结果
    results = load_test_results(str(latest_file))
    
    # 生成报告
    report = generate_summary_report(results)
    print("\n" + report)
    
    # 保存报告
    report_path = results_dir / f"summary_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"📄 详细报告已保存到: {report_path}")
    
    # 创建可视化图表（需要matplotlib）
    if HAS_MATPLOTLIB:
        # 设置字体
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        create_visualization(results, str(results_dir))
    else:
        print("⚠️ matplotlib未安装，跳过图表生成")
        print("   安装命令: pip install matplotlib")
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
