#!/usr/bin/env python3
"""
择时信号去重系统模拟测试

使用模拟的钉钉发送来测试去重逻辑，不依赖实际的网络请求
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import time
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier, NotificationConfig


class MockSignalDeduplicationTester:
    """模拟信号去重测试器"""
    
    def __init__(self):
        # 配置测试用的通知器
        self.config = NotificationConfig(
            enable_signal_notification=True,
            enable_signal_deduplication=True,
            min_recommendation_interval=300,  # 5分钟间隔
            similarity_threshold=0.8,
            dedup_time_window=1800,  # 30分钟窗口
            max_daily_notifications=10
        )
        self.notifier = EventContractDingtalkNotifier(self.config)
        
        # 模拟钉钉发送总是成功
        self.mock_send_success = True
        self.send_call_count = 0
        
    def mock_dingtalk_send(self, message: str) -> Tuple[bool, Optional[str]]:
        """模拟钉钉发送"""
        self.send_call_count += 1
        return self.mock_send_success, None
        
    def test_duplicate_detection_logic(self):
        """测试重复信号检测逻辑（不实际发送）"""
        print("🔍 测试重复信号检测逻辑")
        print("=" * 50)
        
        # 使用模拟发送
        with patch.object(self.notifier, '_send_dingtalk_message', side_effect=self.mock_dingtalk_send):
            # 创建相似的推荐
            base_recommendation = {
                'direction': 'UP',
                'confidence': 75.0,
                'stake': 20.0,
                'score': 45.0,
                'remaining_time': 300,
                'generated_at': datetime.now().isoformat()
            }
            
            # 测试完全相同的推荐
            duplicate_recommendation = base_recommendation.copy()
            
            # 测试相似的推荐
            similar_recommendation = base_recommendation.copy()
            similar_recommendation['confidence'] = 76.0  # 微小差异
            similar_recommendation['stake'] = 21.0
            
            # 测试不同的推荐
            different_recommendation = {
                'direction': 'DOWN',
                'confidence': 60.0,
                'stake': 25.0,
                'score': 35.0,
                'remaining_time': 400,
                'generated_at': datetime.now().isoformat()
            }
            
            self.send_call_count = 0
            
            print("1️⃣ 发送基础推荐...")
            success1, error1 = self.notifier.send_recommendation(base_recommendation)
            print(f"   结果: {'✅ 成功' if success1 else '❌ 失败'} - {error1 or '正常发送'}")
            
            print("\n2️⃣ 发送完全相同的推荐...")
            success2, error2 = self.notifier.send_recommendation(duplicate_recommendation)
            print(f"   结果: {'✅ 成功' if success2 else '❌ 失败'} - {error2 or '应该被去重'}")
            
            print("\n3️⃣ 发送相似的推荐...")
            success3, error3 = self.notifier.send_recommendation(similar_recommendation)
            print(f"   结果: {'✅ 成功' if success3 else '❌ 失败'} - {error3 or '应该被去重'}")
            
            print("\n4️⃣ 发送不同的推荐...")
            success4, error4 = self.notifier.send_recommendation(different_recommendation)
            print(f"   结果: {'✅ 成功' if success4 else '❌ 失败'} - {error4 or '应该正常发送'}")
            
            # 验证去重效果
            print(f"\n📊 实际钉钉发送次数: {self.send_call_count}")
            print(f"📊 去重系统记录的信号数: {len(self.notifier.recent_signals)}")
            
            # 预期：基础推荐发送，重复和相似被去重，不同推荐发送
            expected_sends = 2
            actual_api_calls = self.send_call_count
            
            print(f"\n📊 去重效果:")
            print(f"   预期API调用: {expected_sends} 次")
            print(f"   实际API调用: {actual_api_calls} 次")
            print(f"   去重效果: {'✅ 正常' if actual_api_calls == expected_sends else '❌ 异常'}")
            
            return actual_api_calls == expected_sends
    
    def test_similarity_calculation_detailed(self):
        """详细测试相似度计算"""
        print("\n🔢 详细测试相似度计算")
        print("=" * 50)
        
        base_rec = {
            'direction': 'UP',
            'confidence': 75.0,
            'stake': 20.0,
            'score': 45.0
        }
        
        test_cases = [
            # (测试推荐, 预期相似度范围, 描述)
            ({'direction': 'UP', 'confidence': 75.0, 'stake': 20.0, 'score': 45.0}, (0.95, 1.0), "完全相同"),
            ({'direction': 'UP', 'confidence': 76.0, 'stake': 21.0, 'score': 46.0}, (0.85, 0.95), "微小差异"),
            ({'direction': 'UP', 'confidence': 80.0, 'stake': 25.0, 'score': 50.0}, (0.7, 0.85), "中等差异"),
            ({'direction': 'DOWN', 'confidence': 75.0, 'stake': 20.0, 'score': 45.0}, (0.0, 0.1), "方向不同"),
            ({'direction': 'UP', 'confidence': 50.0, 'stake': 10.0, 'score': 20.0}, (0.3, 0.7), "较大差异"),
        ]
        
        all_passed = True
        
        for i, (test_rec, expected_range, description) in enumerate(test_cases, 1):
            similarity = self.notifier._calculate_recommendation_similarity(base_rec, test_rec)
            min_expected, max_expected = expected_range
            passed = min_expected <= similarity <= max_expected
            
            print(f"{i}️⃣ {description}:")
            print(f"   基础推荐: {base_rec}")
            print(f"   测试推荐: {test_rec}")
            print(f"   计算相似度: {similarity:.3f}")
            print(f"   预期范围: {min_expected:.3f} - {max_expected:.3f}")
            print(f"   结果: {'✅ 通过' if passed else '❌ 失败'}")
            print()
            
            if not passed:
                all_passed = False
        
        print(f"📊 相似度计算测试: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")
        return all_passed
    
    def test_frequency_control_logic(self):
        """测试频率控制逻辑"""
        print("\n🕒 测试频率控制逻辑")
        print("=" * 50)
        
        # 重置时间状态
        self.notifier.last_recommendation_time = None
        
        with patch.object(self.notifier, '_send_dingtalk_message', side_effect=self.mock_dingtalk_send):
            recommendation = {
                'direction': 'UP',
                'confidence': 80.0,
                'stake': 20.0,
                'score': 50.0,
                'remaining_time': 400,
                'generated_at': datetime.now().isoformat()
            }
            
            results = []
            self.send_call_count = 0
            
            print("1️⃣ 快速连续发送5个不同推荐...")
            for i in range(5):
                # 修改推荐内容避免去重，但测试频率控制
                test_rec = recommendation.copy()
                test_rec['confidence'] = 80.0 + i * 10  # 大幅不同避免去重
                test_rec['direction'] = 'UP' if i % 2 == 0 else 'DOWN'  # 交替方向
                test_rec['generated_at'] = datetime.now().isoformat()
                
                success, error = self.notifier.send_recommendation(test_rec)
                results.append(success)
                
                print(f"   推荐 {i+1} ({test_rec['direction']}, {test_rec['confidence']}%): {'✅ 成功' if success else '❌ 失败'} - {error or '正常'}")
                
                # 短暂延迟
                time.sleep(0.1)
            
            successful_sends = sum(results)
            print(f"\n📊 频率控制效果:")
            print(f"   尝试发送: 5 次")
            print(f"   成功发送: {successful_sends} 次")
            print(f"   实际API调用: {self.send_call_count} 次")
            print(f"   频率控制: {'✅ 正常' if successful_sends <= 2 else '❌ 异常'}")
            
            return successful_sends <= 2
    
    def test_deduplication_with_time_window(self):
        """测试带时间窗口的去重"""
        print("\n⏰ 测试时间窗口去重")
        print("=" * 50)
        
        with patch.object(self.notifier, '_send_dingtalk_message', side_effect=self.mock_dingtalk_send):
            # 清空历史记录
            self.notifier.recent_signals = []
            self.send_call_count = 0
            
            recommendation = {
                'direction': 'UP',
                'confidence': 75.0,
                'stake': 20.0,
                'score': 45.0,
                'remaining_time': 300,
                'generated_at': datetime.now().isoformat()
            }
            
            print("1️⃣ 发送第一个推荐...")
            success1, _ = self.notifier.send_recommendation(recommendation)
            print(f"   结果: {'✅ 成功' if success1 else '❌ 失败'}")
            
            print("\n2️⃣ 立即发送相同推荐（应被去重）...")
            success2, _ = self.notifier.send_recommendation(recommendation.copy())
            print(f"   结果: {'✅ 成功' if success2 else '❌ 失败'} - 应该被去重")
            
            # 模拟时间过去，清理旧记录
            print("\n3️⃣ 模拟30分钟后，清理旧记录...")
            old_time = datetime.now() - timedelta(seconds=2000)
            for signal in self.notifier.recent_signals:
                signal['timestamp'] = old_time
            
            self.notifier._cleanup_old_signals(datetime.now())
            print(f"   清理后信号数量: {len(self.notifier.recent_signals)}")
            
            print("\n4️⃣ 再次发送相同推荐（应该成功）...")
            success3, _ = self.notifier.send_recommendation(recommendation.copy())
            print(f"   结果: {'✅ 成功' if success3 else '❌ 失败'} - 应该成功")
            
            print(f"\n📊 时间窗口测试:")
            print(f"   总API调用次数: {self.send_call_count}")
            print(f"   预期调用次数: 2 (第1次和清理后)")
            
            return self.send_call_count == 2
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 择时信号去重系统模拟测试")
        print("=" * 60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        test_results = []
        
        # 运行各项测试
        test_results.append(("重复信号检测逻辑", self.test_duplicate_detection_logic()))
        test_results.append(("相似度计算", self.test_similarity_calculation_detailed()))
        test_results.append(("频率控制逻辑", self.test_frequency_control_logic()))
        test_results.append(("时间窗口去重", self.test_deduplication_with_time_window()))
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("📋 测试结果汇总")
        print("=" * 60)
        
        passed_count = 0
        for test_name, passed in test_results:
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"{test_name}: {status}")
            if passed:
                passed_count += 1
        
        total_tests = len(test_results)
        print(f"\n总体结果: {passed_count}/{total_tests} 项测试通过")
        
        if passed_count == total_tests:
            print("🎉 所有测试通过！信号去重系统逻辑正确")
        else:
            print("⚠️ 部分测试失败，需要检查相关功能")
        
        return passed_count == total_tests


def main():
    """主函数"""
    tester = MockSignalDeduplicationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n💡 优化效果总结:")
        print("✅ 择时信号推送频率已优化到5分钟间隔")
        print("✅ 相似信号去重机制工作正常，避免重复推送")
        print("✅ 30分钟时间窗口内的信号会被去重检查")
        print("✅ 每日通知数量限制生效，避免过度推送")
        print("\n🚀 系统已准备就绪，可以有效控制择时信号推送频率！")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
