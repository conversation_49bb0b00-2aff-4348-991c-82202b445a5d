"""单元测试：FactorFilter.evaluate_entry

验证返回字典字段完整性及得分范围。
"""

from datetime import datetime, timedelta

from quant.strategies.factor_filter import FactorFilter, MinuteKline


def test_factor_filter_basic():
    ff = FactorFilter()
    now = datetime.now()
    pending_time = now - timedelta(seconds=100)  # 剩余 500s (<600) 应不推荐

    # 构造20根1m假K线
    klines = [
        MinuteKline(
            timestamp=int((now - timedelta(minutes=i)).timestamp() * 1000),
            open=100 + i * 0.1,
            high=100 + i * 0.15,
            low=100 + i * 0.05,
            close=100 + i * 0.12,
            volume=1000 + i * 10,
        ) for i in range(20)
    ][::-1]  # 时间升序

    indicators = {"rsi": 55}
    result = ff.evaluate_entry(pending_time, now, klines, indicators)

    # 断言字段存在
    for key in ["score", "factors", "remaining_time", "recommend_entry", "reason"]:
        assert key in result

    # 得分应在 -10~60 之间
    assert -10 <= result["score"] <= 60

    # 因 recommend_entry 受 remaining_time 影响，应为 False
    assert result["recommend_entry"] is False 