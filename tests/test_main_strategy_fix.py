#!/usr/bin/env python3
"""
测试主策略修复
验证EventContractMainStrategy是否能正常运行
"""

import asyncio
import sys
import os
from unittest.mock import AsyncMock, MagicMock, patch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_main_strategy import EventContractMainStrategy
from quant.utils import logger


async def test_main_strategy_initialization():
    """测试主策略初始化"""
    logger.info("🧪 测试主策略初始化...")
    
    try:
        # 创建mock配置
        mock_config = {
            'PLATFORMS': {
                'binance': {
                    'access_key': 'test_key',
                    'secret_key': 'test_secret'
                }
            }
        }
        
        # 使用patch来模拟配置加载
        with patch.object(EventContractMainStrategy, '_load_config', return_value=mock_config):
            # 创建策略实例
            strategy = EventContractMainStrategy("test_config.json")
            
            logger.info("✅ 主策略初始化成功")
            
            # 检查关键组件
            assert hasattr(strategy, 'signal_generator'), "缺少signal_generator"
            assert hasattr(strategy, 'factor_filter'), "缺少factor_filter"
            assert hasattr(strategy, 'recommend_engine'), "缺少recommend_engine"
            assert hasattr(strategy, 'dingtalk_notifier'), "缺少dingtalk_notifier"
            assert hasattr(strategy, 'signal_settlement_checker'), "缺少signal_settlement_checker"
            
            logger.info("✅ 所有组件检查通过")
            
            # 检查signal_generator的klines属性
            assert hasattr(strategy.signal_generator, 'klines'), "signal_generator缺少klines属性"
            assert '1m' in strategy.signal_generator.klines, "signal_generator缺少1m klines"
            
            logger.info("✅ signal_generator klines属性检查通过")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 主策略初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_evaluate_pending_signal():
    """测试_evaluate_pending_signal方法"""
    logger.info("🧪 测试_evaluate_pending_signal方法...")
    
    try:
        # 创建mock配置
        mock_config = {
            'PLATFORMS': {
                'binance': {
                    'access_key': 'test_key',
                    'secret_key': 'test_secret'
                }
            }
        }
        
        with patch.object(EventContractMainStrategy, '_load_config', return_value=mock_config):
            strategy = EventContractMainStrategy("test_config.json")
            
            # 模拟pending_signal为None的情况
            strategy.pending_signal = None
            
            # 调用方法应该不会抛出异常
            await strategy._evaluate_pending_signal()
            
            logger.info("✅ _evaluate_pending_signal方法（pending_signal=None）运行成功")
            
            # 模拟有pending_signal但没有1m kline数据的情况
            from quant.strategies.event_contract_signal_generator_simple import SignalResult
            from datetime import datetime, timedelta
            
            strategy.pending_signal = SignalResult(
                has_signal=True,
                direction="UP",
                confidence=75.0
            )
            strategy.pending_created_at = datetime.now() - timedelta(minutes=5)
            
            # 清空1m klines数据
            strategy.signal_generator.klines['1m'].clear()
            
            # 调用方法应该不会抛出异常
            await strategy._evaluate_pending_signal()
            
            logger.info("✅ _evaluate_pending_signal方法（无1m kline数据）运行成功")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ _evaluate_pending_signal方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_main_loop():
    """测试主循环方法"""
    logger.info("🧪 测试主循环方法...")
    
    try:
        # 创建mock配置
        mock_config = {
            'PLATFORMS': {
                'binance': {
                    'access_key': 'test_key',
                    'secret_key': 'test_secret'
                }
            }
        }
        
        with patch.object(EventContractMainStrategy, '_load_config', return_value=mock_config):
            strategy = EventContractMainStrategy("test_config.json")
            
            # 模拟API调用
            with patch.object(strategy.spot_api, 'get_kline', return_value=(None, "mock error")):
                # 模拟所有async方法
                strategy._fetch_latest_data = AsyncMock()
                strategy._check_settlements = AsyncMock()
                strategy._check_signal_settlements = AsyncMock()
                strategy._generate_and_process_signals = AsyncMock()
                strategy._evaluate_pending_signal = AsyncMock()
                strategy._update_daily_stats = AsyncMock()
                strategy._check_risk_limits = AsyncMock()
                strategy._should_send_hourly_report = MagicMock(return_value=False)
                
                # 运行主循环一次
                await strategy._main_loop()
                
                logger.info("✅ 主循环方法运行成功")
                
                return True
                
    except Exception as e:
        logger.error(f"❌ 主循环方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    logger.info("🚀 开始主策略修复测试")
    logger.info("=" * 60)
    
    test_results = []
    
    # 运行测试
    test_results.append(await test_main_strategy_initialization())
    test_results.append(await test_evaluate_pending_signal())
    test_results.append(await test_main_loop())
    
    # 汇总结果
    passed = sum(test_results)
    total = len(test_results)
    
    logger.info(f"\\n🎯 测试结果汇总:")
    logger.info(f"✅ 通过: {passed}/{total}")
    logger.info(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！主策略修复成功")
        return True
    else:
        logger.error("⚠️ 部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        print("\\n🎉 主策略修复测试全部通过！")
    else:
        print("\\n⚠️ 主策略修复测试存在问题，请检查实现")