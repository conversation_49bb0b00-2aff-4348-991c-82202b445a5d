#!/usr/bin/env python3
"""
交易历史管理器简化集成示例
直接创建交易决策，展示完整的交易历史管理流程
"""
import sys
import os
import asyncio
import tempfile
import shutil
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_trade_history_manager import EventContractTradeHistoryManager
from quant.strategies.event_contract_decision_engine import TradingDecision, MarketCondition, RiskLevel
from quant.strategies.event_contract_signal_generator_simple import SignalResult
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier

async def demo_simple_trade_history():
    """简化的交易历史管理器集成示例"""
    print("🎯 交易历史管理器简化集成示例")
    print("=" * 60)
    
    # 创建临时数据库
    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "simple_trade_history.db")
    
    try:
        # 1. 初始化组件
        print("\n1. 初始化系统组件")
        print("-" * 40)
        
        history_manager = EventContractTradeHistoryManager(db_path=db_path)
        dingtalk_notifier = EventContractDingtalkNotifier()
        print("✅ 系统组件初始化完成")
        
        # 2. 创建模拟信号和决策
        print("\n2. 创建模拟交易信号和决策")
        print("-" * 40)
        
        # 模拟信号结果
        signal_result = SignalResult(
            has_signal=True,
            direction="UP",
            confidence=85.0,
            technical_score=88.0,
            user_reminder="强势上涨信号",
            market_status="正常"
        )
        
        # 模拟交易决策
        decision = TradingDecision(
            should_trade=True,
            direction="UP",
            bet_amount=20.0,
            confidence=85.0,
            risk_level=RiskLevel.MEDIUM,
            market_condition=MarketCondition.TRENDING,
            reason="技术指标显示强势上涨趋势，RSI未超买，MACD金叉",
            timestamp=datetime.now(),
            max_loss_allowed=1000.0,
            current_daily_loss=0.0,
            position_size_ratio=0.1,
            market_score=88.0,
            signal_strength=85.0,
            entry_timing="良好"
        )
        
        print(f"✅ 信号: {signal_result.direction}, 信心度: {signal_result.confidence:.1f}%")
        print(f"✅ 决策: {'执行' if decision.should_trade else '跳过'}, 投注: {decision.bet_amount:.2f} USDT")
        
        # 3. 创建交易记录
        print("\n3. 创建交易记录")
        print("-" * 40)
        
        trade_record = history_manager.create_trade_record(signal_result, decision)
        print(f"✅ 交易记录ID: {trade_record.trade_id}")
        
        # 4. 模拟交易执行
        print("\n4. 模拟交易执行")
        print("-" * 40)
        
        order_id = f"ORDER_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        predicted_price = 100.0
        entry_price = 99.98
        
        history_manager.update_execution(
            trade_record.trade_id,
            order_id,
            predicted_price,
            entry_price
        )
        
        print(f"✅ 交易执行: {order_id}")
        print(f"预测价格: {predicted_price:.2f}")
        print(f"入场价格: {entry_price:.2f}")
        
        # 5. 模拟结算过程
        print("\n5. 模拟结算过程")
        print("-" * 40)
        
        # 等待模拟交易时间
        await asyncio.sleep(1)
        
        # 模拟价格上涨（预测正确）
        final_price = 102.5
        result = "win"
        pnl = decision.bet_amount * 0.8  # 80%收益
        
        print(f"最终价格: {final_price:.2f}")
        print(f"结算结果: {result}")
        print(f"盈亏金额: {pnl:+.2f} USDT")
        
        # 6. 更新结算信息
        print("\n6. 更新结算信息")
        print("-" * 40)
        
        history_manager.update_settlement(
            trade_record.trade_id,
            final_price,
            result,
            pnl
        )
        
        print("✅ 交易历史记录已更新")
        
        # 7. 发送通知
        print("\n7. 发送钉钉通知")
        print("-" * 40)
        
        success, error = dingtalk_notifier.send_settlement_notification(
            order_id=order_id,
            result=result,
            pnl=pnl,
            decision=decision
        )
        
        if success:
            print("✅ 结算通知发送成功")
        else:
            print(f"❌ 结算通知发送失败: {error}")
        
        # 8. 创建更多测试交易
        print("\n8. 创建更多测试交易")
        print("-" * 40)
        
        test_cases = [
            {"direction": "DOWN", "final_price": 97.5, "result": "win", "pnl": 16.0},
            {"direction": "UP", "final_price": 98.0, "result": "loss", "pnl": -20.0},
            {"direction": "DOWN", "final_price": 103.0, "result": "loss", "pnl": -20.0},
            {"direction": "UP", "final_price": 100.0, "result": "tie", "pnl": 0.0},
        ]
        
        for i, case in enumerate(test_cases, 2):
            # 创建新的信号和决策
            new_signal = SignalResult(
                has_signal=True,
                direction=case["direction"],
                confidence=75.0 + i * 2,
                technical_score=80.0 + i,
                user_reminder=f"测试信号 {i}",
                market_status="正常"
            )
            
            new_decision = TradingDecision(
                should_trade=True,
                direction=case["direction"],
                bet_amount=20.0,
                confidence=75.0 + i * 2,
                risk_level=RiskLevel.MEDIUM,
                market_condition=MarketCondition.TRENDING,
                reason=f"测试交易 {i}",
                timestamp=datetime.now(),
                max_loss_allowed=1000.0,
                current_daily_loss=0.0,
                position_size_ratio=0.1,
                market_score=80.0 + i,
                signal_strength=75.0 + i * 2,
                entry_timing="良好"
            )
            
            # 创建和结算交易
            new_record = history_manager.create_trade_record(new_signal, new_decision)
            new_order_id = f"ORDER_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i}"
            
            history_manager.update_execution(new_record.trade_id, new_order_id, 100.0, 99.95)
            history_manager.update_settlement(new_record.trade_id, case["final_price"], case["result"], case["pnl"])
            
            print(f"  交易 {i}: {case['direction']} -> {case['result']} ({case['pnl']:+.2f} USDT)")
        
        print("✅ 创建了5条完整的交易记录")
        
        # 9. 生成统计报告
        print("\n9. 生成统计报告")
        print("-" * 40)
        
        metrics = history_manager.calculate_performance_metrics()
        print(f"总交易数: {metrics.total_trades}")
        print(f"胜/负/平: {metrics.wins}/{metrics.losses}/{metrics.ties}")
        print(f"胜率: {metrics.win_rate:.1%}")
        print(f"总盈亏: {metrics.total_pnl:+.2f} USDT")
        print(f"投资回报率: {(metrics.total_pnl/metrics.total_invested)*100:.1f}%")
        print(f"当前连胜/连败: {metrics.current_streak}")
        print(f"最大连胜: {metrics.max_win_streak}")
        print(f"最大连败: {metrics.max_loss_streak}")
        
        # 10. 导出数据
        print("\n10. 导出数据")
        print("-" * 40)
        
        csv_path = history_manager.export_to_csv()
        json_path = history_manager.export_to_json()
        
        print(f"✅ CSV导出: {csv_path}")
        print(f"✅ JSON导出: {json_path}")
        
        # 11. 生成绩效报告
        print("\n11. 生成绩效报告")
        print("-" * 40)
        
        report = history_manager.generate_performance_report(save_to_file=True)
        print("✅ 绩效报告已生成")
        
        # 显示报告摘要
        print("\n📊 绩效报告摘要:")
        report_lines = report.split('\n')
        for line in report_lines[0:15]:  # 显示前15行
            print(line)
        print("...")
        
        # 12. 验证数据完整性
        print("\n12. 验证数据完整性")
        print("-" * 40)
        
        all_records = history_manager.get_trade_records()
        settled_records = history_manager.get_trade_records(status="settled")
        
        print(f"总记录数: {len(all_records)}")
        print(f"已结算记录: {len(settled_records)}")
        
        # 验证每个记录的完整性
        for record in settled_records:
            print(f"  {record.trade_id}: {record.direction} -> {record.result} ({record.pnl:+.2f})")
        
        print("\n" + "=" * 60)
        print("✅ 交易历史管理器集成示例完成！")
        print("\n🎉 功能验证完成:")
        print("  ✅ 交易记录创建和管理")
        print("  ✅ 执行信息更新")
        print("  ✅ 结算信息更新")
        print("  ✅ 统计指标计算")
        print("  ✅ 数据导出功能")
        print("  ✅ 绩效报告生成")
        print("  ✅ 钉钉通知集成")
        print("  ✅ 数据完整性验证")
        
    except Exception as e:
        print(f"❌ 集成示例过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理临时文件: {temp_dir}")
        except:
            pass

if __name__ == "__main__":
    asyncio.run(demo_simple_trade_history()) 