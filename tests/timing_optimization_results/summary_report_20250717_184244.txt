================================================================================
🎯 信号时机优化测试结果摘要
================================================================================

📋 测试配置:
   • 延迟范围: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9] 分钟
   • 结算时间: 15 分钟
   • 胜利阈值: 0.1%
   • 最小信号数: 20

🔍 核心发现:
   🏆 最佳延迟: 4 分钟
      - 胜率: 45.0%
      - 信号数: 469
      - 平均收益: -0.021%

   📉 最差延迟: 0 分钟
      - 胜率: 42.0%
      - 胜率差异: 3.0%

   📊 胜率趋势: 📈 上升趋势
   💡 建议: 建议使用较长延迟时间

📈 详细统计:
--------------------------------------------------------------------------------
延迟     信号数      胜率       胜/负/平        平均PnL      置信度     
--------------------------------------------------------------------------------
0分钟   469      42.0     197/211/61   -0.020     61.7    
1分钟   469      43.1     202/215/52   -0.018     61.7    
2分钟   469      43.9     206/214/49   -0.016     61.7    
3分钟   469      43.5     204/202/63   -0.015     61.7    
4分钟   469      45.0     211/204/54   -0.021     61.7    
5分钟   469      43.7     205/217/47   -0.030     61.7    
6分钟   469      43.3     203/212/54   -0.020     61.7    
7分钟   469      43.7     205/216/48   -0.030     61.7    
8分钟   469      42.4     199/216/54   -0.038     61.7    
9分钟   469      42.6     200/216/53   -0.033     61.7    
--------------------------------------------------------------------------------

💡 关键洞察:
   • 最优时机可提升胜率 3.0%，具有实际意义
   • 信号质量中等（平均置信度 61.7%）
   • 整体策略亏损（平均收益 -0.024%），需要进一步优化

🚀 实施建议:
   1. 将信号延迟时间设置为 4 分钟
   2. 预期胜率提升至 45.0%
   3. 胜率仍低于50%，建议结合其他优化措施
   4. 持续监控实际效果，根据市场变化调整

================================================================================
报告生成时间: 2025-07-17 18:42:44
================================================================================