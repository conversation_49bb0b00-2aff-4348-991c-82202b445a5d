#!/usr/bin/env python3
"""
最终格式验证测试
"""
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, '/Users/<USER>/PycharmProjects/mitchquant1')

def test_message_format():
    """测试消息格式是否正确"""
    
    # 直接测试消息格式函数
    def build_clean_pending_signal_message():
        direction_icon = "🚀"
        signal_id = "signal_1752673911999_9329"
        signal_price = 118353.62
        kline_sequence = 88
        signal_count = 10
        kline_time = "21:51"
        
        message = f"🔔 **潜在信号检测** {direction_icon}\n\n"
        message += f"🆔 信号ID: {signal_id}\n"
        message += f"💰 信号价格: {signal_price:.2f} USDT\n\n"
        message += f"📊 K线序号: 第{kline_sequence}根15分钟K线 ({kline_time})\n"
        message += f"🔢 信号序号: 今日第{signal_count}个信号\n\n"
        message += f"📈 交易方向: UP {direction_icon}\n"
        message += f"🎯 置信度: 75.3%\n"
        message += f"📊 技术分: 82.5分\n"
        message += f"⚠️ 风险等级: MEDIUM\n\n"
        message += f"🌟 市场条件: 强势信号\n"
        message += f"💡 决策原因: 满足交易条件: 信心度75.3%, 风险MEDIUM\n\n"
        message += f"🔍 市场提醒: 【交易信号提醒】📈 检测到信号！建议关注交易机会\n\n"
        message += f"⏰ 信号时间: 2025-07-16 21:51:52\n"
        message += f"📈 今日进度: {kline_sequence}/96 (15分钟K线)\n\n"
        message += f"⏳ 等待入场时机评估... 系统将在接下来的15分钟内寻找最佳入场点\n\n"
        message += f"🏷️ 跟踪提醒: 请记住信号ID [{signal_id}], 用于结算通知对应\n\n"
        message += f"🎉 祝**交易**顺利！{direction_icon}小火箭{direction_icon}起飞！"
        
        return message
    
    def build_clean_recommendation_message():
        direction_emoji = "🚀"
        signal_id = "signal_1752673911999_9329"
        signal_price = 118353.62
        
        message = f"🎯 **事件合约交易推荐** {direction_emoji}\n\n"
        message += f"🆔 信号ID: {signal_id}\n"
        message += f"💰 信号价格: {signal_price:.2f} USDT\n\n"
        message += f"📈 方向: **UP**\n"
        message += f"💵 建议投入: **20 USDT**\n"
        message += f"🎯 置信度: **73.0%**\n"
        message += f"📊 因子得分: **100.2/100**\n"
        message += f"⏰ 剩余时间: 300s\n"
        message += f"🕒 生成时间: 2025-07-16 21:51:52\n\n"
        message += f"💡 该推荐仅供参考，实际操作请自行评估风控。"
        
        return message
    
    print("=== 钉钉消息格式修复验证 ===\n")
    
    # 测试潜在信号消息
    pending_message = build_clean_pending_signal_message()
    print("=== 修复后的潜在信号消息 ===")
    print(pending_message)
    print("\n" + "="*50 + "\n")
    
    # 测试推荐消息
    recommendation_message = build_clean_recommendation_message()
    print("=== 修复后的推荐消息 ===")
    print(recommendation_message)
    print("\n" + "="*50 + "\n")
    
    # 验证格式
    print("=== 格式验证结果 ===")
    
    # 检查是否包含双反斜杠
    has_double_backslash = "\\\\n" in pending_message or "\\\\n" in recommendation_message
    print(f"包含双反斜杠问题: {'❌ 是' if has_double_backslash else '✅ 否'}")
    
    # 检查是否包含过多等号
    has_too_many_equals = "=" * 30 in pending_message or "=" * 30 in recommendation_message
    print(f"包含过多等号: {'❌ 是' if has_too_many_equals else '✅ 否'}")
    
    # 检查换行符格式
    normal_newlines = pending_message.count('\n') + recommendation_message.count('\n')
    print(f"正常换行符数量: ✅ {normal_newlines}")
    
    # 检查关键信息
    key_elements = [
        "signal_1752673911999_9329",
        "118353.62",
        "第88根15分钟K线",
        "今日第10个信号",
        "小火箭"
    ]
    
    missing_elements = []
    for element in key_elements:
        if element not in pending_message:
            missing_elements.append(element)
    
    print(f"关键信息完整性: {'✅ 完整' if not missing_elements else f'❌ 缺少: {missing_elements}'}")
    
    # 检查消息长度
    print(f"潜在信号消息长度: {len(pending_message)} 字符")
    print(f"推荐消息长度: {len(recommendation_message)} 字符")
    
    # 总结
    if not has_double_backslash and not has_too_many_equals and not missing_elements:
        print("\n🎉 格式修复成功！消息格式清晰易读。")
    else:
        print("\n⚠️ 仍有格式问题需要解决。")
    
    return pending_message, recommendation_message

def main():
    """主函数"""
    try:
        pending_msg, recommendation_msg = test_message_format()
        print(f"\n✅ 测试完成！")
        
        # 保存消息样本到文件
        with open('/Users/<USER>/PycharmProjects/mitchquant1/tests/fixed_message_samples.txt', 'w', encoding='utf-8') as f:
            f.write("=== 修复后的潜在信号消息样本 ===\n")
            f.write(pending_msg)
            f.write("\n\n=== 修复后的推荐消息样本 ===\n")
            f.write(recommendation_msg)
        
        print("📝 消息样本已保存到 tests/fixed_message_samples.txt")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()