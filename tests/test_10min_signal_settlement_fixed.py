#!/usr/bin/env python3
"""
测试10分钟信号结算逻辑
验证：
1. 信号生成时自动添加到结算跟踪器
2. 10分钟后自动检查并结算到期信号
"""

import asyncio
import time
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.signal_settlement_checker import SignalSettlementChecker
from quant.utils import logger


class Test10MinSignalSettlement:
    def __init__(self):
        # 使用独立的测试数据库
        self.test_db_path = f"./data/test_10min_settlement_{int(time.time())}.db"
        self.settlement_checker = SignalSettlementChecker(self.test_db_path)
        logger.info("🧪 10分钟信号结算测试初始化完成")

    def test_signal_expiry_time(self):
        """测试信号到期时间设置"""
        logger.info("\\n📋 测试1: 验证信号到期时间为10分钟")
        
        # 创建测试信号
        test_signal = {
            'direction': 'UP',
            'confidence': 75.0,
            'signal_price': 43500.0,
            'signal_strength': 'STRONG',
            'supporting_indicators': ['RSI', 'MACD'],
            'market_conditions': 'BULLISH'
        }
        
        # 记录添加时间
        start_time = datetime.now()
        
        # 添加信号
        signal_id = self.settlement_checker.add_signal_record(test_signal)
        logger.info(f"✅ 添加信号: {signal_id}")
        
        # 查询信号记录
        import sqlite3
        conn = sqlite3.connect(self.settlement_checker.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT signal_id, timestamp, expiry_time 
            FROM signal_records 
            WHERE signal_id = ?
        ''', (signal_id,))
        
        record = cursor.fetchone()
        conn.close()
        
        if record:
            signal_time = datetime.fromisoformat(record[1])
            expiry_time = datetime.fromisoformat(record[2])
            
            # 计算时间差
            time_diff = expiry_time - signal_time
            expected_diff = timedelta(minutes=10)
            
            logger.info(f"📅 信号时间: {signal_time}")
            logger.info(f"⏰ 到期时间: {expiry_time}")
            logger.info(f"🕐 时间差: {time_diff}")
            logger.info(f"✅ 预期时间差: {expected_diff}")
            
            if abs(time_diff - expected_diff).total_seconds() < 60:  # 允许1分钟误差
                logger.info("✅ 测试通过: 信号到期时间正确设置为10分钟")
                return True
            else:
                logger.error(f"❌ 测试失败: 到期时间不正确，预期10分钟，实际{time_diff}")
                return False
        else:
            logger.error("❌ 测试失败: 未找到信号记录")
            return False

    def test_signal_settlement_timing(self):
        """测试信号结算时机"""
        logger.info("\\n📋 测试2: 验证信号结算时机")
        
        # 创建一个即将到期的信号
        signal_data = {
            'direction': 'DOWN',
            'confidence': 80.0,
            'signal_price': 43600.0,
            'signal_strength': 'MEDIUM',
            'supporting_indicators': ['MACD', 'BB'],
            'market_conditions': 'BEARISH'
        }
        
        # 添加信号
        signal_id = self.settlement_checker.add_signal_record(signal_data)
        logger.info(f"✅ 添加测试信号: {signal_id}")
        
        # 手动修改到期时间为1分钟前（模拟已到期）
        import sqlite3
        conn = sqlite3.connect(self.settlement_checker.db_path)
        cursor = conn.cursor()
        
        past_expiry = (datetime.now() - timedelta(minutes=1)).isoformat()
        cursor.execute('''
            UPDATE signal_records 
            SET expiry_time = ? 
            WHERE signal_id = ?
        ''', (past_expiry, signal_id))
        
        conn.commit()
        conn.close()
        
        logger.info("⏰ 已将信号设置为过期状态")
        
        # 测试结算逻辑
        current_price = 43550.0  # 设置当前价格
        settled_signals = self.settlement_checker.check_and_settle_signals(current_price)
        
        if settled_signals:
            settled_signal = settled_signals[0]
            logger.info(f"✅ 信号已结算: {settled_signal['signal_id']}")
            logger.info(f"📊 结算结果: {settled_signal['result']}")
            logger.info(f"💰 价格变化: {settled_signal['price_change']:.2f}%")
            logger.info(f"🎯 结算价格: {settled_signal['settlement_price']}")
            
            # 验证结算结果
            if settled_signal['direction'] == 'DOWN':
                if current_price < settled_signal['signal_price']:
                    expected_result = 'WIN'
                elif current_price > settled_signal['signal_price']:
                    expected_result = 'LOSS'
                else:
                    expected_result = 'TIE'
                
                if settled_signal['result'] == expected_result:
                    logger.info("✅ 测试通过: 结算逻辑正确")
                    return True
                else:
                    logger.error(f"❌ 测试失败: 结算结果不正确，预期{expected_result}，实际{settled_signal['result']}")
                    return False
            else:
                logger.error("❌ 测试失败: 信号方向不匹配")
                return False
        else:
            logger.error("❌ 测试失败: 未找到结算信号")
            return False

    def test_settlement_statistics(self):
        """测试结算统计功能"""
        logger.info("\\n📋 测试3: 验证结算统计功能")
        
        # 获取统计信息
        stats = self.settlement_checker.get_settlement_stats(days=1)
        
        logger.info(f"📈 结算统计:")
        logger.info(f"   总结算数: {stats['total_settled']}")
        logger.info(f"   胜利数: {stats['total_wins']}")
        logger.info(f"   失败数: {stats['total_losses']}")
        logger.info(f"   平局数: {stats['total_ties']}")
        logger.info(f"   整体胜率: {stats['overall_win_rate']:.1f}%")
        logger.info(f"   待结算数: {stats['pending_signals']}")
        
        # 统计功能只要能正常运行就算通过
        logger.info("✅ 测试通过: 统计功能正常")
        return True

    def test_pending_signals_count(self):
        """测试待结算信号数量"""
        logger.info("\\n📋 测试4: 验证待结算信号数量")
        
        # 添加一个新信号（不过期）
        signal_data = {
            'direction': 'UP',
            'confidence': 75.0,
            'signal_price': 43500.0,
            'signal_strength': 'STRONG',
            'supporting_indicators': ['RSI', 'MACD'],
            'market_conditions': 'BULLISH'
        }
        
        signal_id = self.settlement_checker.add_signal_record(signal_data)
        logger.info(f"✅ 添加待结算信号: {signal_id}")
        
        # 检查待结算信号数量
        pending_count = self.settlement_checker.get_pending_signals_count()
        logger.info(f"📊 当前待结算信号数量: {pending_count}")
        
        if pending_count > 0:
            logger.info("✅ 测试通过: 待结算信号计数正常")
            return True
        else:
            logger.error("❌ 测试失败: 待结算信号计数异常")
            return False

    def cleanup(self):
        """清理测试数据"""
        logger.info("\\n🧹 清理测试数据")
        try:
            if os.path.exists(self.test_db_path):
                os.remove(self.test_db_path)
                logger.info("✅ 测试数据库文件已删除")
            else:
                logger.info("ℹ️ 测试数据库文件不存在")
        except Exception as e:
            logger.error(f"❌ 清理测试数据失败: {e}")

    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始10分钟信号结算测试")
        
        test_results = []
        
        # 运行测试
        test_results.append(self.test_signal_expiry_time())
        test_results.append(self.test_signal_settlement_timing())
        test_results.append(self.test_settlement_statistics())
        test_results.append(self.test_pending_signals_count())
        
        # 清理
        self.cleanup()
        
        # 汇总结果
        passed = sum(test_results)
        total = len(test_results)
        
        logger.info(f"\\n🎯 测试结果汇总:")
        logger.info(f"✅ 通过: {passed}/{total}")
        logger.info(f"❌ 失败: {total - passed}/{total}")
        
        if passed == total:
            logger.info("🎉 所有测试通过！10分钟信号结算功能正常")
            return True
        else:
            logger.error("⚠️ 部分测试失败，请检查实现")
            return False


def main():
    """主函数"""
    tester = Test10MinSignalSettlement()
    success = tester.run_all_tests()
    
    if success:
        print("\\n🎉 10分钟信号结算测试全部通过！")
    else:
        print("\\n⚠️ 10分钟信号结算测试存在问题，请检查实现")
    
    return success


if __name__ == "__main__":
    main()