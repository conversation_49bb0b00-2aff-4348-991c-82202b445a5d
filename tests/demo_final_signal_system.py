#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终演示：事件合约信号生成系统

展示完整的信号生成、talib替代方案和系统工作状态
"""

import time
import random
from datetime import datetime
from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple


def demo_complete_signal_system():
    """演示完整的信号生成系统"""
    
    print("🎯 币安事件合约信号生成系统 - 最终演示")
    print("=" * 70)
    
    # 创建信号生成器
    print("📊 正在初始化信号生成器...")
    generator = EventContractSignalGeneratorSimple(
        signal_threshold=75.0,      # 稍微降低阈值以便演示
        min_timeframe_consensus=1,   # 需要至少1个时间周期共识
        confidence_threshold=60.0    # 置信度阈值60%
    )
    
    print(f"✅ 系统初始化完成")
    print(f"   📈 信号阈值: {generator.signal_threshold}%")
    print(f"   🔄 最少共识: {generator.min_timeframe_consensus}个时间周期")
    print(f"   🎯 置信度要求: {generator.confidence_threshold}%")
    print()
    
    # 生成有一定趋势的模拟数据（增加信号生成概率）
    print("📈 生成模拟市场数据...")
    base_price = 95000
    start_time = int(datetime.now().timestamp() * 1000)
    
    # 第一阶段：下跌趋势（前120分钟）
    print("   📉 第一阶段：模拟下跌趋势 (0-120分钟)")
    for i in range(120):
        timestamp = start_time + i * 60 * 1000
        
        # 创建下跌趋势：基础下跌 + 随机波动
        trend_change = -0.002  # 每分钟平均下跌0.2%
        random_change = (random.random() - 0.5) * 0.008  # ±0.4%随机波动
        total_change = trend_change + random_change
        
        new_price = base_price * (1 + total_change)
        
        # 生成OHLC数据
        open_price = base_price
        close_price = new_price
        
        if close_price > open_price:  # 阳线
            high_price = close_price * (1 + random.random() * 0.003)
            low_price = open_price * (1 - random.random() * 0.001)
        else:  # 阴线
            high_price = open_price * (1 + random.random() * 0.001)
            low_price = close_price * (1 - random.random() * 0.003)
        
        volume = 1000 + random.random() * 3000
        
        # 添加K线数据
        generator.add_kline_data(
            timestamp=timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume
        )
        
        base_price = close_price
        
        if (i + 1) % 30 == 0:
            print(f"      📊 {i+1:3d}分钟: ${base_price:,.2f}")
    
    # 第二阶段：反弹趋势（后120分钟）
    print("   📈 第二阶段：模拟反弹趋势 (120-240分钟)")
    for i in range(120, 240):
        timestamp = start_time + i * 60 * 1000
        
        # 创建上涨趋势：基础上涨 + 随机波动
        trend_change = 0.003   # 每分钟平均上涨0.3%
        random_change = (random.random() - 0.5) * 0.01  # ±0.5%随机波动
        total_change = trend_change + random_change
        
        new_price = base_price * (1 + total_change)
        
        # 生成OHLC数据
        open_price = base_price
        close_price = new_price
        
        if close_price > open_price:  # 阳线
            high_price = close_price * (1 + random.random() * 0.004)
            low_price = open_price * (1 - random.random() * 0.001)
        else:  # 阴线
            high_price = open_price * (1 + random.random() * 0.001)
            low_price = close_price * (1 - random.random() * 0.002)
        
        volume = 1200 + random.random() * 4000  # 反弹时成交量增加
        
        # 添加K线数据
        generator.add_kline_data(
            timestamp=timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume
        )
        
        base_price = close_price
        
        if (i + 1) % 30 == 0:
            print(f"      📊 {i+1:3d}分钟: ${base_price:,.2f}")
    
    print(f"\n✅ 数据生成完成: 总计240分钟，价格从$95,000变化到${base_price:,.2f}")
    
    # 检查数据状态
    print("\n📋 数据统计:")
    status = generator.get_status()
    for timeframe in ['1m', '5m', '15m', '30m', '1h']:
        info = status[timeframe]
        count = info['total_klines']
        latest = info['latest_kline']
        if latest and latest['timestamp']:
            kline_type = "🟢阳线" if latest['is_bullish'] else "🔴阴线" if latest['is_bearish'] else "➖十字"
            print(f"   {timeframe:>3}: {count:>2}根K线, 最新: {kline_type} ${latest['close']:>8,.0f}")
    
    # 生成信号
    print(f"\n🎯 生成交易信号...")
    print("=" * 50)
    
    signal_result = generator.generate_signal()
    
    print("=" * 50)
    print("📊 🎉 最终信号结果:")
    
    if signal_result.has_signal:
        direction_emoji = "🚀" if signal_result.direction == "UP" else "📉"
        risk_emoji = "🟢" if signal_result.risk_level == "LOW" else "🟡" if signal_result.risk_level == "MEDIUM" else "🔴"
        
        print(f"""
   ✅ 检测到有效交易信号!
   
   🎯 交易方向: {direction_emoji} {signal_result.direction}
   📊 信号置信度: {signal_result.confidence:.1f}%
   📈 看涨概率: {signal_result.bullish_probability:.1f}%
   📉 看跌概率: {signal_result.bearish_probability:.1f}%
   🔧 技术评分: {signal_result.technical_score:.1f}/100
   {risk_emoji} 风险等级: {signal_result.risk_level}
   🎯 支持周期: {', '.join(signal_result.supporting_timeframes) if signal_result.supporting_timeframes else '无'}
   
   💡 建议: 根据{signal_result.direction}方向，考虑开仓事件合约
        """)
    else:
        print(f"""
   ❌ 当前无有效交易信号
   
   📈 看涨概率: {signal_result.bullish_probability:.1f}%
   📉 看跌概率: {signal_result.bearish_probability:.1f}%
   
   💡 建议: 继续观察市场，等待更明确的信号
        """)
    
    # 显示技术分析详情
    if signal_result.timeframe_analysis:
        print("\n📈 各时间周期技术分析:")
        print("-" * 50)
        for timeframe, analysis in signal_result.timeframe_analysis.items():
            direction = analysis['dominant_direction']
            strength = analysis['signal_strength']
            confidence = analysis['confidence']
            bullish = analysis['bullish_probability']
            bearish = analysis['bearish_probability']
            
            direction_emoji = "🚀" if direction == "UP" else "📉"
            strength_level = "🔥强" if strength > 70 else "💪中" if strength > 50 else "💤弱"
            confidence_level = "🎯高" if confidence > 70 else "📊中" if confidence > 40 else "❓低"
            
            print(f"   {timeframe:>3}: {direction_emoji} {direction} "
                  f"| 强度:{strength_level}({strength:.0f}%) "
                  f"| 置信:{confidence_level}({confidence:.0f}%) "
                  f"| 多:{bullish:.0f}% 空:{bearish:.0f}%")
    
    print("\n" + "=" * 70)
    print("🎉 演示完成! 事件合约信号生成系统运行正常")
    print("💡 说明: 本系统已成功解决talib依赖问题，可以在任何环境下运行")
    print("🔧 特性: 支持多时间周期分析、概率计算、技术指标、风险评估")
    
    return signal_result


if __name__ == "__main__":
    try:
        demo_complete_signal_system()
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()