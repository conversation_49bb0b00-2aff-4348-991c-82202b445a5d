#!/usr/bin/env python3
"""
简化的信号结算系统演示
演示核心功能，避免数据库冲突问题
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from quant.strategies.signal_settlement_checker import SignalSettlementChecker


def simple_demo():
    """简单的信号结算演示"""
    print("🚀 信号结算系统简单演示")
    print("=" * 50)
    
    # 使用独立的数据库文件
    db_path = f"./data/demo_simple_{int(datetime.now().timestamp())}.db"
    checker = SignalSettlementChecker(db_path=db_path)
    
    print("\n📝 步骤1：添加测试信号")
    print("-" * 30)
    
    # 添加一个测试信号
    signal_data = {
        'direction': 'UP',
        'confidence': 75.0,
        'signal_price': 50000.0,
        'signal_strength': 'STRONG',
        'supporting_indicators': ['RSI_15m_超卖', 'MACD_15m_看涨'],
        'market_conditions': 'NORMAL'
    }
    
    signal_id = checker.add_signal_record(signal_data)
    if signal_id:
        print(f"✅ 信号已添加: {signal_id}")
        print(f"   方向: {signal_data['direction']}")
        print(f"   价格: {signal_data['signal_price']:.0f} USDT")
        print(f"   置信度: {signal_data['confidence']:.1f}%")
    else:
        print("❌ 信号添加失败")
        return
    
    print("\n📊 步骤2：查看系统状态")
    print("-" * 30)
    
    pending_count = checker.get_pending_signals_count()
    print(f"⏳ 待结算信号: {pending_count}个")
    
    stats = checker.get_settlement_stats(days=1)
    print(f"📈 今日统计: {stats['total_settled']}个已结算")
    
    print("\n⏰ 步骤3：模拟结算过程")
    print("-" * 30)
    
    # 模拟价格上涨到50300，信号应该WIN
    test_price = 50300.0
    print(f"💰 模拟价格变化到: {test_price:.0f} USDT")
    
    # 手动修改数据库使信号到期
    import sqlite3
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    past_time = (datetime.now() - timedelta(minutes=1)).isoformat()
    cursor.execute(
        "UPDATE signal_records SET expiry_time = ? WHERE signal_id = ?",
        (past_time, signal_id)
    )
    conn.commit()
    conn.close()
    
    # 执行结算
    settled_signals = checker.check_and_settle_signals(test_price)
    
    if settled_signals:
        signal = settled_signals[0]
        result_emoji = "✅" if signal['result'] == 'WIN' else "❌"
        print(f"\n{result_emoji} 结算完成!")
        print(f"   信号ID: {signal['signal_id']}")
        print(f"   方向: {signal['direction']}")
        print(f"   信号价格: {signal['signal_price']:.0f} USDT")
        print(f"   结算价格: {signal['settlement_price']:.0f} USDT")
        print(f"   结果: {signal['result']}")
        print(f"   价格变化: {signal['price_change']:.2f}%")
        print(f"   置信度: {signal['confidence']:.1f}%")
        
        # 验证逻辑
        if signal['direction'] == 'UP' and signal['settlement_price'] > signal['signal_price']:
            print("✅ 结算逻辑正确：价格上涨，UP信号获胜")
        else:
            print("❌ 结算逻辑可能有误")
    else:
        print("❌ 没有找到可结算的信号")
    
    print("\n📊 步骤4：查看最终统计")
    print("-" * 30)
    
    final_stats = checker.get_settlement_stats(days=1)
    print(f"📈 最终统计:")
    print(f"   总结算: {final_stats['total_settled']}个")
    print(f"   胜利: {final_stats['total_wins']}个")
    print(f"   失败: {final_stats['total_losses']}个")
    print(f"   平局: {final_stats['total_ties']}个")
    print(f"   胜率: {final_stats['overall_win_rate']:.1f}%")
    print(f"   累计变化: {final_stats['total_pnl']:.2f}%")
    
    print("\n🎉 演示完成！")
    print("=" * 50)
    
    # 清理测试数据库
    try:
        os.remove(db_path)
        print("🧹 测试数据库已清理")
    except:
        pass
    
    print("\n💡 总结:")
    print("✅ 信号添加功能正常")
    print("✅ 结算逻辑正确")
    print("✅ 统计计算准确")
    print("✅ 胜率跟踪有效")
    print("\n🚀 现在可以集成到主策略中开始实际跟踪！")


if __name__ == "__main__":
    simple_demo()