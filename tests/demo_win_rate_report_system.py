#!/usr/bin/env python3
"""
胜率统计报表系统演示程序
展示如何使用胜率报表系统的各项功能
基于现有架构的集成演示

Author: AI Assistant
Date: 2025-07-17
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.win_rate_report_system import WinRateReportSystem
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier, NotificationConfig


def demo_basic_usage():
    """演示基本使用方法"""
    print("🚀 胜率统计报表系统演示")
    print("=" * 60)
    
    # 1. 初始化报表系统
    print("\n1️⃣ 初始化报表系统")
    report_system = WinRateReportSystem("./data/signal_settlement.db")
    print("   ✅ 报表系统初始化完成")
    
    # 2. 计算不同周期的胜率统计
    print("\n2️⃣ 计算胜率统计")
    
    # 今日统计
    today_stats = report_system.calculate_win_rate_stats(1)
    print(f"   📊 今日统计:")
    print(f"      总信号数: {today_stats.total_signals}")
    print(f"      已结算数: {today_stats.total_settled}")
    print(f"      胜率: {today_stats.win_rate}%")
    print(f"      总盈亏: {today_stats.total_pnl:+.2f}%")
    print(f"      平均置信度: {today_stats.avg_confidence:.1f}%")
    
    # 本周统计
    week_stats = report_system.calculate_win_rate_stats(7)
    print(f"   📈 本周统计:")
    print(f"      总信号数: {week_stats.total_signals}")
    print(f"      胜率: {week_stats.win_rate}%")
    print(f"      看涨胜率: {week_stats.up_win_rate}%")
    print(f"      看跌胜率: {week_stats.down_win_rate}%")
    
    # 本月统计
    month_stats = report_system.calculate_win_rate_stats(30)
    print(f"   📅 本月统计:")
    print(f"      总信号数: {month_stats.total_signals}")
    print(f"      胜率: {month_stats.win_rate}%")
    print(f"      强信号胜率: {month_stats.strong_win_rate}%")
    
    # 3. 趋势分析
    print("\n3️⃣ 趋势分析")
    trend = report_system.analyze_trend(7)
    print(f"   🔍 最近表现: {trend.recent_performance}")
    print(f"   📊 表现趋势: {trend.performance_trend}")
    print(f"   💡 操作建议: {trend.recommendation}")
    print(f"   🔥 最大连胜: {trend.win_streak}次")
    print(f"   ❄️ 最大连败: {trend.loss_streak}次")
    
    # 4. 生成综合报表
    print("\n4️⃣ 生成综合报表")
    report = report_system.generate_comprehensive_report()
    print(f"   📋 报表生成时间: {report['report_time']}")
    print(f"   🎯 一致性得分: {report['performance_metrics']['consistency']:.1f}/100")
    print(f"   ⭐ 信号质量: {report['performance_metrics']['signal_quality']}")
    
    # 5. 导出报表
    print("\n5️⃣ 导出报表")
    export_file = report_system.export_report_to_json(report)
    if export_file:
        print(f"   💾 报表已导出: {export_file}")
    
    # 6. 获取最近信号
    print("\n6️⃣ 最近信号记录")
    recent_signals = report_system.get_recent_signals(5)
    print(f"   📝 最近 {len(recent_signals)} 个信号:")
    for i, signal in enumerate(recent_signals, 1):
        status_emoji = {"WIN": "✅", "LOSS": "❌", "TIE": "⚖️", "PENDING": "⏳"}.get(signal['result'], "❓")
        print(f"      {i}. {signal['signal_id']}: {signal['direction']} {status_emoji} ({signal['confidence']:.1f}%)")


def demo_notification_messages():
    """演示通知消息格式"""
    print("\n" + "=" * 60)
    print("📱 通知消息格式演示")
    print("=" * 60)

    # 初始化通知器（使用现有的钉钉通知器）
    config = NotificationConfig(
        enable_daily_summary=True,
        max_daily_notifications=50
    )
    notifier = EventContractDingtalkNotifier(config)
    
    # 创建示例报表数据
    sample_report = {
        'report_time': datetime.now().isoformat(),
        'summary': {
            'today': {
                'total_signals': 12,
                'settled_signals': 10,
                'win_rate': 70.0,
                'total_pnl': 2.35
            },
            'week': {
                'total_signals': 45,
                'settled_signals': 42,
                'win_rate': 64.3,
                'total_pnl': 8.92
            },
            'month': {
                'total_signals': 180,
                'settled_signals': 175,
                'win_rate': 61.7,
                'total_pnl': 28.45
            }
        },
        'detailed_stats': {
            'today': {
                'up_win_rate': 75.0,
                'down_win_rate': 65.0,
                'strong_win_rate': 85.0
            }
        },
        'trend_analysis': {
            'recent_performance': '最近15笔交易胜率73.3%',
            'performance_trend': '表现优秀',
            'recommendation': '继续保持当前策略',
            'win_streak': 4,
            'loss_streak': 2
        },
        'performance_metrics': {
            'consistency': 82.5,
            'signal_quality': '优秀'
        }
    }
    
    # 演示每小时报表消息
    print("\n1️⃣ 每小时报表消息格式:")
    print("-" * 40)
    hourly_message = notifier._build_win_rate_report_message(sample_report, "hourly")
    print(hourly_message)

    # 演示每日总结消息
    print("\n2️⃣ 每日总结消息格式:")
    print("-" * 40)
    daily_message = notifier._build_win_rate_report_message(sample_report, "daily")
    print(daily_message)

    # 演示每周报表消息
    print("\n3️⃣ 每周报表消息格式:")
    print("-" * 40)
    weekly_message = notifier._build_win_rate_report_message(sample_report, "weekly")
    print(weekly_message)


async def demo_integration_with_main_strategy():
    """演示与主策略的集成"""
    print("\n" + "=" * 60)
    print("🔗 与主策略集成演示")
    print("=" * 60)

    print("\n1️⃣ 集成方式说明:")
    print("   胜率报表系统已集成到 EventContractMainStrategy 中")
    print("   使用现有的 HeartBeat 框架实现定时任务")
    print("   复用现有的钉钉通知器发送报表")

    print("\n2️⃣ 定时任务配置:")
    print("   每日0点: 重新计算当日胜率数据并发送每日总结")
    print("   每小时整点: 发送胜率统计报表")
    print("   最小信号数: 至少1个信号才发送报表")

    print("\n3️⃣ 启动主策略:")
    print("   python3 -m quant.strategies.event_contract_main_strategy")
    print("   系统将自动运行胜率报表功能")

    print("\n4️⃣ 报表内容:")
    print("   - 今日/本周/本月胜率统计")
    print("   - 看涨/看跌信号分别统计")
    print("   - 强/中/弱信号胜率分析")
    print("   - 连胜连败趋势分析")
    print("   - 操作建议和风险提示")

    print("\n5️⃣ 数据存储:")
    print("   - 报表数据自动导出到 ./exports/ 目录")
    print("   - 文件格式: daily_win_rate_report_YYYYMMDD.json")
    print("   - 可用于历史数据分析和回测")

    # 演示报表系统的基本功能
    if os.path.exists("./data/signal_settlement.db"):
        print("\n6️⃣ 实时数据演示:")
        report_system = WinRateReportSystem("./data/signal_settlement.db")

        # 获取今日统计
        today_stats = report_system.calculate_win_rate_stats(1)
        print(f"   今日信号数: {today_stats.total_signals}")
        print(f"   今日胜率: {today_stats.win_rate}%")

        if today_stats.total_signals > 0:
            # 生成报表
            report = report_system.generate_comprehensive_report()
            print(f"   一致性得分: {report['performance_metrics']['consistency']:.1f}/100")
            print(f"   信号质量: {report['performance_metrics']['signal_quality']}")
        else:
            print("   暂无信号数据，请先运行交易系统")
    else:
        print("\n6️⃣ 数据库状态:")
        print("   ⚠️  信号结算数据库不存在")
        print("   请先运行交易系统生成信号数据")


def demo_configuration():
    """演示配置选项"""
    print("\n" + "=" * 60)
    print("⚙️ 配置选项演示")
    print("=" * 60)

    print("\n1️⃣ 钉钉通知器配置:")
    notification_config = NotificationConfig()
    print(f"   启用每日总结: {notification_config.enable_daily_summary}")
    print(f"   每日最大通知数: {notification_config.max_daily_notifications}")
    print(f"   最小信号间隔: {notification_config.min_signal_interval}秒")
    print(f"   启用风险通知: {notification_config.enable_risk_notification}")

    print("\n2️⃣ 胜率报表系统配置:")
    print("   数据库路径: ./data/signal_settlement.db")
    print("   导出目录: ./exports/")
    print("   日志级别: INFO")
    print("   最小信号数要求: 1")

    print("\n3️⃣ 定时任务配置:")
    print("   每日计算时间: 00:00 (凌晨0点)")
    print("   每小时报表: 整点发送")
    print("   时区: Asia/Shanghai")
    print("   容错机制: 10分钟窗口期")

    print("\n4️⃣ 自定义配置方法:")
    print("   1. 修改主策略中的定时检查逻辑")
    print("   2. 调整钉钉通知器的配置参数")
    print("   3. 设置不同的最小信号数要求")
    print("   4. 自定义报表发送频率")

    print("\n5️⃣ 配置文件位置:")
    print("   主策略配置: quant/strategies/event_contract_main_strategy.py")
    print("   钉钉配置: quant/config.py")
    print("   数据库配置: 在主策略初始化时指定")


async def main():
    """主演示函数"""
    print("🎯 胜率统计报表系统完整演示")
    print("=" * 80)
    
    try:
        # 确保数据库存在
        if not os.path.exists("./data/signal_settlement.db"):
            print("⚠️  警告: 信号结算数据库不存在，某些功能可能无法正常演示")
            print("   请先运行交易系统生成一些信号数据，或运行测试程序创建测试数据")
            print()
        
        # 1. 基本使用演示
        demo_basic_usage()
        
        # 2. 通知消息演示
        demo_notification_messages()

        # 3. 与主策略集成演示
        await demo_integration_with_main_strategy()

        # 4. 配置选项演示
        demo_configuration()
        
        print("\n" + "=" * 80)
        print("🎉 演示完成！")
        print("\n💡 使用提示:")
        print("   1. 确保钉钉Token已在 quant/config.py 中配置")
        print("   2. 运行 python3 -m quant.strategies.event_contract_main_strategy 启动主策略")
        print("   3. 系统将自动运行胜率报表功能")
        print("   4. 运行 python3 tests/test_win_rate_report_system.py 进行功能测试")
        print("   5. 查看主策略日志获取胜率报表运行状态")
        print("   6. 导出的报表保存在 exports/ 目录下")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 确保必要的目录存在
    os.makedirs('./logs', exist_ok=True)
    os.makedirs('./exports', exist_ok=True)
    os.makedirs('./config', exist_ok=True)
    
    # 运行演示
    asyncio.run(main())
