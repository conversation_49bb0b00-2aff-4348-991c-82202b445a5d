#!/usr/bin/env python3
"""
交易系统调试脚本
检查并诊断潜在信号通知、择时信号通知和结算时间异常问题
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
import json
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_main_strategy import EventContractMainStrategy
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier
from quant.strategies.signal_settlement_checker import SignalSettlementChecker
from quant.config import config
from quant.utils import logger


class TradingSystemDebugger:
    """交易系统调试器"""
    
    def __init__(self):
        self.strategy = None
        self.dingtalk_notifier = None
        self.signal_checker = None
        
    async def initialize(self):
        """初始化调试器"""
        try:
            # 加载配置
            config.loads("config.json")
            
            # 创建组件
            self.strategy = EventContractMainStrategy()
            self.dingtalk_notifier = EventContractDingtalkNotifier()
            self.signal_checker = SignalSettlementChecker()
            
            print("✅ 调试器初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 调试器初始化失败: {e}")
            return False
    
    def check_dingtalk_notification_status(self):
        """检查钉钉通知状态"""
        print("\n" + "="*60)
        print("🔔 钉钉通知状态检查")
        print("="*60)
        
        try:
            # 检查配置
            dingtalk_config = config.dingtalk
            if dingtalk_config:
                print(f"✅ 钉钉配置存在: {dingtalk_config[:50]}...")
            else:
                print("❌ 钉钉配置缺失")
                return False
            
            # 检查通知器状态
            notifier_config = self.dingtalk_notifier.config
            print(f"📊 通知器配置:")
            print(f"   - 启用信号通知: {notifier_config.enable_signal_notification}")
            print(f"   - 最小间隔: {notifier_config.min_signal_interval}秒")
            print(f"   - 每日最大通知数: {notifier_config.max_daily_notifications}")
            
            # 检查通知统计
            stats = self.dingtalk_notifier.get_notification_stats()
            print(f"📈 通知统计:")
            print(f"   - 今日已发送: {stats['sent_today']}")
            print(f"   - 剩余配额: {stats['remaining_quota']}")
            print(f"   - 最后发送时间: {stats['last_time']}")
            
            # 检查各类型通知统计
            breakdown = stats['detail_breakdown']
            print(f"📋 通知类型统计:")
            print(f"   - 潜在信号: {breakdown['pending_signal']}")
            print(f"   - 择时信号: {breakdown['recommendation']}")
            print(f"   - 交易信号: {breakdown['trading_signal']}")
            print(f"   - 结算通知: {breakdown['settlement']}")
            
            # 检查是否达到限制
            if stats['sent_today'] >= notifier_config.max_daily_notifications:
                print("⚠️ 警告: 已达到每日通知上限!")
                return False
                
            return True
            
        except Exception as e:
            print(f"❌ 检查钉钉通知状态失败: {e}")
            return False
    
    def check_signal_generation_status(self):
        """检查信号生成状态"""
        print("\n" + "="*60)
        print("🎯 信号生成状态检查")
        print("="*60)
        
        try:
            # 检查信号生成器状态
            status = self.strategy.signal_generator.get_status()
            
            for timeframe, data in status.items():
                print(f"📊 {timeframe} K线状态:")
                print(f"   - 总数量: {data['total_klines']}")
                if data['latest_kline']:
                    latest = data['latest_kline']
                    print(f"   - 最新K线: {latest['timestamp']} | 价格: {latest['close']}")
                    print(f"   - 阳线/阴线: {latest['is_bullish']}/{latest['is_bearish']}")
                else:
                    print("   - 暂无K线数据")
            
            # 检查待处理信号
            if hasattr(self.strategy, 'pending_signal') and self.strategy.pending_signal:
                pending = self.strategy.pending_signal
                print(f"⏳ 待处理信号:")
                print(f"   - 方向: {pending.direction}")
                print(f"   - 置信度: {pending.confidence:.1f}%")
                print(f"   - 创建时间: {self.strategy.pending_created_at}")
                
                # 检查是否超时
                if self.strategy.pending_created_at:
                    elapsed = (datetime.now() - self.strategy.pending_created_at).total_seconds()
                    print(f"   - 已等待时间: {elapsed:.0f}秒")
                    if elapsed > 600:
                        print("⚠️ 警告: 待处理信号已超时!")
            else:
                print("📭 当前无待处理信号")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查信号生成状态失败: {e}")
            return False
    
    def check_signal_settlement_status(self):
        """检查信号结算状态"""
        print("\n" + "="*60)
        print("⚖️ 信号结算状态检查")
        print("="*60)
        
        try:
            # 检查待结算信号数量
            pending_count = self.signal_checker.get_pending_signals_count()
            print(f"📊 待结算信号数量: {pending_count}")
            
            # 获取结算统计
            stats = self.signal_checker.get_settlement_stats(days=7)
            print(f"📈 最近7天结算统计:")
            print(f"   - 总结算: {stats['total_settled']}")
            print(f"   - 胜利: {stats['total_wins']}")
            print(f"   - 失败: {stats['total_losses']}")
            print(f"   - 平局: {stats['total_ties']}")
            print(f"   - 胜率: {stats['overall_win_rate']:.2f}%")
            
            # 检查今日结算
            today_stats = stats['today_stats']
            print(f"📅 今日结算统计:")
            print(f"   - 结算: {today_stats['settled']}")
            print(f"   - 胜利: {today_stats['wins']}")
            print(f"   - 失败: {today_stats['losses']}")
            print(f"   - 平局: {today_stats['ties']}")
            print(f"   - 胜率: {today_stats['win_rate']:.2f}%")
            
            # 检查数据库连接
            try:
                conn = sqlite3.connect(self.signal_checker.db_path)
                cursor = conn.cursor()
                
                # 检查最近的信号记录
                cursor.execute('''
                    SELECT signal_id, timestamp, direction, confidence, signal_price, 
                           result, settlement_time
                    FROM signal_records 
                    ORDER BY timestamp DESC 
                    LIMIT 5
                ''')
                
                recent_signals = cursor.fetchall()
                conn.close()
                
                if recent_signals:
                    print(f"📝 最近5个信号记录:")
                    for signal in recent_signals:
                        signal_id, timestamp, direction, confidence, price, result, settlement_time = signal
                        status = result if result != 'PENDING' else '待结算'
                        print(f"   - {signal_id}: {direction} {confidence:.1f}% @{price:.2f} -> {status}")
                        
                        # 检查结算时间异常
                        if settlement_time:
                            signal_time = datetime.fromisoformat(timestamp)
                            settle_time = datetime.fromisoformat(settlement_time)
                            duration = (settle_time - signal_time).total_seconds()
                            
                            if duration < 580:  # 少于9分40秒
                                print(f"     ⚠️ 结算时间异常: 仅{duration:.0f}秒")
                            elif duration > 620:  # 超过10分20秒
                                print(f"     ⚠️ 结算时间延迟: {duration:.0f}秒")
                else:
                    print("📭 暂无信号记录")
                    
            except Exception as e:
                print(f"❌ 检查信号记录失败: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查信号结算状态失败: {e}")
            return False
    
    def check_api_connectivity(self):
        """检查API连接状态"""
        print("\n" + "="*60)
        print("🌐 API连接状态检查")
        print("="*60)
        
        try:
            # 检查现货API
            if hasattr(self.strategy, 'spot_api'):
                print("📊 测试现货API连接...")
                success, error = self.strategy.spot_api.get_kline("BTC/USDT", "1m")
                
                if success:
                    latest_kline = success[-1]
                    price = float(latest_kline[4])
                    print(f"✅ 现货API连接正常, 当前BTC价格: {price:.2f} USDT")
                else:
                    print(f"❌ 现货API连接失败: {error}")
                    return False
            
            # 检查事件合约API
            if hasattr(self.strategy, 'api_client'):
                print("📊 事件合约API连接状态: 需要手动测试")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查API连接失败: {e}")
            return False
    
    async def test_send_notification(self):
        """测试发送通知"""
        print("\n" + "="*60)
        print("🧪 测试发送通知")
        print("="*60)
        
        try:
            test_message = (
                f"🧪 **系统调试测试** 🧪\n\n"
                f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"🔧 这是一条调试测试消息\n\n"
                f"如果您收到此消息，说明钉钉通知功能正常工作。"
            )
            
            success, error = await self.dingtalk_notifier.send_message(test_message)
            
            if success:
                print("✅ 测试通知发送成功")
                return True
            else:
                print(f"❌ 测试通知发送失败: {error}")
                return False
                
        except Exception as e:
            print(f"❌ 测试通知发送异常: {e}")
            return False
    
    def get_debug_summary(self):
        """获取调试摘要"""
        print("\n" + "="*60)
        print("📋 调试摘要")
        print("="*60)
        
        issues = []
        
        # 检查钉钉通知
        if not self.check_dingtalk_notification_status():
            issues.append("钉钉通知配置或限制问题")
        
        # 检查信号生成
        if not self.check_signal_generation_status():
            issues.append("信号生成器状态异常")
        
        # 检查信号结算
        if not self.check_signal_settlement_status():
            issues.append("信号结算检查异常")
        
        # 检查API连接
        if not self.check_api_connectivity():
            issues.append("API连接问题")
        
        if issues:
            print("⚠️ 发现的问题:")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
        else:
            print("✅ 系统状态正常")
        
        return issues
    
    def suggest_fixes(self, issues):
        """建议修复方案"""
        print("\n" + "="*60)
        print("🔧 建议修复方案")
        print("="*60)
        
        if not issues:
            print("✅ 暂无需要修复的问题")
            return
        
        solutions = {
            "钉钉通知配置或限制问题": [
                "1. 检查config.json中的钉钉webhook配置",
                "2. 重启程序以重置每日通知计数器",
                "3. 调整通知频率限制参数"
            ],
            "信号生成器状态异常": [
                "1. 检查网络连接和API访问",
                "2. 重新初始化历史K线数据",
                "3. 检查币安API密钥配置"
            ],
            "信号结算检查异常": [
                "1. 检查signal_settlement.db数据库文件",
                "2. 调整结算时间判断逻辑",
                "3. 重新初始化结算检查器"
            ],
            "API连接问题": [
                "1. 检查网络连接",
                "2. 验证API密钥和权限",
                "3. 检查API调用频率限制"
            ]
        }
        
        for issue in issues:
            if issue in solutions:
                print(f"🔧 {issue}:")
                for solution in solutions[issue]:
                    print(f"   {solution}")
                print()


async def main():
    """主调试函数"""
    print("🚀 开始交易系统调试")
    print("="*60)
    
    debugger = TradingSystemDebugger()
    
    # 初始化调试器
    if not await debugger.initialize():
        print("❌ 调试器初始化失败，退出")
        return
    
    # 执行各项检查
    issues = debugger.get_debug_summary()
    
    # 测试发送通知
    print("\n🧪 测试发送通知功能...")
    await debugger.test_send_notification()
    
    # 提供修复建议
    debugger.suggest_fixes(issues)
    
    print("\n" + "="*60)
    print("🔚 调试完成")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())