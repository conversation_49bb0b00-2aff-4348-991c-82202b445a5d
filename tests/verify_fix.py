#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证修复状态
"""

import sys
import os
import inspect
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_main_strategy import EventContractMainStrategy

def verify_fix():
    """验证修复状态"""
    print("🔍 验证修复状态...")
    
    # 获取_execute_trade方法的源代码
    strategy = EventContractMainStrategy()
    method = getattr(strategy, '_execute_trade')
    
    # 获取方法签名
    sig = inspect.signature(method)
    print(f"📋 方法签名: {sig}")
    
    # 检查参数类型注解
    params = sig.parameters
    for name, param in params.items():
        print(f"   参数 {name}: {param.annotation}")
    
    # 获取方法源代码
    try:
        source = inspect.getsource(method)
        print(f"📄 方法源代码前10行:")
        lines = source.split('\n')[:10]
        for i, line in enumerate(lines, 1):
            print(f"   {i:2d}: {line}")
    except Exception as e:
        print(f"❌ 无法获取源代码: {e}")
    
    print("\n✅ 验证完成")

if __name__ == "__main__":
    verify_fix() 