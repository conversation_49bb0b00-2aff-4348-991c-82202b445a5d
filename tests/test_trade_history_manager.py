#!/usr/bin/env python3
"""
测试交易历史管理器功能
"""
import sys
import os
import tempfile
import shutil
from datetime import datetime, date, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_trade_history_manager import (
    EventContractTradeHistoryManager, TradeRecord, TradingPerformanceMetrics
)
from quant.strategies.event_contract_decision_engine import TradingDecision, MarketCondition, RiskLevel
from quant.strategies.event_contract_signal_generator_simple import SignalResult
from quant.strategies.event_contract_settlement_checker import ContractRecord

def test_trade_history_manager():
    """测试交易历史管理器"""
    print("📊 测试交易历史管理器")
    print("=" * 50)
    
    # 创建临时数据库
    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "test_trade_history.db")
    
    try:
        # 初始化管理器
        manager = EventContractTradeHistoryManager(db_path=db_path)
        print(f"✅ 交易历史管理器已初始化")
        
        # 测试创建交易记录
        print("\n1. 测试创建交易记录")
        print("-" * 30)
        
        # 创建模拟信号和决策
        signal_result = SignalResult(
            has_signal=True,
            direction="UP",
            confidence=75.0,
            technical_score=80.0,
            user_reminder="测试信号",
            market_status="正常"
        )
        
        decision = TradingDecision(
            should_trade=True,
            direction="UP",
            bet_amount=20.0,
            confidence=75.0,
            risk_level=RiskLevel.MEDIUM,
            market_condition=MarketCondition.TRENDING,
            reason="技术指标显示上涨趋势",
            timestamp=datetime.now(),
            max_loss_allowed=1000.0,
            current_daily_loss=0.0,
            position_size_ratio=0.1,
            market_score=75.0,
            signal_strength=75.0,
            entry_timing="良好"
        )
        
        # 创建交易记录
        trade_record = manager.create_trade_record(signal_result, decision)
        print(f"✅ 创建交易记录: {trade_record.trade_id}")
        
        # 测试更新执行信息
        print("\n2. 测试更新执行信息")
        print("-" * 30)
        
        success = manager.update_execution(
            trade_record.trade_id,
            "ORDER_001",
            100.0,  # 预测价格
            99.5    # 入场价格
        )
        print(f"✅ 更新执行信息: {success}")
        
        # 测试更新结算信息
        print("\n3. 测试更新结算信息")
        print("-" * 30)
        
        success = manager.update_settlement(
            trade_record.trade_id,
            105.0,  # 最终价格
            "win",  # 结果
            16.0    # 盈亏
        )
        print(f"✅ 更新结算信息: {success}")
        
        # 创建更多测试数据
        print("\n4. 创建更多测试数据")
        print("-" * 30)
        
        test_cases = [
            {"direction": "DOWN", "final_price": 95.0, "result": "win", "pnl": 16.0},
            {"direction": "UP", "final_price": 95.0, "result": "loss", "pnl": -20.0},
            {"direction": "DOWN", "final_price": 105.0, "result": "loss", "pnl": -20.0},
            {"direction": "UP", "final_price": 100.0, "result": "tie", "pnl": 0.0},
        ]
        
        for i, case in enumerate(test_cases, 2):
            # 创建新的决策
            new_decision = TradingDecision(
                should_trade=True,
                direction=case["direction"],
                bet_amount=20.0,
                confidence=70.0,
                risk_level=RiskLevel.MEDIUM,
                market_condition=MarketCondition.TRENDING,
                reason=f"测试用例 {i}",
                timestamp=datetime.now(),
                max_loss_allowed=1000.0,
                current_daily_loss=0.0,
                position_size_ratio=0.1,
                market_score=70.0,
                signal_strength=70.0,
                entry_timing="良好"
            )
            
            # 创建交易记录
            new_record = manager.create_trade_record(signal_result, new_decision)
            
            # 更新执行和结算
            manager.update_execution(new_record.trade_id, f"ORDER_{i:03d}", 100.0, 99.5)
            manager.update_settlement(new_record.trade_id, case["final_price"], case["result"], case["pnl"])
            
            print(f"  创建测试交易 {i}: {case['direction']} -> {case['result']}")
        
        print(f"✅ 创建了 {len(test_cases) + 1} 条测试交易记录")
        
        # 测试查询功能
        print("\n5. 测试查询功能")
        print("-" * 30)
        
        all_records = manager.get_trade_records()
        print(f"总交易记录数: {len(all_records)}")
        
        settled_records = manager.get_trade_records(status="settled")
        print(f"已结算交易数: {len(settled_records)}")
        
        win_records = manager.get_trade_records(result="win")
        print(f"盈利交易数: {len(win_records)}")
        
        # 测试绩效计算
        print("\n6. 测试绩效计算")
        print("-" * 30)
        
        metrics = manager.calculate_performance_metrics()
        print(f"总交易数: {metrics.total_trades}")
        print(f"胜率: {metrics.win_rate:.2%}")
        print(f"总盈亏: {metrics.total_pnl:+.2f} USDT")
        print(f"平均投注: {metrics.avg_bet_amount:.2f} USDT")
        print(f"当前连胜/连败: {metrics.current_streak}")
        print(f"最大连胜: {metrics.max_win_streak}")
        print(f"最大连败: {metrics.max_loss_streak}")
        print(f"上涨交易胜率: {metrics.up_win_rate:.2%}")
        print(f"下跌交易胜率: {metrics.down_win_rate:.2%}")
        
        # 测试统计功能
        print("\n7. 测试统计功能")
        print("-" * 30)
        
        daily_stats = manager.get_daily_stats()
        print(f"今日交易: {daily_stats.trades}")
        print(f"今日胜率: {daily_stats.win_rate:.2%}")
        print(f"今日盈亏: {daily_stats.pnl:+.2f} USDT")
        print(f"今日投资回报率: {daily_stats.roi:.2%}")
        
        # 测试导出功能
        print("\n8. 测试导出功能")
        print("-" * 30)
        
        # 导出CSV
        csv_path = manager.export_to_csv()
        print(f"✅ CSV导出: {csv_path}")
        
        # 导出JSON
        json_path = manager.export_to_json()
        print(f"✅ JSON导出: {json_path}")
        
        # 测试绩效报告
        print("\n9. 测试绩效报告")
        print("-" * 30)
        
        report = manager.generate_performance_report(save_to_file=False)
        print("绩效报告预览:")
        print(report[:500] + "..." if len(report) > 500 else report)
        
        # 测试状态信息
        print("\n10. 测试状态信息")
        print("-" * 30)
        
        status = manager.get_status()
        print(f"状态信息: {status}")
        
        print("\n" + "=" * 50)
        print("✅ 交易历史管理器测试完成！")
        
        # 显示最终统计
        print("\n📊 最终统计摘要:")
        print(f"  总交易数: {metrics.total_trades}")
        print(f"  胜/负/平: {metrics.wins}/{metrics.losses}/{metrics.ties}")
        print(f"  胜率: {metrics.win_rate:.1%}")
        print(f"  总盈亏: {metrics.total_pnl:+.2f} USDT")
        print(f"  投资回报率: {(metrics.total_pnl/metrics.total_invested)*100:.1f}%" if metrics.total_invested > 0 else "  投资回报率: 0.0%")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
            print(f"🧹 清理临时文件: {temp_dir}")
        except:
            pass

if __name__ == "__main__":
    test_trade_history_manager() 