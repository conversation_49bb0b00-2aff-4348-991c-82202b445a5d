#!/usr/bin/env python3
"""
结算检查器测试
测试自动合约结算和统计功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import time
from datetime import datetime, timedelta
from quant.config import config
from quant.strategies.event_contract_settlement_checker import (
    EventContractSettlementChecker, 
    ContractRecord, 
    TradingStatistics
)
from quant.strategies.event_contract_decision_engine import TradingDecision, RiskLevel, MarketCondition
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier


def test_contract_record():
    """测试合约记录基本功能"""
    print("=" * 50)
    print("测试合约记录基本功能")
    print("=" * 50)
    
    # 创建测试决策
    decision = TradingDecision(
        should_trade=True,
        direction="UP",
        bet_amount=20.0,
        confidence=85.0,
        risk_level=RiskLevel.MEDIUM,
        market_condition=MarketCondition.TRENDING,
        reason="测试决策",
        timestamp=datetime.now(),
        max_loss_allowed=980.0,
        current_daily_loss=20.0,
        position_size_ratio=0.02,
        market_score=85.0,
        signal_strength=85.0,
        entry_timing="良好"
    )
    
    # 创建合约记录
    entry_time = datetime.now()
    expiry_time = entry_time + timedelta(minutes=10)
    
    contract = ContractRecord(
        order_id="TEST_001",
        symbol="BTCUSDT",
        direction="UP",
        bet_amount=20.0,
        predicted_price=67500.0,
        entry_time=entry_time,
        expiry_time=expiry_time,
        decision=decision,
        confidence=85.0,
        market_condition="trending"
    )
    
    print(f"✅ 合约记录创建成功")
    print(f"   订单ID: {contract.order_id}")
    print(f"   方向: {contract.direction}")
    print(f"   投注金额: {contract.bet_amount} USDT")
    print(f"   预测价格: {contract.predicted_price}")
    print(f"   到期时间: {contract.expiry_time}")
    print(f"   距离到期: {contract.time_to_expiry():.0f} 秒")
    print(f"   是否到期: {contract.is_expired()}")
    
    return contract


def test_statistics_calculation():
    """测试统计计算功能"""
    print("\n" + "=" * 50)
    print("测试统计计算功能")
    print("=" * 50)
    
    stats = TradingStatistics()
    
    # 模拟一些交易数据
    trades = [
        ("win", 8.0, 20.0),   # 盈利8 USDT
        ("loss", -20.0, 20.0), # 亏损20 USDT
        ("win", 8.0, 20.0),   # 盈利8 USDT
        ("win", 8.0, 20.0),   # 盈利8 USDT
        ("loss", -20.0, 20.0), # 亏损20 USDT
    ]
    
    for result, pnl, bet_amount in trades:
        stats.total_trades += 1
        stats.total_invested += bet_amount
        stats.total_pnl += pnl
        
        if result == "win":
            stats.wins += 1
            if stats.current_streak >= 0:
                stats.current_streak += 1
            else:
                stats.current_streak = 1
            stats.max_consecutive_wins = max(stats.max_consecutive_wins, stats.current_streak)
        else:
            stats.losses += 1
            if stats.current_streak <= 0:
                stats.current_streak -= 1
            else:
                stats.current_streak = -1
            stats.max_consecutive_losses = max(stats.max_consecutive_losses, abs(stats.current_streak))
        
        stats.win_rate = stats.wins / stats.total_trades if stats.total_trades > 0 else 0.0
        stats.avg_bet_amount = stats.total_invested / stats.total_trades if stats.total_trades > 0 else 0.0
    
    print(f"✅ 统计计算完成")
    print(f"   总交易数: {stats.total_trades}")
    print(f"   胜利次数: {stats.wins}")
    print(f"   失败次数: {stats.losses}")
    print(f"   胜率: {stats.win_rate:.1%}")
    print(f"   总盈亏: {stats.total_pnl:.2f} USDT")
    print(f"   总投入: {stats.total_invested:.2f} USDT")
    print(f"   平均投注: {stats.avg_bet_amount:.2f} USDT")
    print(f"   当前连胜/连败: {stats.current_streak}")
    print(f"   最大连胜: {stats.max_consecutive_wins}")
    print(f"   最大连败: {stats.max_consecutive_losses}")
    
    return stats


def test_settlement_checker_basic():
    """测试结算检查器基本功能"""
    print("\n" + "=" * 50)
    print("测试结算检查器基本功能")
    print("=" * 50)
    
    # 加载配置
    config.loads("config.json")
    
    # 创建钉钉通知器
    dingtalk_notifier = EventContractDingtalkNotifier()
    
    # 创建结算检查器
    checker = EventContractSettlementChecker(
        dingtalk_notifier=dingtalk_notifier,
        check_interval=5  # 5秒检查一次
    )
    
    # 创建测试决策
    decision = TradingDecision(
        should_trade=True,
        direction="UP",
        bet_amount=20.0,
        confidence=85.0,
        risk_level=RiskLevel.MEDIUM,
        market_condition=MarketCondition.TRENDING,
        reason="测试决策：技术指标看涨",
        timestamp=datetime.now(),
        max_loss_allowed=980.0,
        current_daily_loss=20.0,
        position_size_ratio=0.02,
        market_score=85.0,
        signal_strength=85.0,
        entry_timing="良好"
    )
    
    # 添加合约
    checker.add_contract(
        order_id="TEST_001",
        symbol="BTCUSDT",
        direction="UP",
        bet_amount=20.0,
        predicted_price=67500.0,
        decision=decision,
        expiry_minutes=1  # 1分钟到期，用于快速测试
    )
    
    checker.add_contract(
        order_id="TEST_002",
        symbol="BTCUSDT",
        direction="DOWN",
        bet_amount=25.0,
        predicted_price=67800.0,
        decision=decision,
        expiry_minutes=1
    )
    
    print(f"✅ 结算检查器创建成功")
    print(f"   检查间隔: {checker.check_interval} 秒")
    print(f"   待结算合约: {len(checker.pending_contracts)}")
    print(f"   已结算合约: {len(checker.settled_contracts)}")
    
    # 显示状态
    status = checker.get_status()
    print(f"   运行状态: {status}")
    
    return checker


async def test_settlement_process():
    """测试结算流程"""
    print("\n" + "=" * 50)
    print("测试结算流程")
    print("=" * 50)
    
    # 加载配置
    config.loads("config.json")
    
    # 创建钉钉通知器
    dingtalk_notifier = EventContractDingtalkNotifier()
    
    # 创建结算检查器
    checker = EventContractSettlementChecker(
        dingtalk_notifier=dingtalk_notifier,
        check_interval=5  # 5秒检查一次
    )
    
    # 创建测试决策
    decision = TradingDecision(
        should_trade=True,
        direction="UP",
        bet_amount=20.0,
        confidence=85.0,
        risk_level=RiskLevel.MEDIUM,
        market_condition=MarketCondition.TRENDING,
        reason="测试决策：技术指标看涨",
        timestamp=datetime.now(),
        max_loss_allowed=980.0,
        current_daily_loss=20.0,
        position_size_ratio=0.02,
        market_score=85.0,
        signal_strength=85.0,
        entry_timing="良好"
    )
    
    # 添加几个合约进行测试
    contracts = [
        ("TEST_001", "UP", 20.0, 67500.0),
        ("TEST_002", "DOWN", 25.0, 67800.0),
        ("TEST_003", "UP", 30.0, 67600.0),
    ]
    
    for order_id, direction, bet_amount, predicted_price in contracts:
        test_decision = TradingDecision(
            should_trade=True,
            direction=direction,
            bet_amount=bet_amount,
            confidence=85.0,
            risk_level=RiskLevel.MEDIUM,
            market_condition=MarketCondition.TRENDING,
            reason=f"测试决策：{direction}方向",
            timestamp=datetime.now(),
            max_loss_allowed=980.0,
            current_daily_loss=20.0,
            position_size_ratio=0.02,
            market_score=85.0,
            signal_strength=85.0,
            entry_timing="良好"
        )
        
        checker.add_contract(
            order_id=order_id,
            symbol="BTCUSDT",
            direction=direction,
            bet_amount=bet_amount,
            predicted_price=predicted_price,
            decision=test_decision,
            expiry_minutes=0.1  # 6秒到期，快速测试
        )
    
    print(f"✅ 添加了 {len(contracts)} 个测试合约")
    print("📊 开始监控结算...")
    
    # 开始监控
    checker.start_monitoring()
    
    # 等待结算完成
    await asyncio.sleep(15)  # 等待15秒让合约结算
    
    # 停止监控
    checker.stop_monitoring()
    
    # 显示结算结果
    print("\n🎯 结算结果:")
    stats = checker.get_statistics()
    print(f"   总交易数: {stats.total_trades}")
    print(f"   胜利次数: {stats.wins}")
    print(f"   失败次数: {stats.losses}")
    print(f"   胜率: {stats.win_rate:.1%}")
    print(f"   总盈亏: {stats.total_pnl:.2f} USDT")
    print(f"   当前连胜/连败: {stats.current_streak}")
    
    # 显示已结算合约
    settled_contracts = checker.get_settled_contracts()
    print(f"\n📋 已结算合约 ({len(settled_contracts)}):")
    for contract in settled_contracts:
        print(f"   {contract.order_id}: {contract.result} - PnL: {contract.pnl:.2f} USDT")
    
    return checker


def test_callback_system():
    """测试回调系统"""
    print("\n" + "=" * 50)
    print("测试回调系统")
    print("=" * 50)
    
    # 创建结算检查器
    checker = EventContractSettlementChecker(check_interval=5)
    
    # 回调函数
    def settlement_callback(contract):
        print(f"📞 回调触发: {contract.order_id} - {contract.result} - PnL: {contract.pnl:.2f} USDT")
    
    async def async_settlement_callback(contract):
        print(f"📞 异步回调触发: {contract.order_id} - {contract.result}")
        await asyncio.sleep(0.1)  # 模拟异步操作
    
    # 添加回调
    checker.add_settlement_callback(settlement_callback)
    checker.add_settlement_callback(async_settlement_callback)
    
    print(f"✅ 添加了 {len(checker.settlement_callbacks)} 个回调函数")
    
    return checker


def test_data_export():
    """测试数据导出功能"""
    print("\n" + "=" * 50)
    print("测试数据导出功能")
    print("=" * 50)
    
    # 创建测试检查器
    checker = EventContractSettlementChecker()
    
    # 手动添加一些测试数据
    decision = TradingDecision(
        should_trade=True,
        direction="UP",
        bet_amount=20.0,
        confidence=85.0,
        risk_level=RiskLevel.MEDIUM,
        market_condition=MarketCondition.TRENDING,
        reason="测试决策",
        timestamp=datetime.now(),
        max_loss_allowed=980.0,
        current_daily_loss=20.0,
        position_size_ratio=0.02,
        market_score=85.0,
        signal_strength=85.0,
        entry_timing="良好"
    )
    
    # 模拟已结算合约
    for i in range(5):
        contract = ContractRecord(
            order_id=f"TEST_{i+1:03d}",
            symbol="BTCUSDT",
            direction="UP" if i % 2 == 0 else "DOWN",
            bet_amount=20.0,
            predicted_price=67500.0,
            entry_time=datetime.now() - timedelta(minutes=15+i),
            expiry_time=datetime.now() - timedelta(minutes=5+i),
            decision=decision,
            confidence=85.0,
            market_condition="trending"
        )
        
        # 设置结算结果
        contract.is_settled = True
        contract.settlement_time = datetime.now() - timedelta(minutes=i)
        contract.final_price = 67500.0 + (i * 50) * (1 if i % 2 == 0 else -1)
        contract.result = "win" if i % 3 != 0 else "loss"
        contract.pnl = 8.0 if contract.result == "win" else -20.0
        contract.return_rate = contract.pnl / contract.bet_amount
        
        checker.settled_contracts[contract.order_id] = contract
        
        # 更新统计
        stats = checker.statistics
        stats.total_trades += 1
        stats.total_invested += contract.bet_amount
        stats.total_pnl += contract.pnl
        if contract.result == "win":
            stats.wins += 1
        else:
            stats.losses += 1
        stats.win_rate = stats.wins / stats.total_trades
        stats.avg_bet_amount = stats.total_invested / stats.total_trades
    
    # 导出数据
    export_path = checker.export_trading_history("tests/test_trading_history.json")
    print(f"✅ 数据导出成功: {export_path}")
    
    # 验证导出文件
    import json
    with open(export_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"   导出统计: {data['statistics']}")
    print(f"   导出合约数: {len(data['settled_contracts'])}")
    
    return export_path


async def main():
    """主测试函数"""
    print("🚀 开始结算检查器测试...")
    
    try:
        # 测试1：基本功能
        contract = test_contract_record()
        
        # 测试2：统计计算
        stats = test_statistics_calculation()
        
        # 测试3：结算检查器基本功能
        checker = test_settlement_checker_basic()
        
        # 测试4：回调系统
        checker_with_callbacks = test_callback_system()
        
        # 测试5：数据导出
        export_path = test_data_export()
        
        # 测试6：完整结算流程
        await test_settlement_process()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main()) 