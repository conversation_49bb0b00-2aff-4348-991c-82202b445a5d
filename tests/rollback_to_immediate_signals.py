#!/usr/bin/env python3
"""
紧急回滚脚本
如果4分钟延迟优化效果不佳，快速回滚到立即信号生成（第0分钟）
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import shutil

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class SignalTimingRollback:
    """信号时机回滚器"""
    
    def __init__(self):
        """初始化回滚器"""
        self.strategy_file = Path("quant/strategies/event_contract_main_strategy.py")
        self.backup_dir = Path("backups/signal_timing")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        print("🔄 信号时机回滚器初始化完成")
    
    def create_backup(self):
        """创建当前文件备份"""
        if not self.strategy_file.exists():
            print(f"❌ 策略文件不存在: {self.strategy_file}")
            return False
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = self.backup_dir / f"main_strategy_4min_delay_{timestamp}.py"
        
        try:
            shutil.copy2(self.strategy_file, backup_file)
            print(f"✅ 4分钟延迟版本已备份到: {backup_file}")
            return True
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False
    
    def show_current_configuration(self):
        """显示当前配置"""
        print("\n" + "=" * 60)
        print("📋 当前信号时机配置")
        print("=" * 60)
        
        try:
            with open(self.strategy_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找关键配置行
            if "minute_in_15m_cycle != 4" in content:
                print("✅ 当前配置: 4分钟延迟")
                print("   - 信号在每15分钟周期的第4分钟生成")
                print("   - 预期胜率: 45.0%")
                return "4min_delay"
            elif "minute_in_15m_cycle != 0" in content:
                print("✅ 当前配置: 立即信号（第0分钟）")
                print("   - 信号在每15分钟周期开始时立即生成")
                print("   - 基线胜率: 42.0%")
                return "immediate"
            else:
                print("⚠️ 无法识别当前配置")
                return "unknown"
                
        except Exception as e:
            print(f"❌ 读取配置失败: {e}")
            return "error"
    
    def rollback_to_immediate(self):
        """回滚到立即信号生成"""
        print("\n" + "=" * 60)
        print("🔄 执行回滚到立即信号生成")
        print("=" * 60)
        
        try:
            with open(self.strategy_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换关键配置
            if "minute_in_15m_cycle != 4" in content:
                # 将4分钟延迟改回0分钟（立即）
                new_content = content.replace(
                    "minute_in_15m_cycle != 4",
                    "minute_in_15m_cycle != 0"
                )
                
                # 更新日志信息
                new_content = new_content.replace(
                    "跳过信号生成，当前为第{minute_in_15m_cycle}分钟，等待第4分钟",
                    "跳过信号生成，当前为第{minute_in_15m_cycle}分钟，等待第0分钟"
                )
                
                new_content = new_content.replace(
                    "🎯 4分钟延迟信号生成触发",
                    "🎯 立即信号生成触发"
                )
                
                new_content = new_content.replace(
                    "优化版信号生成 - 添加4分钟延迟以提升胜率",
                    "标准信号生成 - 立即生成信号（已回滚）"
                )
                
                # 写入修改后的内容
                with open(self.strategy_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("✅ 回滚成功！")
                print("📊 配置变更:")
                print("   - 信号延迟: 4分钟 → 0分钟（立即）")
                print("   - 预期胜率: 45.0% → 42.0%")
                print("   - 信号时机: 第4分钟 → 第0分钟")
                
                return True
            else:
                print("⚠️ 当前不是4分钟延迟配置，无需回滚")
                return False
                
        except Exception as e:
            print(f"❌ 回滚失败: {e}")
            return False
    
    def verify_rollback(self):
        """验证回滚结果"""
        print("\n" + "=" * 60)
        print("🔍 验证回滚结果")
        print("=" * 60)
        
        current_config = self.show_current_configuration()
        
        if current_config == "immediate":
            print("\n✅ 回滚验证通过")
            print("📊 当前状态:")
            print("   - 信号将在每15分钟周期的第0分钟生成")
            print("   - 即：00:00, 00:15, 00:30, 00:45, 01:00...")
            print("   - 预期胜率回到基线42.0%")
            
            # 显示接下来的信号时间
            from datetime import datetime, timedelta
            current_time = datetime.now()
            next_signals = []
            
            # 找到下一个第0分钟时间点
            current_minute = current_time.minute
            minute_in_cycle = current_minute % 15
            
            if minute_in_cycle == 0:
                next_signal_minute = current_minute
            else:
                next_signal_minute = current_minute + (15 - minute_in_cycle)
            
            start_time = current_time.replace(minute=next_signal_minute % 60, second=0, microsecond=0)
            if next_signal_minute >= 60:
                start_time += timedelta(hours=1)
            
            # 生成接下来的5个信号时间
            for i in range(5):
                signal_time = start_time + timedelta(minutes=i * 15)
                next_signals.append(signal_time)
            
            print("\n🕐 接下来的信号时间:")
            for i, signal_time in enumerate(next_signals):
                minute_in_cycle = signal_time.minute % 15
                print(f"   {i+1}. {signal_time.strftime('%H:%M:%S')} (第{minute_in_cycle}分钟)")
            
            return True
        else:
            print("\n❌ 回滚验证失败")
            print("⚠️ 配置可能未正确回滚，请手动检查")
            return False
    
    def show_rollback_impact(self):
        """显示回滚影响"""
        print("\n" + "=" * 60)
        print("📊 回滚影响分析")
        print("=" * 60)
        
        print("📉 性能变化:")
        print("   - 胜率: 45.0% → 42.0% (-3.0%)")
        print("   - 月度胜利数: 1296 → 1210 (-86次)")
        print("   - 相对性能: -7.1%")
        
        print("\n⚠️ 预期问题:")
        print("   - RSI超买信号可能过早触发")
        print("   - 布林带突破信号可能误判趋势")
        print("   - 技术指标滞后性导致假信号增加")
        
        print("\n💡 后续建议:")
        print("   - 分析4分钟延迟失败的原因")
        print("   - 考虑测试其他延迟时间（2-3分钟）")
        print("   - 优化信号生成算法本身")
        print("   - 结合其他技术指标提升准确性")
    
    def run_rollback_process(self):
        """运行完整回滚流程"""
        print("🚨 启动信号时机紧急回滚流程")
        print("=" * 80)
        
        # 1. 显示当前配置
        current_config = self.show_current_configuration()
        
        if current_config != "4min_delay":
            print("\n⚠️ 当前不是4分钟延迟配置，无需回滚")
            return False
        
        # 2. 确认回滚
        print(f"\n❓ 确认要回滚到立即信号生成吗？")
        print(f"   这将使胜率从45.0%降回42.0%")
        
        # 在实际使用中，这里应该有用户确认
        # confirm = input("输入 'YES' 确认回滚: ")
        # if confirm != 'YES':
        #     print("❌ 回滚已取消")
        #     return False
        
        print("✅ 自动确认回滚（演示模式）")
        
        # 3. 创建备份
        if not self.create_backup():
            print("❌ 备份失败，回滚中止")
            return False
        
        # 4. 执行回滚
        if not self.rollback_to_immediate():
            print("❌ 回滚执行失败")
            return False
        
        # 5. 验证回滚
        if not self.verify_rollback():
            print("❌ 回滚验证失败")
            return False
        
        # 6. 显示影响
        self.show_rollback_impact()
        
        print("\n" + "=" * 80)
        print("✅ 回滚流程完成")
        print("=" * 80)
        print("🔄 下一步操作:")
        print("1. 重启交易系统")
        print("2. 观察日志确认信号在第0分钟生成")
        print("3. 监控胜率变化")
        print("4. 分析4分钟延迟失败原因")
        
        return True


def main():
    """主函数"""
    rollback = SignalTimingRollback()
    success = rollback.run_rollback_process()
    
    return 0 if success else 1


if __name__ == "__main__":
    print("⚠️ 这是紧急回滚脚本")
    print("只有在4分钟延迟优化效果不佳时才使用")
    print("=" * 50)
    
    exit_code = main()
    sys.exit(exit_code)
