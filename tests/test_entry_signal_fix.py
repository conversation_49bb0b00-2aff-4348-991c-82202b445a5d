#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的入场信号检测问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from typing import Dict, List
from dataclasses import dataclass
from quant.strategies.factor_filter import FactorFilter
from quant.strategies.recommendation_engine import RecommendationEngine


@dataclass
class MinuteKline:
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float


class SignalResult:
    def __init__(self, direction: str, confidence: float, has_signal: bool = True):
        self.direction = direction
        self.confidence = confidence
        self.has_signal = has_signal


def test_fixed_entry_signal():
    """测试修复后的入场信号检测"""
    
    print("🔧 测试修复后的入场信号检测")
    print("=" * 60)
    
    # 1. 使用修复后的FactorFilter
    factor_filter = FactorFilter()
    recommend_engine = RecommendationEngine()
    
    print(f"📊 当前阈值: {factor_filter.threshold}")
    print(f"📊 权重设置: {factor_filter.weights}")
    
    # 2. 创建测试数据
    base_time = datetime.now()
    base_price = 67000.0
    
    minute_klines = []
    for i in range(20):
        # 模拟更明显的价格波动
        price_change = (i % 4 - 2) * 0.4  # -0.8%, -0.4%, 0%, +0.4%
        open_price = base_price * (1 + price_change/100)
        close_price = open_price * (1 + (i % 6 - 3) * 0.3/100)  # 更大的波动
        high_price = max(open_price, close_price) * 1.002
        low_price = min(open_price, close_price) * 0.998
        volume = 1000 + i * 80 + (i % 3) * 200  # 更大的成交量波动
        
        kline = MinuteKline(
            timestamp=base_time - timedelta(minutes=20-i),
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=volume
        )
        minute_klines.append(kline)
    
    print(f"📈 生成了{len(minute_klines)}根模拟K线数据")
    
    # 3. 创建pending_signal
    pending_signal = SignalResult(
        direction="UP",
        confidence=0.75,
        has_signal=True
    )
    
    # 4. 测试不同时间场景
    scenarios = [
        ("刚生成pending信号", 0, {'rsi': 35.0}),
        ("2分钟后", 120, {'rsi': 30.0}),
        ("5分钟后", 300, {'rsi': 40.0}),
        ("8分钟后", 480, {'rsi': 45.0}),
        ("10分钟后", 600, {'rsi': 50.0}),
    ]
    
    print("\n📊 不同时间场景的入场信号检测结果:")
    print("-" * 80)
    
    for scenario_name, elapsed_seconds, indicators in scenarios:
        pending_created_at = datetime.now() - timedelta(seconds=elapsed_seconds)
        now = datetime.now()
        
        # 因子评估
        factor_eval = factor_filter.evaluate_entry(
            pending_created_at=pending_created_at,
            now=now,
            minute_klines=minute_klines,
            indicators=indicators
        )
        
        # 推荐生成
        recommendation = recommend_engine.make_recommendation(pending_signal, factor_eval)
        
        print(f"\n🕐 {scenario_name} (已过去{elapsed_seconds}秒):")
        print(f"   RSI: {indicators['rsi']}")
        print(f"   总得分: {factor_eval['score']:.1f}/{factor_filter.threshold}")
        print(f"   剩余时间: {factor_eval['remaining_time']}秒")
        print(f"   是否推荐入场: {'✅' if factor_eval['recommend_entry'] else '❌'}")
        print(f"   原因: {factor_eval['reason']}")
        
        if recommendation['has_recommendation']:
            print(f"   🚀 生成推荐: 方向={recommendation['direction']}, "
                  f"投注={recommendation['stake']}USDT, "
                  f"置信度={recommendation['confidence']:.1%}")
        else:
            print(f"   ⏳ 未生成推荐: {recommendation['reason']}")
        
        # 详细因子分析
        print("   📊 详细因子得分:")
        for factor_name, score in factor_eval['factors'].items():
            weight = factor_filter.weights.get(factor_name, 0)
            if factor_name == 'time_decay':
                print(f"     {factor_name}: {score} (时间衰减)")
            else:
                percentage = (score / weight * 100) if weight > 0 else 0
                print(f"     {factor_name}: {score:.1f}/{weight} ({percentage:.1f}%)")
    
    # 5. 总结改进效果
    print("\n🎯 改进效果总结:")
    print("=" * 60)
    
    print("✅ 阈值优化: 从40.0降低到25.0")
    print("✅ 权重调整: 增加volume和momentum权重")
    print("✅ 时间限制: 从600秒降低到300秒")
    print("✅ 因子计算: 降低各因子的触发门槛")
    
    print("\n📈 预期效果:")
    print("- 更容易触发入场信号")
    print("- 保持合理的风险控制")
    print("- 在5-8分钟窗口内有更多入场机会")
    
    # 6. 验证修复结果
    print("\n🔍 验证修复结果:")
    print("-" * 60)
    
    success_count = 0
    total_tests = len(scenarios)
    
    for scenario_name, elapsed_seconds, indicators in scenarios:
        pending_created_at = datetime.now() - timedelta(seconds=elapsed_seconds)
        now = datetime.now()
        
        factor_eval = factor_filter.evaluate_entry(
            pending_created_at=pending_created_at,
            now=now,
            minute_klines=minute_klines,
            indicators=indicators
        )
        
        if factor_eval['recommend_entry']:
            success_count += 1
    
    success_rate = success_count / total_tests * 100
    print(f"入场信号成功率: {success_count}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 40:  # 至少40%的场景能够入场
        print("✅ 修复成功！入场信号检测已恢复正常")
    else:
        print("❌ 仍需进一步优化参数")


if __name__ == "__main__":
    test_fixed_entry_signal()