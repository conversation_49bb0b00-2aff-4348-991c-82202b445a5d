#!/usr/bin/env python3
"""
事件合约决策引擎演示
展示如何使用决策引擎进行自动化交易决策
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
from quant.strategies.event_contract_decision_engine import (
    EventContractDecisionEngine,
    RiskManagementConfig,
    RiskLevel,
    MarketCondition
)
from quant.strategies.event_contract_signal_generator_simple import (
    SignalResult,
    EventContractSignalGeneratorSimple
)

def demo_complete_workflow():
    """演示完整的交易决策流程"""
    print("=" * 60)
    print("🚀 事件合约自动化决策引擎演示")
    print("=" * 60)
    
    # 1. 创建决策引擎配置
    config = RiskManagementConfig(
        base_bet_amount=20.0,
        daily_soft_limit=1000.0,
        daily_hard_limit=10000.0,
        max_bet_amount=200.0,
        min_bet_amount=10.0,
        win_rate_threshold=0.65,
        bet_increase_factor=1.3,
        bet_decrease_factor=0.7
    )
    
    print(f"💼 风险管理配置:")
    print(f"   - 基础投注: {config.base_bet_amount} USDT")
    print(f"   - 软限制: {config.daily_soft_limit} USDT")
    print(f"   - 硬限制: {config.daily_hard_limit} USDT")
    print(f"   - 胜率阈值: {config.win_rate_threshold:.1%}")
    print()
    
    # 2. 创建决策引擎
    engine = EventContractDecisionEngine(config)
    
    # 3. 创建信号生成器
    signal_generator = EventContractSignalGeneratorSimple()
    
    # 4. 模拟多个交易场景
    scenarios = [
        {
            'name': '📈 强势上涨信号',
            'market_data': {
                'price': 95000.0,
                'volume': 1200000,
                'volatility': 0.03,
                'trend_strength': 0.85,
                'price_change_24h': 0.08
            },
            'signal': SignalResult(
                has_signal=True,
                direction="UP",
                confidence=88.0,
                technical_score=82.0,
                market_status="正常",
                user_reminder="强势上涨信号"
            )
        },
        {
            'name': '📉 弱势下跌信号',
            'market_data': {
                'price': 92000.0,
                'volume': 800000,
                'volatility': 0.02,
                'trend_strength': 0.45,
                'price_change_24h': -0.03
            },
            'signal': SignalResult(
                has_signal=True,
                direction="DOWN",
                confidence=68.0,
                technical_score=58.0,
                market_status="谨慎",
                user_reminder="弱势下跌信号"
            )
        },
        {
            'name': '⚡ 高波动信号',
            'market_data': {
                'price': 94000.0,
                'volume': 1500000,
                'volatility': 0.12,
                'trend_strength': 0.65,
                'price_change_24h': 0.05
            },
            'signal': SignalResult(
                has_signal=True,
                direction="UP",
                confidence=92.0,
                technical_score=85.0,
                market_status="高波动",
                user_reminder="高波动突破信号"
            )
        }
    ]
    
    # 5. 处理每个场景
    for i, scenario in enumerate(scenarios, 1):
        print(f"🎯 场景 {i}: {scenario['name']}")
        print(f"   市场数据: 价格={scenario['market_data']['price']:.0f}, "
              f"波动率={scenario['market_data']['volatility']:.1%}, "
              f"趋势强度={scenario['market_data']['trend_strength']:.1%}")
        
        # 做出决策
        decision = engine.make_trading_decision(
            signal_result=scenario['signal'],
            current_balance=5000.0,
            market_data=scenario['market_data']
        )
        
        print(f"   🎲 决策结果:")
        print(f"     - 执行交易: {'✅ 是' if decision.should_trade else '❌ 否'}")
        print(f"     - 投注金额: {decision.bet_amount:.2f} USDT")
        print(f"     - 交易方向: {decision.direction}")
        print(f"     - 信心度: {decision.confidence:.1f}%")
        print(f"     - 风险等级: {decision.risk_level.value}")
        print(f"     - 市场条件: {decision.market_condition.value}")
        print(f"     - 仓位比例: {decision.position_size_ratio:.2%}")
        print(f"     - 决策原因: {decision.reason}")
        
        # 如果决策是交易，模拟执行
        if decision.should_trade:
            order_id = f"order_{i}_{datetime.now().strftime('%H%M%S')}"
            engine.record_trade(
                decision=decision,
                order_id=order_id,
                executed_price=scenario['market_data']['price'],
                executed_amount=decision.bet_amount
            )
            
            # 模拟交易结果
            import random
            win_probability = decision.confidence / 100.0
            is_win = random.random() < win_probability
            
            pnl = decision.bet_amount * 0.9 if is_win else -decision.bet_amount
            result = "win" if is_win else "loss"
            
            engine.update_trade_result(order_id, result, pnl)
            print(f"     - 交易结果: {'🎉 盈利' if is_win else '😔 亏损'} {pnl:.2f} USDT")
        
        print()
    
    # 6. 显示总结统计
    print("📊 交易统计总结:")
    print("=" * 40)
    
    daily_stats = engine.get_daily_stats()
    print(f"📈 交易绩效:")
    print(f"   - 总交易数: {daily_stats['total_trades']}")
    print(f"   - 胜率: {daily_stats['win_rate']:.1%}")
    print(f"   - 总盈亏: {daily_stats['total_pnl']:.2f} USDT")
    print(f"   - 平均投注: {daily_stats['avg_bet']:.2f} USDT")
    print(f"   - 连胜/连败: {daily_stats['current_streak']}")
    print(f"   - 最大亏损: {daily_stats['max_loss']:.2f} USDT")
    print()
    
    risk_summary = engine.get_risk_summary()
    print(f"⚠️ 风险控制:")
    print(f"   - 当日损失: {risk_summary['daily_loss']:.2f} USDT")
    print(f"   - 损失比例: {risk_summary['daily_loss_ratio']:.1%}")
    print(f"   - 剩余软限制: {risk_summary['remaining_soft_limit']:.2f} USDT")
    print(f"   - 剩余硬限制: {risk_summary['remaining_hard_limit']:.2f} USDT")
    print(f"   - 当前胜率: {risk_summary['current_win_rate']:.1%}")
    print(f"   - 今日胜率: {risk_summary['today_win_rate']:.1%}")
    
    should_stop, stop_reason = engine.should_stop_trading()
    print(f"   - 停止交易: {'是' if should_stop else '否'}")
    if should_stop:
        print(f"   - 停止原因: {stop_reason}")
    
    print()
    print("🎯 演示完成！")
    
    return engine

def demo_risk_scenarios():
    """演示风险管理场景"""
    print("\n" + "=" * 60)
    print("⚠️ 风险管理场景演示")
    print("=" * 60)
    
    # 创建严格的风险配置
    strict_config = RiskManagementConfig(
        base_bet_amount=20.0,
        daily_soft_limit=100.0,  # 很低的限制用于演示
        daily_hard_limit=200.0,
        max_bet_amount=50.0,
        min_bet_amount=5.0
    )
    
    engine = EventContractDecisionEngine(strict_config)
    
    # 模拟一些亏损
    print("1️⃣ 模拟连续亏损场景")
    losses = [25.0, 30.0, 20.0, 15.0]  # 总计90 USDT损失
    for i, loss in enumerate(losses):
        engine.daily_trades.append({
            'date': datetime.now().date(),
            'pnl': -loss,
            'bet_amount': 20.0,
            'order_id': f'loss_{i}'
        })
    
    print(f"   累计损失: {engine._calculate_daily_loss():.2f} USDT")
    
    # 测试在高损失情况下的决策
    high_confidence_signal = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=95.0,
        technical_score=90.0,
        market_status="强势",
        user_reminder="高置信度信号"
    )
    
    decision = engine.make_trading_decision(
        signal_result=high_confidence_signal,
        current_balance=1000.0
    )
    
    print(f"   高置信度信号决策:")
    print(f"     - 执行交易: {'是' if decision.should_trade else '否'}")
    print(f"     - 投注金额: {decision.bet_amount:.2f} USDT")
    print(f"     - 风险等级: {decision.risk_level.value}")
    print(f"     - 决策原因: {decision.reason}")
    
    should_stop, reason = engine.should_stop_trading()
    print(f"   停止交易建议: {'是' if should_stop else '否'}")
    if should_stop:
        print(f"   停止原因: {reason}")
    
    print()
    print("2️⃣ 模拟达到硬限制场景")
    
    # 添加更多损失达到硬限制
    engine.daily_trades.append({
        'date': datetime.now().date(),
        'pnl': -120.0,  # 总损失达到210 USDT，超过硬限制
        'bet_amount': 50.0,
        'order_id': 'big_loss'
    })
    
    print(f"   总损失: {engine._calculate_daily_loss():.2f} USDT")
    
    decision = engine.make_trading_decision(
        signal_result=high_confidence_signal,
        current_balance=1000.0
    )
    
    print(f"   超限制后决策:")
    print(f"     - 执行交易: {'是' if decision.should_trade else '否'}")
    print(f"     - 决策原因: {decision.reason}")
    
    should_stop, reason = engine.should_stop_trading()
    print(f"   停止交易建议: {'是' if should_stop else '否'}")
    if should_stop:
        print(f"   停止原因: {reason}")

def main():
    """主函数"""
    try:
        # 演示完整工作流程
        engine = demo_complete_workflow()
        
        # 演示风险管理场景
        demo_risk_scenarios()
        
        print("\n🎉 所有演示完成！")
        print("决策引擎已经准备好用于实际交易。")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()