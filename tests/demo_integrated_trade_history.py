#!/usr/bin/env python3
"""
交易历史管理器完整集成示例
展示从信号生成到结算跟踪的完整流程
"""
import sys
import os
import asyncio
import tempfile
import shutil
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_trade_history_manager import EventContractTradeHistoryManager
from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple
from quant.strategies.event_contract_decision_engine import EventContractDecisionEngine
from quant.strategies.event_contract_settlement_checker import EventContractSettlementChecker
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier

async def demo_integrated_trade_history():
    """完整的交易历史管理器集成示例"""
    print("🎯 交易历史管理器完整集成示例")
    print("=" * 60)
    
    # 创建临时数据库
    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "integrated_trade_history.db")
    
    try:
        # 1. 初始化所有组件
        print("\n1. 初始化系统组件")
        print("-" * 40)
        
        # 交易历史管理器
        history_manager = EventContractTradeHistoryManager(db_path=db_path)
        print("✅ 交易历史管理器")
        
        # 信号生成器
        signal_generator = EventContractSignalGeneratorSimple()
        print("✅ 信号生成器")
        
        # 决策引擎
        decision_engine = EventContractDecisionEngine()
        print("✅ 决策引擎")
        
        # 结算检查器
        settlement_checker = EventContractSettlementChecker()
        print("✅ 结算检查器")
        
        # 钉钉通知器
        dingtalk_notifier = EventContractDingtalkNotifier()
        print("✅ 钉钉通知器")
        
        # 2. 模拟完整的交易流程
        print("\n2. 模拟完整交易流程")
        print("-" * 40)
        
        # 添加模拟K线数据
        from quant.strategies.event_contract_signal_generator_simple import KlineData
        
        # 创建一系列模拟K线数据（带明显趋势）
        base_price = 100.0
        current_time = int(datetime.now().timestamp() * 1000)  # 转换为毫秒
        
        # 添加足够的K线数据（300根1分钟K线，可以聚合成20根15分钟K线）
        for i in range(300):
            # 生成模拟K线数据（创建明显的上涨趋势）
            trend_factor = i * 0.01  # 逐渐上涨的趋势
            price_noise = (i % 5 - 2) * 0.1  # 价格噪音
            open_price = base_price + trend_factor + price_noise
            
            # 创建明显的趋势K线
            if i % 3 == 0:  # 强势上涨K线
                close_price = open_price + 0.2
            elif i % 3 == 1:  # 小幅回调K线
                close_price = open_price - 0.05
            else:  # 震荡K线
                close_price = open_price + 0.1
            
            high_price = max(open_price, close_price) + 0.05
            low_price = min(open_price, close_price) - 0.03
            
            # 1分钟间隔，毫秒
            timestamp = current_time - (300 - i) * 60000  
            volume = 1000 + i * 20  # 逐渐增加的成交量
            
            signal_generator.add_kline_data(
                timestamp=timestamp,
                open_price=open_price,
                high_price=high_price,
                low_price=low_price,
                close_price=close_price,
                volume=volume
            )
        
        print("✅ 添加了300根1分钟K线数据（带明显上涨趋势）")
        
        # 3. 生成交易信号
        print("\n3. 生成交易信号")
        print("-" * 40)
        
        signal_result = signal_generator.generate_signal()
        print(f"信号结果: {signal_result.direction if signal_result.has_signal else 'NO_SIGNAL'}")
        print(f"信号信心度: {signal_result.confidence:.1f}%")
        print(f"技术得分: {signal_result.technical_score:.1f}")
        
        if not signal_result.has_signal:
            print("❌ 没有生成有效信号，跳过交易流程")
            return
        
        # 4. 做出交易决策
        print("\n4. 做出交易决策")
        print("-" * 40)
        
        current_balance = 10000.0  # 模拟账户余额
        market_data = {
            'current_price': 100.0,
            'volume': 50000,
            'volatility': 0.02
        }
        
        decision = decision_engine.make_trading_decision(
            signal_result=signal_result,
            current_balance=current_balance,
            market_data=market_data
        )
        
        print(f"交易决策: {'执行' if decision.should_trade else '跳过'}")
        print(f"投注金额: {decision.bet_amount:.2f} USDT")
        print(f"风险等级: {decision.risk_level.value}")
        print(f"决策原因: {decision.reason}")
        
        if not decision.should_trade:
            print("❌ 决策引擎决定不执行交易")
            return
        
        # 5. 创建交易记录
        print("\n5. 创建交易记录")
        print("-" * 40)
        
        trade_record = history_manager.create_trade_record(signal_result, decision)
        print(f"✅ 创建交易记录: {trade_record.trade_id}")
        
        # 6. 模拟交易执行
        print("\n6. 模拟交易执行")
        print("-" * 40)
        
        order_id = f"ORDER_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        predicted_price = 100.0
        entry_price = 99.95
        
        # 更新执行信息
        history_manager.update_execution(
            trade_record.trade_id,
            order_id,
            predicted_price,
            entry_price
        )
        
        print(f"✅ 交易执行: {order_id}")
        print(f"预测价格: {predicted_price:.2f}")
        print(f"入场价格: {entry_price:.2f}")
        
        # 添加到结算检查器
        settlement_checker.add_contract(
            order_id=order_id,
            symbol="BTCUSDT",
            direction=decision.direction,
            bet_amount=decision.bet_amount,
            predicted_price=predicted_price,
            decision=decision
        )
        
        print("✅ 添加到结算检查器")
        
        # 7. 模拟结算过程
        print("\n7. 模拟结算过程")
        print("-" * 40)
        
        # 等待一小段时间模拟交易时间
        await asyncio.sleep(1)
        
        # 模拟价格变化和结算
        import random
        price_change = random.choice([-2.0, -1.0, 0.0, 1.0, 2.0])
        final_price = predicted_price + price_change
        
        # 判断结果
        if decision.direction == "UP":
            if final_price > predicted_price:
                result = "win"
                pnl = decision.bet_amount * 0.8
            elif final_price == predicted_price:
                result = "tie"
                pnl = 0.0
            else:
                result = "loss"
                pnl = -decision.bet_amount
        else:  # DOWN
            if final_price < predicted_price:
                result = "win"
                pnl = decision.bet_amount * 0.8
            elif final_price == predicted_price:
                result = "tie"
                pnl = 0.0
            else:
                result = "loss"
                pnl = -decision.bet_amount
        
        print(f"最终价格: {final_price:.2f}")
        print(f"结算结果: {result}")
        print(f"盈亏金额: {pnl:+.2f} USDT")
        
        # 8. 更新结算信息
        print("\n8. 更新结算信息")
        print("-" * 40)
        
        # 更新历史管理器
        history_manager.update_settlement(
            trade_record.trade_id,
            final_price,
            result,
            pnl
        )
        
        print("✅ 更新交易历史记录")
        
        # 更新决策引擎
        decision_engine.update_trade_result(order_id, result, pnl)
        print("✅ 更新决策引擎记录")
        
        # 9. 发送通知
        print("\n9. 发送通知")
        print("-" * 40)
        
        # 发送结算通知
        success, error = dingtalk_notifier.send_settlement_notification(
            order_id=order_id,
            result=result,
            pnl=pnl,
            decision=decision
        )
        
        if success:
            print("✅ 结算通知发送成功")
        else:
            print(f"❌ 结算通知发送失败: {error}")
        
        # 10. 生成统计报告
        print("\n10. 生成统计报告")
        print("-" * 40)
        
        # 获取绩效指标
        metrics = history_manager.calculate_performance_metrics()
        print(f"总交易数: {metrics.total_trades}")
        print(f"胜率: {metrics.win_rate:.1%}")
        print(f"总盈亏: {metrics.total_pnl:+.2f} USDT")
        
        # 获取今日统计
        daily_stats = history_manager.get_daily_stats()
        print(f"今日交易: {daily_stats.trades}")
        print(f"今日盈亏: {daily_stats.pnl:+.2f} USDT")
        
        # 11. 导出数据
        print("\n11. 导出数据")
        print("-" * 40)
        
        # 导出CSV
        csv_path = history_manager.export_to_csv()
        print(f"✅ CSV导出: {csv_path}")
        
        # 生成绩效报告
        report = history_manager.generate_performance_report(save_to_file=True)
        print("✅ 绩效报告已生成")
        
        # 12. 展示完整流程总结
        print("\n12. 完整流程总结")
        print("-" * 40)
        
        print("🎯 交易流程完成！")
        print(f"  交易ID: {trade_record.trade_id}")
        print(f"  订单ID: {order_id}")
        print(f"  方向: {decision.direction}")
        print(f"  投注: {decision.bet_amount:.2f} USDT")
        print(f"  结果: {result}")
        print(f"  盈亏: {pnl:+.2f} USDT")
        print(f"  收益率: {(pnl/decision.bet_amount)*100:.1f}%")
        
        # 获取最新的交易记录
        updated_record = history_manager.get_trade_record(trade_record.trade_id)
        print(f"  状态: {updated_record.status}")
        print(f"  创建时间: {updated_record.created_at.strftime('%H:%M:%S')}")
        print(f"  结算时间: {updated_record.settlement_timestamp.strftime('%H:%M:%S')}")
        
        print("\n" + "=" * 60)
        print("✅ 交易历史管理器集成示例完成！")
        print("\n🎉 系统功能验证:")
        print("  ✅ 信号生成 → 决策制定 → 交易执行")
        print("  ✅ 结算跟踪 → 历史记录 → 统计分析")
        print("  ✅ 数据导出 → 绩效报告 → 通知发送")
        print("  ✅ 完整的交易生命周期管理")
        
    except Exception as e:
        print(f"❌ 集成示例过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
            print(f"\n🧹 清理临时文件: {temp_dir}")
        except:
            pass

if __name__ == "__main__":
    asyncio.run(demo_integrated_trade_history()) 