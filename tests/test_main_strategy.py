"""
测试事件合约主策略类
"""

import asyncio
import pytest
import sys
import os
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_main_strategy import EventContractMainStrategy


class TestEventContractMainStrategy:
    """测试事件合约主策略类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.config_data = {
            'PLATFORMS': {
                'binance': {
                    'access_key': 'test_key',
                    'secret_key': 'test_secret'
                }
            },
            'DINGTALK': 'https://test.webhook.url'
        }
    
    @patch('quant.strategies.event_contract_main_strategy.json.load')
    @patch('builtins.open')
    def test_init(self, mock_open, mock_json_load):
        """测试初始化"""
        mock_json_load.return_value = self.config_data
        
        strategy = EventContractMainStrategy()
        
        assert strategy.config == self.config_data
        assert strategy.is_running == False
        assert strategy.daily_stats['date'] == datetime.now().strftime('%Y-%m-%d')
        
        print("✅ 主策略初始化测试通过")
    
    @patch('quant.strategies.event_contract_main_strategy.json.load')
    @patch('builtins.open')
    async def test_start_stop(self, mock_open, mock_json_load):
        """测试启动和停止"""
        mock_json_load.return_value = self.config_data
        
        strategy = EventContractMainStrategy()
        
        # Mock所有组件
        strategy.dingtalk_notifier = AsyncMock()
        strategy.trade_history_manager = AsyncMock()
        strategy.settlement_checker = AsyncMock()
        strategy.signal_generator = AsyncMock()
        strategy.decision_engine = AsyncMock()
        strategy.api_client = AsyncMock()
        
        # 测试启动
        assert strategy.is_running == False
        
        # 模拟短暂运行
        async def mock_main_loop():
            await asyncio.sleep(0.1)
            strategy.is_running = False
        
        strategy._main_loop = mock_main_loop
        
        await strategy.start()
        
        print("✅ 主策略启动停止测试通过")
    
    @patch('quant.strategies.event_contract_main_strategy.json.load')
    @patch('builtins.open')
    async def test_signal_processing(self, mock_open, mock_json_load):
        """测试信号处理流程"""
        mock_json_load.return_value = self.config_data
        
        strategy = EventContractMainStrategy()
        
        # Mock所有组件
        strategy.dingtalk_notifier = AsyncMock()
        strategy.trade_history_manager = AsyncMock()
        strategy.settlement_checker = AsyncMock()
        strategy.api_client = AsyncMock()
        
        # Mock信号生成器
        strategy.signal_generator = AsyncMock()
        strategy.signal_generator.generate_signal.return_value = {
            'should_trade': True,
            'direction': 'UP',
            'current_price': 50000,
            'confidence': 0.8
        }
        
        # Mock决策引擎
        strategy.decision_engine = AsyncMock()
        strategy.decision_engine.make_decision.return_value = {
            'should_execute': True,
            'amount': 20
        }
        
        # Mock API下单
        strategy.api_client.place_order.return_value = {
            'success': True,
            'order_id': 'test_order_123'
        }
        
        # Mock交易历史管理器
        strategy.trade_history_manager.add_trade_record.return_value = 12345
        
        # 测试信号处理
        await strategy._generate_and_process_signals()
        
        # 验证调用
        strategy.signal_generator.generate_signal.assert_called_once()
        strategy.decision_engine.make_decision.assert_called_once()
        strategy.api_client.place_order.assert_called_once()
        strategy.trade_history_manager.add_trade_record.assert_called_once()
        
        print("✅ 信号处理流程测试通过")
    
    @patch('quant.strategies.event_contract_main_strategy.json.load')
    @patch('builtins.open')
    async def test_risk_management(self, mock_open, mock_json_load):
        """测试风险管理"""
        mock_json_load.return_value = self.config_data
        
        strategy = EventContractMainStrategy()
        
        # Mock钉钉通知器
        strategy.dingtalk_notifier = AsyncMock()
        
        # 测试软限制
        strategy.daily_stats['total_pnl'] = -1500
        await strategy._check_risk_limits()
        
        # 验证发送了风险警告
        strategy.dingtalk_notifier.send_message.assert_called()
        assert strategy.daily_stats['risk_level'] == 'HIGH'
        
        print("✅ 风险管理测试通过")
    
    @patch('quant.strategies.event_contract_main_strategy.json.load')
    @patch('builtins.open')
    async def test_notifications(self, mock_open, mock_json_load):
        """测试通知功能"""
        mock_json_load.return_value = self.config_data
        
        strategy = EventContractMainStrategy()
        
        # Mock钉钉通知器和交易历史管理器
        strategy.dingtalk_notifier = AsyncMock()
        strategy.trade_history_manager = AsyncMock()
        strategy.trade_history_manager.get_daily_performance.return_value = {
            'total_trades': 5,
            'total_pnl': 50.0,
            'win_rate': 0.6
        }
        
        # 测试启动通知
        await strategy._send_startup_notification()
        strategy.dingtalk_notifier.send_message.assert_called()
        
        # 测试停止通知
        await strategy._send_shutdown_notification()
        assert strategy.dingtalk_notifier.send_message.call_count == 2
        
        # 测试交易通知
        trade_record = {
            'direction': 'UP',
            'symbol': 'BTCUSDT',
            'amount': 20,
            'entry_price': 50000,
            'confidence': 0.8,
            'signal_time': datetime.now()
        }
        
        await strategy._send_trade_notification(trade_record, 12345)
        assert strategy.dingtalk_notifier.send_message.call_count == 3
        
        print("✅ 通知功能测试通过")
    
    @patch('quant.strategies.event_contract_main_strategy.json.load')
    @patch('builtins.open')
    async def test_status_reporting(self, mock_open, mock_json_load):
        """测试状态报告"""
        mock_json_load.return_value = self.config_data
        
        strategy = EventContractMainStrategy()
        
        # Mock交易历史管理器
        strategy.trade_history_manager = AsyncMock()
        strategy.trade_history_manager.get_daily_performance.return_value = {
            'total_trades': 10,
            'total_pnl': 100.0,
            'win_rate': 0.7
        }
        
        # 获取状态
        status = await strategy.get_status()
        
        assert 'is_running' in status
        assert 'daily_stats' in status
        assert 'performance' in status
        assert 'components_status' in status
        
        print("✅ 状态报告测试通过")


async def run_integration_test():
    """运行集成测试"""
    print("🧪 开始主策略集成测试...")
    
    try:
        # 创建测试实例
        test_instance = TestEventContractMainStrategy()
        test_instance.setup_method()
        
        # 运行各项测试
        await test_instance.test_start_stop()
        await test_instance.test_signal_processing()
        await test_instance.test_risk_management()
        await test_instance.test_notifications()
        await test_instance.test_status_reporting()
        
        print("\n✅ 所有主策略测试通过！")
        print("📊 测试结果:")
        print("- 初始化测试: ✅")
        print("- 启动停止测试: ✅")
        print("- 信号处理测试: ✅")
        print("- 风险管理测试: ✅")
        print("- 通知功能测试: ✅")
        print("- 状态报告测试: ✅")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(run_integration_test()) 