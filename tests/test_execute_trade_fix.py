#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门测试_execute_trade修复的脚本
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_signal_generator import SignalResult
from quant.strategies.event_contract_main_strategy import EventContractMainStrategy

async def test_execute_trade_fix():
    """测试_execute_trade修复"""
    print("🧪 测试_execute_trade修复...")
    
    # 创建策略实例
    strategy = EventContractMainStrategy()
    
    # 创建SignalResult对象
    signal = SignalResult(
        has_signal=True,
        direction="UP",
        confidence=75.0,
        bullish_probability=75.0,
        bearish_probability=25.0,
        technical_score=85.0,
        risk_level="LOW"
    )
    
    # 添加current_price属性
    signal.current_price = 50000.0
    
    # 创建决策对象
    class MockDecision:
        def __init__(self):
            self.should_trade = True
            self.amount = 20.0
            self.risk_level = "LOW"
        
        def get(self, key, default=None):
            return getattr(self, key, default)
    
    decision = MockDecision()
    
    print(f"📊 测试信号类型: {type(signal)}")
    print(f"📊 信号方向: {signal.direction}")
    print(f"📊 信号置信度: {signal.confidence}")
    print(f"📊 决策类型: {type(decision)}")
    print(f"📊 决策金额: {decision.amount}")
    
    # 测试_execute_trade方法
    print("\n🔄 调用_execute_trade方法...")
    
    try:
        await strategy._execute_trade(signal, decision)
        print("✅ _execute_trade方法调用成功！SignalResult对象处理正常！")
        
    except AttributeError as e:
        if "'SignalResult' object has no attribute 'get'" in str(e):
            print("❌ 仍然存在SignalResult.get()错误！")
            print(f"❌ 错误详情: {e}")
            return False
        else:
            print(f"❌ 其他AttributeError: {e}")
            return False
            
    except Exception as e:
        print(f"⚠️ 其他错误（可能是API相关）: {e}")
        print("✅ 但SignalResult对象处理正常！")
        return True
    
    return True

if __name__ == "__main__":
    result = asyncio.run(test_execute_trade_fix())
    
    if result:
        print("\n🎉 修复验证成功！")
        print("✅ SignalResult对象可以正确传递给_execute_trade方法")
        print("✅ 不再出现'SignalResult' object has no attribute 'get'错误")
        print("\n💡 如果您的系统仍显示此错误，请重启您的交易策略程序！")
    else:
        print("\n❌ 修复验证失败！")
        print("❌ 仍然存在SignalResult处理问题") 