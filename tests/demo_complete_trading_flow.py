#!/usr/bin/env python3
"""
完整交易流程演示
展示信号生成器 + 决策引擎 + 钉钉通知器的完整集成
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from unittest.mock import patch
import time
from quant.strategies.event_contract_signal_generator_simple import (
    EventContractSignalGeneratorSimple,
    SignalResult
)
from quant.strategies.event_contract_decision_engine import (
    EventContractDecisionEngine,
    RiskManagementConfig,
    TradingDecision,
    RiskLevel,
    MarketCondition
)
from quant.strategies.event_contract_dingtalk_notifier import (
    EventContractDingtalkNotifier,
    NotificationConfig
)

class CompleteEventContractTradingSystem:
    """完整的事件合约交易系统"""
    
    def __init__(self):
        """初始化系统"""
        # 1. 初始化信号生成器
        self.signal_generator = EventContractSignalGeneratorSimple()
        
        # 2. 初始化决策引擎
        risk_config = RiskManagementConfig(
            base_bet_amount=20.0,
            daily_soft_limit=1000.0,
            daily_hard_limit=10000.0,
            max_bet_amount=100.0,
            min_bet_amount=10.0,
            win_rate_threshold=0.65
        )
        self.decision_engine = EventContractDecisionEngine(risk_config)
        
        # 3. 初始化钉钉通知器
        notification_config = NotificationConfig(
            enable_signal_notification=True,
            enable_settlement_notification=True,
            enable_risk_notification=True,
            enable_daily_summary=True,
            min_signal_interval=30,
            max_daily_notifications=100
        )
        self.notifier = EventContractDingtalkNotifier(notification_config)
        
        # 系统状态
        self.current_balance = 5000.0
        self.active_orders = {}
        self.system_running = True
        
        print("🚀 完整事件合约交易系统已初始化")
        print(f"   💰 初始余额: {self.current_balance:.2f} USDT")
        print(f"   📊 风险限制: {risk_config.daily_soft_limit:.2f} USDT")
        print(f"   📢 通知配置: 已启用所有通知类型")
        print()
    
    def process_trading_signal(self, kline_data, market_data=None):
        """处理交易信号的完整流程"""
        print(f"📊 处理新的K线数据: {kline_data.get('close', 'N/A')}")
        
        # 1. 首先添加K线数据到信号生成器
        self.signal_generator.add_kline_data(
            timestamp=int(kline_data['timestamp'].timestamp() * 1000),
            open_price=kline_data['open'],
            high_price=kline_data['high'],
            low_price=kline_data['low'],
            close_price=kline_data['close'],
            volume=kline_data['volume']
        )
        
        # 2. 生成交易信号
        signal_result = self.signal_generator.generate_signal()
        print(f"   🔍 信号分析: {signal_result.market_status}")
        
        if signal_result.user_reminder:
            print(f"   💡 用户提醒: {signal_result.user_reminder}")
        
        # 2. 如果有信号，进行决策
        if signal_result.has_signal:
            print(f"   ✅ 发现交易信号: {signal_result.direction}")
            
            # 生成交易决策
            decision = self.decision_engine.make_trading_decision(
                signal_result=signal_result,
                current_balance=self.current_balance,
                market_data=market_data
            )
            
            print(f"   🎯 决策结果: {'执行' if decision.should_trade else '跳过'}")
            print(f"   💰 投注金额: {decision.bet_amount:.2f} USDT")
            print(f"   ⚠️ 风险等级: {decision.risk_level.value}")
            
            # 3. 如果决策执行交易，发送通知并模拟下单
            if decision.should_trade:
                # 发送交易信号通知
                self._send_trading_signal_notification(decision, signal_result, market_data)
                
                # 模拟下单
                order_id = self._simulate_place_order(decision)
                
                # 模拟10分钟后的结算检查
                self._simulate_settlement_check(order_id, decision)
                
                return decision
            else:
                print(f"   ⏭️ 跳过原因: {decision.reason}")
        else:
            print(f"   ⏳ 无交易信号")
        
        return None
    
    def _send_trading_signal_notification(self, decision, signal_result, market_data):
        """发送交易信号通知"""
        print(f"   📢 发送交易信号通知...")
        
        # 使用mock避免实际发送钉钉消息
        with patch('quant.strategies.event_contract_dingtalk_notifier.Dingtalk.markdown') as mock_dingtalk:
            mock_dingtalk.return_value = ({"errcode": 0, "errmsg": "ok"}, None)
            
            success, error = self.notifier.send_trading_signal(
                decision=decision,
                signal_result=signal_result,
                market_data=market_data
            )
            
            if success:
                print(f"   ✅ 钉钉通知发送成功")
            else:
                print(f"   ❌ 钉钉通知发送失败: {error}")
    
    def _simulate_place_order(self, decision):
        """模拟下单"""
        order_id = f"order_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 记录订单
        self.active_orders[order_id] = {
            'decision': decision,
            'timestamp': datetime.now(),
            'status': 'pending'
        }
        
        # 记录到决策引擎
        self.decision_engine.record_trade(
            decision=decision,
            order_id=order_id,
            executed_price=95000.0,  # 模拟价格
            executed_amount=decision.bet_amount
        )
        
        print(f"   📝 订单已提交: {order_id}")
        print(f"   ⏰ 预计10分钟后结算")
        
        return order_id
    
    def _simulate_settlement_check(self, order_id, decision):
        """模拟结算检查"""
        print(f"   ⏳ 模拟10分钟后的结算检查...")
        
        # 模拟结算结果（基于信心度）
        import random
        win_probability = decision.confidence / 100.0
        is_win = random.random() < win_probability
        
        result = "win" if is_win else "loss"
        pnl = decision.bet_amount * 0.9 if is_win else -decision.bet_amount
        
        # 更新余额
        self.current_balance += pnl
        
        # 更新订单状态
        if order_id in self.active_orders:
            self.active_orders[order_id]['status'] = result
            self.active_orders[order_id]['pnl'] = pnl
        
        # 更新决策引擎
        self.decision_engine.update_trade_result(order_id, result, pnl)
        
        print(f"   📊 结算结果: {'🎉 盈利' if is_win else '😔 亏损'}")
        print(f"   💰 盈亏金额: {pnl:+.2f} USDT")
        print(f"   💳 账户余额: {self.current_balance:.2f} USDT")
        
        # 发送结算通知
        self._send_settlement_notification(order_id, result, pnl, decision)
        
        # 检查风险状态
        self._check_risk_status()
    
    def _send_settlement_notification(self, order_id, result, pnl, decision):
        """发送结算通知"""
        print(f"   📢 发送结算通知...")
        
        with patch('quant.strategies.event_contract_dingtalk_notifier.Dingtalk.markdown') as mock_dingtalk:
            mock_dingtalk.return_value = ({"errcode": 0, "errmsg": "ok"}, None)
            
            success, error = self.notifier.send_settlement_notification(
                order_id=order_id,
                result=result,
                pnl=pnl,
                decision=decision
            )
            
            if success:
                print(f"   ✅ 结算通知发送成功")
            else:
                print(f"   ❌ 结算通知发送失败: {error}")
    
    def _check_risk_status(self):
        """检查风险状态"""
        should_stop, reason = self.decision_engine.should_stop_trading()
        
        if should_stop:
            print(f"   ⚠️ 风险警告: {reason}")
            self._send_risk_alert(reason)
            self.system_running = False
        
        # 检查是否需要风险提醒
        risk_summary = self.decision_engine.get_risk_summary()
        if risk_summary['daily_loss_ratio'] > 0.3:  # 损失超过30%
            print(f"   ⚠️ 风险提醒: 当日损失比例 {risk_summary['daily_loss_ratio']:.1%}")
            self._send_risk_alert(
                f"当日损失比例达到 {risk_summary['daily_loss_ratio']:.1%}，请谨慎操作"
            )
    
    def _send_risk_alert(self, message):
        """发送风险提醒"""
        print(f"   📢 发送风险提醒...")
        
        with patch('quant.strategies.event_contract_dingtalk_notifier.Dingtalk.markdown') as mock_dingtalk:
            mock_dingtalk.return_value = ({"errcode": 0, "errmsg": "ok"}, None)
            
            risk_summary = self.decision_engine.get_risk_summary()
            success, error = self.notifier.send_risk_alert(
                risk_type="交易风险",
                message=message,
                current_loss=risk_summary['daily_loss'],
                loss_ratio=risk_summary['daily_loss_ratio']
            )
            
            if success:
                print(f"   ✅ 风险提醒发送成功")
            else:
                print(f"   ❌ 风险提醒发送失败: {error}")
    
    def send_daily_summary(self):
        """发送每日总结"""
        print(f"📊 发送每日交易总结...")
        
        stats = self.decision_engine.get_daily_stats()
        risk_summary = self.decision_engine.get_risk_summary()
        
        with patch('quant.strategies.event_contract_dingtalk_notifier.Dingtalk.markdown') as mock_dingtalk:
            mock_dingtalk.return_value = ({"errcode": 0, "errmsg": "ok"}, None)
            
            success, error = self.notifier.send_daily_summary(stats, risk_summary)
            
            if success:
                print(f"   ✅ 每日总结发送成功")
            else:
                print(f"   ❌ 每日总结发送失败: {error}")
        
        # 显示统计信息
        print(f"   📈 今日交易统计:")
        print(f"      总交易数: {stats['total_trades']}")
        print(f"      胜率: {stats['win_rate']:.1%}")
        print(f"      总盈亏: {stats['total_pnl']:+.2f} USDT")
        print(f"      连胜/连败: {stats['current_streak']}")
        print(f"      当日损失: {risk_summary['daily_loss']:.2f} USDT")

def simulate_trading_day():
    """模拟一个交易日"""
    print("🌅 开始模拟交易日")
    print("=" * 60)
    
    # 初始化系统
    trading_system = CompleteEventContractTradingSystem()
    
    # 模拟多个K线数据
    kline_scenarios = [
        {
            'name': '📈 强势上涨',
            'data': {
                'open': 94000.0,
                'high': 95500.0,
                'low': 93800.0,
                'close': 95200.0,
                'volume': 1200000,
                'timestamp': datetime.now()
            },
            'market_data': {
                'price': 95200.0,
                'volatility': 0.025,
                'trend_strength': 0.8,
                'price_change_24h': 0.06
            }
        },
        {
            'name': '📉 震荡下跌',
            'data': {
                'open': 95200.0,
                'high': 95400.0,
                'low': 94100.0,
                'close': 94300.0,
                'volume': 900000,
                'timestamp': datetime.now()
            },
            'market_data': {
                'price': 94300.0,
                'volatility': 0.015,
                'trend_strength': 0.3,
                'price_change_24h': -0.02
            }
        },
        {
            'name': '⚡ 高波动突破',
            'data': {
                'open': 94300.0,
                'high': 96800.0,
                'low': 93500.0,
                'close': 96200.0,
                'volume': 1800000,
                'timestamp': datetime.now()
            },
            'market_data': {
                'price': 96200.0,
                'volatility': 0.08,
                'trend_strength': 0.9,
                'price_change_24h': 0.12
            }
        },
        {
            'name': '🔄 横盘整理',
            'data': {
                'open': 96200.0,
                'high': 96500.0,
                'low': 95800.0,
                'close': 96100.0,
                'volume': 600000,
                'timestamp': datetime.now()
            },
            'market_data': {
                'price': 96100.0,
                'volatility': 0.008,
                'trend_strength': 0.2,
                'price_change_24h': 0.005
            }
        }
    ]
    
    executed_trades = []
    
    # 处理每个K线
    for i, scenario in enumerate(kline_scenarios, 1):
        print(f"\n🕐 {datetime.now().strftime('%H:%M:%S')} - 处理第{i}个K线")
        print(f"📊 场景: {scenario['name']}")
        print(f"📈 价格: {scenario['data']['open']:.0f} → {scenario['data']['close']:.0f}")
        print(f"📊 波动率: {scenario['market_data']['volatility']:.2%}")
        print("-" * 50)
        
        # 处理交易信号
        decision = trading_system.process_trading_signal(
            kline_data=scenario['data'],
            market_data=scenario['market_data']
        )
        
        if decision:
            executed_trades.append(decision)
        
        print("-" * 50)
        
        # 检查是否应该停止
        if not trading_system.system_running:
            print("⏹️ 系统已停止交易")
            break
        
        # 模拟时间间隔
        time.sleep(0.1)
    
    # 发送每日总结
    print(f"\n🌆 交易日结束")
    print("=" * 60)
    trading_system.send_daily_summary()
    
    # 显示最终统计
    print(f"\n📊 最终统计:")
    print(f"   执行交易数: {len(executed_trades)}")
    print(f"   最终余额: {trading_system.current_balance:.2f} USDT")
    print(f"   盈亏: {trading_system.current_balance - 5000.0:+.2f} USDT")
    
    # 通知统计
    notification_stats = trading_system.notifier.get_notification_stats()
    print(f"\n📢 通知统计:")
    print(f"   总通知数: {notification_stats['total_today']}")
    print(f"   各类型通知: {notification_stats['types_today']}")
    
    return trading_system

def main():
    """主函数"""
    try:
        trading_system = simulate_trading_day()
        
        print(f"\n🎉 完整交易流程演示完成！")
        print(f"系统已成功集成信号生成、决策引擎和钉钉通知功能。")
        
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()