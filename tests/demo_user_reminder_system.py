#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示用户提醒系统

展示在15分钟K线无信号时的用户提醒功能
包括：
1. 数据不足时的提醒
2. 市场质量过滤时的提醒
3. 入场机会不明确时的提醒
4. 接近信号阈值时的提醒
5. 常规监控时的提醒
"""

import random
import time
from datetime import datetime
from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple


def demo_user_reminder_system():
    """演示用户提醒系统的各种场景"""
    
    print("🔔 用户提醒系统演示")
    print("=" * 80)
    print("✨ 功能展示:")
    print("   📊 数据不足时的友好提醒")
    print("   🔍 市场质量过滤时的状态说明")
    print("   🎯 入场机会不明确时的建议")
    print("   📈 接近信号阈值时的预警")
    print("   ⏰ 常规监控时的状态更新")
    print("=" * 80)
    
    # 创建信号生成器
    generator = EventContractSignalGeneratorSimple(
        signal_threshold=75.0,
        min_timeframe_consensus=1,
        confidence_threshold=65.0
    )
    
    print("\n📊 系统配置:")
    print(f"   🎯 信号阈值: {generator.signal_threshold}%")
    print(f"   🔄 最少共识: {generator.min_timeframe_consensus}个时间周期")
    print(f"   📊 置信度要求: {generator.confidence_threshold}%")
    
    # 场景1：数据不足时的提醒
    print("\n" + "🔔 场景1: 数据收集阶段".center(80, "="))
    print("💡 模拟系统刚启动，数据还不充足的情况")
    print("-" * 60)
    
    # 只添加少量数据
    base_price = 95000
    start_time = int(datetime.now().timestamp() * 1000)
    
    for i in range(5):  # 只添加5分钟数据
        timestamp = start_time + i * 60 * 1000
        change = (random.random() - 0.5) * 0.01
        
        open_price = base_price
        close_price = open_price * (1 + change)
        high_price = max(open_price, close_price) * (1 + random.random() * 0.003)
        low_price = min(open_price, close_price) * (1 - random.random() * 0.003)
        volume = 1000 + random.random() * 500
        
        generator.add_kline_data(
            timestamp=timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume
        )
        base_price = close_price
    
    # 生成信号（应该提醒数据不足）
    print("🔄 尝试生成信号...")
    signal1 = generator.generate_signal()
    
    print(f"📋 信号结果: {signal1.market_status}")
    print(f"💬 用户提醒: 已显示")
    
    # 场景2：市场质量过滤时的提醒
    print("\n" + "🔔 场景2: 市场质量过滤".center(80, "="))
    print("💡 模拟市场振幅过小，被质量过滤的情况")
    print("-" * 60)
    
    # 创建新的生成器
    generator2 = EventContractSignalGeneratorSimple(
        signal_threshold=70.0,
        min_timeframe_consensus=1,
        confidence_threshold=60.0
    )
    
    # 生成低振幅数据
    for i in range(120):
        timestamp = start_time + i * 60 * 1000
        # 极小振幅变化
        change = (random.random() - 0.5) * 0.006  # ±0.3%
        
        open_price = base_price
        close_price = open_price * (1 + change)
        high_price = max(open_price, close_price) * (1 + random.random() * 0.001)
        low_price = min(open_price, close_price) * (1 - random.random() * 0.001)
        volume = 800 + random.random() * 400
        
        generator2.add_kline_data(
            timestamp=timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume
        )
        base_price = close_price
    
    print("🔄 尝试生成信号...")
    signal2 = generator2.generate_signal()
    
    print(f"📋 信号结果: {signal2.market_status}")
    print(f"💬 用户提醒: 已显示")
    
    # 场景3：接近信号阈值时的提醒
    print("\n" + "🔔 场景3: 接近信号阈值".center(80, "="))
    print("💡 模拟信号概率接近阈值，即将触发的情况")
    print("-" * 60)
    
    # 创建新的生成器，降低阈值便于演示
    generator3 = EventContractSignalGeneratorSimple(
        signal_threshold=80.0,  # 设置较高阈值
        min_timeframe_consensus=1,
        confidence_threshold=60.0
    )
    
    # 生成有一定趋势但未达到阈值的数据
    for i in range(150):
        timestamp = start_time + i * 60 * 1000
        # 温和上涨趋势
        trend_change = 0.004  # 每分钟0.4%
        random_change = (random.random() - 0.5) * 0.006
        total_change = trend_change + random_change
        
        open_price = base_price
        close_price = open_price * (1 + total_change)
        high_price = max(open_price, close_price) * (1 + random.random() * 0.003)
        low_price = min(open_price, close_price) * (1 - random.random() * 0.002)
        volume = 1000 + random.random() * 800
        
        generator3.add_kline_data(
            timestamp=timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume
        )
        base_price = close_price
    
    print("🔄 尝试生成信号...")
    signal3 = generator3.generate_signal()
    
    print(f"📋 信号结果: {signal3.market_status}")
    print(f"💬 用户提醒: 已显示")
    
    # 场景4：常规监控时的提醒
    print("\n" + "🔔 场景4: 常规监控状态".center(80, "="))
    print("💡 模拟正常市场条件，但信号不足的情况")
    print("-" * 60)
    
    # 创建新的生成器
    generator4 = EventContractSignalGeneratorSimple(
        signal_threshold=90.0,  # 设置很高阈值
        min_timeframe_consensus=2,
        confidence_threshold=80.0
    )
    
    # 生成正常波动数据
    for i in range(180):
        timestamp = start_time + i * 60 * 1000
        # 正常随机波动
        change = (random.random() - 0.5) * 0.015  # ±0.75%
        
        open_price = base_price
        close_price = open_price * (1 + change)
        high_price = max(open_price, close_price) * (1 + random.random() * 0.005)
        low_price = min(open_price, close_price) * (1 - random.random() * 0.005)
        volume = 1200 + random.random() * 800
        
        generator4.add_kline_data(
            timestamp=timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume
        )
        base_price = close_price
    
    print("🔄 尝试生成信号...")
    signal4 = generator4.generate_signal()
    
    print(f"📋 信号结果: {signal4.market_status}")
    print(f"💬 用户提醒: 已显示")
    
    # 场景5：成功信号时的提醒
    print("\n" + "🔔 场景5: 成功信号确认".center(80, "="))
    print("💡 模拟检测到明确交易信号的情况")
    print("-" * 60)
    
    # 创建新的生成器，使用较低阈值便于触发
    generator5 = EventContractSignalGeneratorSimple(
        signal_threshold=60.0,  # 降低阈值
        min_timeframe_consensus=1,
        confidence_threshold=50.0
    )
    
    # 生成强烈上涨趋势数据
    for i in range(200):
        timestamp = start_time + i * 60 * 1000
        # 强势上涨趋势
        if i < 100:
            trend_change = 0.008  # 前期强势上涨
        else:
            trend_change = 0.012  # 后期加速上涨
        
        random_change = (random.random() - 0.5) * 0.004
        total_change = trend_change + random_change
        
        open_price = base_price
        close_price = open_price * (1 + total_change)
        
        # 大部分是阳线
        if close_price > open_price:
            high_price = close_price * (1 + random.random() * 0.008)
            low_price = open_price * (1 - random.random() * 0.002)
        else:
            high_price = open_price * (1 + random.random() * 0.002)
            low_price = close_price * (1 - random.random() * 0.008)
        
        volume = 1500 + random.random() * 1000
        
        generator5.add_kline_data(
            timestamp=timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            volume=volume
        )
        base_price = close_price
    
    print("🔄 尝试生成信号...")
    signal5 = generator5.generate_signal()
    
    print(f"📋 信号结果: {signal5.market_status}")
    print(f"💬 用户提醒: 已显示")
    
    # 总结
    print("\n" + "🎉 演示总结".center(80, "="))
    print("✅ 用户提醒系统功能展示完成!")
    print()
    print("📊 演示的提醒类型:")
    print(f"   📈 数据收集中: {signal1.market_status}")
    print(f"   🔍 市场质量过滤: {signal2.market_status}")
    print(f"   📊 信号待确认: {signal3.market_status}")
    print(f"   ⏰ 常规监控中: {signal4.market_status}")
    print(f"   ✅ 信号确认: {signal5.market_status}")
    print()
    print("💡 用户体验改进:")
    print("   ✅ 明确告知系统当前状态")
    print("   ✅ 提供具体的建议和说明")
    print("   ✅ 包含时间戳和详细信息")
    print("   ✅ 不同场景的个性化提醒")
    print("   ✅ 让用户了解系统正在工作")
    
    return [signal1, signal2, signal3, signal4, signal5]


if __name__ == "__main__":
    try:
        demo_user_reminder_system()
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()