#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整演示：增强版事件合约信号生成系统

展示新增的市场质量过滤和入场机会评估功能
包括：
1. K线实体大小检查（振幅≥0.5%）
2. 趋势显著性评估
3. 清晰入场机会识别
4. 智能信号过滤
"""

import random
from datetime import datetime
from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple


def create_realistic_market_scenario():
    """创建真实的市场场景数据"""
    
    scenarios = []
    base_price = 95000
    start_time = int(datetime.now().timestamp() * 1000)
    
    print("📊 生成真实市场场景数据...")
    
    # 场景1：早期整理阶段（应被过滤）
    print("   📅 00:00-01:00: 早期整理阶段")
    for i in range(60):
        timestamp = start_time + i * 60 * 1000
        # 小幅波动，无明确趋势
        change = (random.random() - 0.5) * 0.008  # ±0.4%
        
        open_price = base_price
        close_price = open_price * (1 + change)
        high_price = max(open_price, close_price) * (1 + random.random() * 0.002)
        low_price = min(open_price, close_price) * (1 - random.random() * 0.002)
        volume = 800 + random.random() * 400
        
        scenarios.append({
            'timestamp': timestamp,
            'open_price': open_price,
            'high_price': high_price,
            'low_price': low_price,
            'close_price': close_price,
            'volume': volume,
            'scenario': '整理阶段'
        })
        base_price = close_price
    
    # 场景2：突破开始（应该通过过滤）
    print("   📅 01:00-02:30: 突破上涨阶段")
    for i in range(60, 150):
        timestamp = start_time + i * 60 * 1000
        # 强势上涨趋势
        trend_change = 0.008   # 每分钟上涨0.8%
        random_change = (random.random() - 0.5) * 0.004
        total_change = trend_change + random_change
        
        open_price = base_price
        close_price = open_price * (1 + total_change)
        
        if close_price > open_price:  # 阳线
            high_price = close_price * (1 + random.random() * 0.006)
            low_price = open_price * (1 - random.random() * 0.002)
        else:  # 阴线
            high_price = open_price * (1 + random.random() * 0.002)
            low_price = close_price * (1 - random.random() * 0.006)
        
        # 突破时成交量放大
        if 75 <= i <= 85:  # 突破期间
            volume = 2500 + random.random() * 1500
        else:
            volume = 1200 + random.random() * 800
        
        scenarios.append({
            'timestamp': timestamp,
            'open_price': open_price,
            'high_price': high_price,
            'low_price': low_price,
            'close_price': close_price,
            'volume': volume,
            'scenario': '突破上涨'
        })
        base_price = close_price
    
    # 场景3：高位震荡（应被部分过滤）
    print("   📅 02:30-03:30: 高位震荡阶段")
    for i in range(150, 210):
        timestamp = start_time + i * 60 * 1000
        # 高位小幅震荡
        change = (random.random() - 0.5) * 0.012  # ±0.6%
        
        open_price = base_price
        close_price = open_price * (1 + change)
        high_price = max(open_price, close_price) * (1 + random.random() * 0.004)
        low_price = min(open_price, close_price) * (1 - random.random() * 0.004)
        volume = 1000 + random.random() * 600
        
        scenarios.append({
            'timestamp': timestamp,
            'open_price': open_price,
            'high_price': high_price,
            'low_price': low_price,
            'close_price': close_price,
            'volume': volume,
            'scenario': '高位震荡'
        })
        base_price = close_price
    
    print(f"✅ 生成完成: 总计{len(scenarios)}分钟数据，价格从$95,000变化到${base_price:,.0f}")
    return scenarios


def demo_enhanced_signal_system():
    """演示增强版信号生成系统"""
    
    print("🚀 增强版事件合约信号生成系统演示")
    print("=" * 80)
    print("✨ 新功能:")
    print("   📏 K线实体大小检查（振幅≥0.5%）")
    print("   📈 趋势显著性评估（3K变化≥1%）")
    print("   🎯 清晰入场机会识别")
    print("   🔍 智能信号过滤")
    print("=" * 80)
    
    # 创建信号生成器
    generator = EventContractSignalGeneratorSimple(
        signal_threshold=75.0,
        min_timeframe_consensus=1,
        confidence_threshold=65.0
    )
    
    print("\n📊 配置信息:")
    print(f"   🎯 信号阈值: {generator.signal_threshold}%")
    print(f"   🔄 最少共识: {generator.min_timeframe_consensus}个时间周期")
    print(f"   📊 置信度要求: {generator.confidence_threshold}%")
    
    # 生成市场场景数据
    market_data = create_realistic_market_scenario()
    
    print(f"\n📈 逐步添加市场数据并监控信号...")
    print("-" * 60)
    
    # 分阶段添加数据并测试
    stage_points = [60, 120, 180, 210]  # 测试点
    stage_names = ["整理期结束", "突破期中段", "突破期结束", "震荡期结束"]
    
    for i, data_point in enumerate(market_data):
        generator.add_kline_data(**{k: v for k, v in data_point.items() if k != 'scenario'})
        
        # 在关键时间点测试信号生成
        if (i + 1) in stage_points:
            stage_idx = stage_points.index(i + 1)
            stage_name = stage_names[stage_idx]
            
            print(f"\n🔍 阶段{stage_idx + 1}测试 - {stage_name} (第{i+1}分钟)")
            print(f"💰 当前价格: ${data_point['close_price']:,.0f}")
            print(f"📊 当前场景: {data_point['scenario']}")
            
            # 检查数据状态
            status = generator.get_status()
            for timeframe in ['15m', '30m', '1h']:
                count = status[timeframe]['total_klines']
                print(f"   {timeframe}: {count}根K线")
            
            # 生成信号
            print("\n   🎯 信号生成测试:")
            signal = generator.generate_signal()
            
            if signal.has_signal:
                direction_emoji = "🚀" if signal.direction == "UP" else "📉"
                print(f"   ✅ 检测到{direction_emoji} {signal.direction}信号!")
                print(f"      📊 置信度: {signal.confidence:.1f}%")
                print(f"      🔧 技术评分: {signal.technical_score:.1f}/100")
                print(f"      ⚠️ 风险等级: {signal.risk_level}")
                
                # 显示入场机会
                if 'entry_opportunity' in signal.timeframe_analysis:
                    entry = signal.timeframe_analysis['entry_opportunity']
                    print(f"      🎯 入场机会: {entry['opportunity_type']} (强度:{entry['strength']}/100)")
            else:
                print(f"   ❌ 无有效信号")
                
                # 显示过滤原因
                if signal.timeframe_analysis:
                    if 'market_quality' in signal.timeframe_analysis:
                        quality = signal.timeframe_analysis['market_quality']
                        if not quality['suitable_for_trading']:
                            print(f"      📝 原因: 市场质量过滤 - {quality['reason']}")
                    
                    if 'entry_opportunity' in signal.timeframe_analysis:
                        entry = signal.timeframe_analysis['entry_opportunity']
                        if not entry['has_clear_opportunity']:
                            print(f"      📝 原因: 入场机会不足 - {entry['reason']}")
            
            print("-" * 60)
    
    # 最终状态检查
    print(f"\n📋 最终系统状态:")
    final_status = generator.get_status()
    
    print(f"📊 K线数据统计:")
    for timeframe in ['1m', '5m', '15m', '30m', '1h']:
        info = final_status[timeframe]
        count = info['total_klines']
        latest = info['latest_kline']
        if latest and latest['timestamp']:
            kline_type = "🟢阳线" if latest['is_bullish'] else "🔴阴线" if latest['is_bearish'] else "➖十字"
            print(f"   {timeframe:>3}: {count:>2}根K线, 最新: {kline_type} ${latest['close']:>8,.0f}")
    
    # 最终信号测试
    print(f"\n🎯 最终信号生成测试:")
    final_signal = generator.generate_signal()
    
    if final_signal.has_signal:
        direction_emoji = "🚀" if final_signal.direction == "UP" else "📉"
        print(f"""
✅ 系统最终输出: {direction_emoji} {final_signal.direction}信号
   📊 置信度: {final_signal.confidence:.1f}%
   📈 看涨概率: {final_signal.bullish_probability:.1f}%
   📉 看跌概率: {final_signal.bearish_probability:.1f}%
   🔧 技术评分: {final_signal.technical_score:.1f}/100
   ⚠️ 风险等级: {final_signal.risk_level}
        """)
        
        if 'entry_opportunity' in final_signal.timeframe_analysis:
            entry = final_signal.timeframe_analysis['entry_opportunity']
            print(f"   🎯 入场机会: {entry['opportunity_type']} (强度:{entry['strength']}/100)")
            if entry.get('opportunity_signals'):
                print(f"   📋 支撑信号: {', '.join(entry['opportunity_signals'])}")
    else:
        print(f"❌ 系统最终判断: 当前不适合交易")
        
        # 详细分析原因
        if final_signal.timeframe_analysis:
            if 'market_quality' in final_signal.timeframe_analysis:
                quality = final_signal.timeframe_analysis['market_quality']
                print(f"   📊 市场质量评分: {quality['quality_score']}/100")
                if not quality['suitable_for_trading']:
                    print(f"   📝 过滤原因: {quality['reason']}")
    
    print("\n" + "=" * 80)
    print("🎉 增强版信号生成系统演示完成!")
    print("")
    print("💡 系统改进总结:")
    print("   ✅ 智能过滤小振幅K线（<0.5%）")
    print("   ✅ 识别趋势显著性（3K变化≥1%）")
    print("   ✅ 评估入场机会清晰度")
    print("   ✅ 减少无效信号，提高信号质量")
    print("   ✅ 只在有明确交易机会时输出信号")
    
    return final_signal


if __name__ == "__main__":
    try:
        demo_enhanced_signal_system()
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()