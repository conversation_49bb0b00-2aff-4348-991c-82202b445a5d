#!/usr/bin/env python3
"""
信号结算系统演示程序
展示完整的信号生成、跟踪、结算和统计功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import time
from datetime import datetime, timedelta
from quant.strategies.signal_settlement_checker import SignalSettlementChecker


class SignalSettlementDemo:
    """信号结算系统演示类"""
    
    def __init__(self):
        self.checker = SignalSettlementChecker(db_path="./data/demo_signal_settlement.db")
        self.demo_signals = []
        
    def run_demo(self):
        """运行完整演示"""
        print("🚀 欢迎使用信号结算系统演示！")
        print("="*60)
        
        # 阶段1：添加信号
        self.demo_add_signals()
        
        # 阶段2：显示当前状态
        self.demo_show_status()
        
        # 阶段3：模拟结算
        self.demo_simulate_settlement()
        
        # 阶段4：显示统计结果
        self.demo_show_statistics()
        
        # 阶段5：导出数据
        self.demo_export_data()
        
        print("\n🎉 演示完成！")
        print("="*60)
        print("💡 现在您可以将此系统集成到主策略中，开始实际的信号跟踪和胜率统计。")
        
    def demo_add_signals(self):
        """演示添加信号功能"""
        print("\n📝 第1步：添加信号到结算跟踪器")
        print("-" * 40)
        
        # 模拟不同类型的信号
        demo_signals = [
            {
                'name': '强势看涨信号',
                'direction': 'UP',
                'confidence': 85.0,
                'signal_price': 50000.0,
                'signal_strength': 'STRONG',
                'supporting_indicators': ['RSI_15m_超卖', 'MACD_15m_看涨', 'EMA_15m_多头排列'],
                'market_conditions': 'NORMAL'
            },
            {
                'name': '中等看跌信号',
                'direction': 'DOWN',
                'confidence': 65.0,
                'signal_price': 50200.0,
                'signal_strength': 'MEDIUM',
                'supporting_indicators': ['RSI_15m_超买', '布林带_15m_上轨阻力'],
                'market_conditions': 'NORMAL'
            },
            {
                'name': '弱势看涨信号',
                'direction': 'UP',
                'confidence': 45.0,
                'signal_price': 49800.0,
                'signal_strength': 'WEAK',
                'supporting_indicators': ['MACD_15m_看涨'],
                'market_conditions': 'VOLATILE'
            },
            {
                'name': '强势看跌信号',
                'direction': 'DOWN',
                'confidence': 80.0,
                'signal_price': 50500.0,
                'signal_strength': 'STRONG',
                'supporting_indicators': ['RSI_15m_超买', 'EMA_15m_空头排列', 'MACD_15m_看跌'],
                'market_conditions': 'NORMAL'
            }
        ]
        
        for signal in demo_signals:
            signal_id = self.checker.add_signal_record(signal)
            if signal_id:
                self.demo_signals.append({
                    'id': signal_id,
                    'name': signal['name'],
                    'direction': signal['direction'],
                    'confidence': signal['confidence'],
                    'signal_price': signal['signal_price'],
                    'signal_strength': signal['signal_strength']
                })
                print(f"✅ {signal['name']}: {signal_id}")
                print(f"   方向: {signal['direction']} | 价格: {signal['signal_price']:.0f} | 置信度: {signal['confidence']:.1f}%")
            else:
                print(f"❌ 添加信号失败: {signal['name']}")
        
        print(f"\n📊 成功添加 {len(self.demo_signals)} 个信号到跟踪器")
        
    def demo_show_status(self):
        """演示显示当前状态"""
        print("\n📊 第2步：查看当前系统状态")
        print("-" * 40)
        
        pending_count = self.checker.get_pending_signals_count()
        print(f"⏳ 待结算信号数量: {pending_count}")
        
        stats = self.checker.get_settlement_stats(days=1)
        print(f"📈 今日统计:")
        print(f"   总结算: {stats['total_settled']}")
        print(f"   胜率: {stats['overall_win_rate']:.1f}%")
        print(f"   待结算: {stats['pending_signals']}")
        
    def demo_simulate_settlement(self):
        """演示模拟结算过程"""
        print("\n⏰ 第3步：模拟信号结算过程")
        print("-" * 40)
        
        # 模拟价格变化场景
        price_scenarios = [
            {'price': 50100.0, 'description': '小幅上涨'},
            {'price': 49900.0, 'description': '小幅下跌'},
            {'price': 50600.0, 'description': '大幅上涨'},
            {'price': 49200.0, 'description': '大幅下跌'},
            {'price': 50000.0, 'description': '回到原价'}
        ]
        
        print("📈 模拟不同价格场景的结算结果:")
        
        for scenario in price_scenarios:
            print(f"\n💰 场景: {scenario['description']} (价格: {scenario['price']:.0f})")
            
            # 强制修改数据库中的到期时间，使信号立即可结算
            self._force_expire_signals()
            
            # 执行结算
            settled_signals = self.checker.check_and_settle_signals(scenario['price'])
            
            if settled_signals:
                for signal in settled_signals:
                    result_emoji = self._get_result_emoji(signal['result'])
                    direction_emoji = "🚀" if signal['direction'] == 'UP' else "📉"
                    
                    print(f"   {result_emoji} {signal['signal_id'][:15]}...")
                    print(f"      方向: {signal['direction']} {direction_emoji}")
                    print(f"      价格: {signal['signal_price']:.0f} → {signal['settlement_price']:.0f}")
                    print(f"      结果: {signal['result']}")
                    print(f"      变化: {signal['price_change']:.2f}%")
                    print(f"      置信度: {signal['confidence']:.1f}%")
                    
                print(f"   📊 本轮结算了 {len(settled_signals)} 个信号")
            else:
                print("   暂无到期信号")
            
            # 短暂停顿以模拟真实场景
            time.sleep(0.5)
            
    def demo_show_statistics(self):
        """演示显示统计结果"""
        print("\n📊 第4步：查看详细统计结果")
        print("-" * 40)
        
        # 整体统计
        stats = self.checker.get_settlement_stats(days=1)
        print(f"📈 整体统计 (今日):")
        print(f"   总结算数量: {stats['total_settled']}")
        print(f"   胜利数量: {stats['total_wins']}")
        print(f"   失败数量: {stats['total_losses']}")
        print(f"   平局数量: {stats['total_ties']}")
        print(f"   整体胜率: {stats['overall_win_rate']:.1f}%")
        print(f"   累计价格变化: {stats['total_pnl']:.2f}%")
        
        # 分方向统计
        print(f"\n📊 分方向统计:")
        
        up_stats = self.checker.get_historical_win_rates(direction='UP', lookback_days=1)
        down_stats = self.checker.get_historical_win_rates(direction='DOWN', lookback_days=1)
        
        print(f"   🚀 看涨信号:")
        print(f"      总数: {up_stats['total_signals']}")
        print(f"      胜率: {up_stats['win_rate']:.1f}%")
        print(f"      平均置信度: {up_stats['avg_confidence']:.1f}%")
        print(f"      平均变化: {up_stats['avg_pnl']:.2f}%")
        
        print(f"   📉 看跌信号:")
        print(f"      总数: {down_stats['total_signals']}")
        print(f"      胜率: {down_stats['win_rate']:.1f}%")
        print(f"      平均置信度: {down_stats['avg_confidence']:.1f}%")
        print(f"      平均变化: {down_stats['avg_pnl']:.2f}%")
        
        # 分强度统计
        print(f"\n🔥 分强度统计:")
        for strength in ['STRONG', 'MEDIUM', 'WEAK']:
            strength_stats = self.checker.get_historical_win_rates(strength=strength, lookback_days=1)
            print(f"   {strength}: {strength_stats['total_signals']}个信号, 胜率: {strength_stats['win_rate']:.1f}%")
        
    def demo_export_data(self):
        """演示数据导出功能"""
        print("\n💾 第5步：导出数据")
        print("-" * 40)
        
        export_file = self.checker.export_settlement_data()
        if export_file:
            print(f"✅ 数据已导出到: {export_file}")
            print(f"📁 包含内容:")
            print(f"   - 所有信号记录")
            print(f"   - 每日统计数据")
            print(f"   - 完整的结算历史")
            print(f"   - 可用于进一步分析")
        else:
            print("❌ 数据导出失败")
    
    def _force_expire_signals(self):
        """强制使信号到期（仅用于演示）"""
        import sqlite3
        
        conn = sqlite3.connect(self.checker.db_path)
        cursor = conn.cursor()
        
        # 将所有PENDING信号的到期时间设置为过去时间
        past_time = (datetime.now() - timedelta(minutes=1)).isoformat()
        cursor.execute(
            "UPDATE signal_records SET expiry_time = ? WHERE result = 'PENDING'",
            (past_time,)
        )
        conn.commit()
        conn.close()
    
    def _get_result_emoji(self, result):
        """根据结果获取对应的emoji"""
        if result == 'WIN':
            return '✅'
        elif result == 'LOSS':
            return '❌'
        elif result == 'TIE':
            return '⚖️'
        else:
            return '❓'


def main():
    """主演示函数"""
    demo = SignalSettlementDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()