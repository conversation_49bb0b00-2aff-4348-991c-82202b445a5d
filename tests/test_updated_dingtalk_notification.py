#!/usr/bin/env python3
"""
测试修正后的钉钉通知功能
"""
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier
from quant.strategies.event_contract_decision_engine import TradingDecision, MarketCondition, RiskLevel
from datetime import datetime

def test_updated_dingtalk_notifications():
    """测试修正后的钉钉通知功能"""
    print("📢 测试修正后的钉钉通知功能")
    print("=" * 50)
    
    # 创建通知器
    notifier = EventContractDingtalkNotifier()
    
    # 创建模拟决策
    decision = TradingDecision(
        should_trade=True,
        direction="UP",
        bet_amount=20.0,
        confidence=75.0,
        risk_level=RiskLevel.MEDIUM,
        market_condition=MarketCondition.TRENDING,
        reason="技术指标显示上涨趋势",
        timestamp=datetime.now(),
        max_loss_allowed=1000.0,
        current_daily_loss=100.0,
        position_size_ratio=0.1,
        market_score=75.0,
        signal_strength=75.0,
        entry_timing="良好"
    )
    
    print("\n1. 测试胜利结算通知（80%收益）")
    print("-" * 30)
    
    # 测试胜利结算
    win_pnl = 20.0 * 0.8  # 16.0 USDT
    success, error = notifier.send_settlement_notification(
        order_id="WIN_001",
        result="win",
        pnl=win_pnl,
        decision=decision
    )
    
    if success:
        print(f"✅ 胜利结算通知发送成功，盈利: +{win_pnl:.2f} USDT")
    else:
        print(f"❌ 胜利结算通知发送失败: {error}")
    
    print("\n2. 测试亏损结算通知（全额损失）")
    print("-" * 30)
    
    # 测试亏损结算
    loss_pnl = -20.0  # 损失全部投入
    success, error = notifier.send_settlement_notification(
        order_id="LOSS_001",
        result="loss",
        pnl=loss_pnl,
        decision=decision
    )
    
    if success:
        print(f"✅ 亏损结算通知发送成功，亏损: {loss_pnl:.2f} USDT")
    else:
        print(f"❌ 亏损结算通知发送失败: {error}")
    
    print("\n3. 测试平局结算通知（回本）")
    print("-" * 30)
    
    # 测试平局结算
    tie_pnl = 0.0  # 回本
    success, error = notifier.send_settlement_notification(
        order_id="TIE_001",
        result="tie",
        pnl=tie_pnl,
        decision=decision
    )
    
    if success:
        print(f"✅ 平局结算通知发送成功，盈亏: {tie_pnl:.2f} USDT（回本）")
    else:
        print(f"❌ 平局结算通知发送失败: {error}")
    
    print("\n4. 测试每日总结通知（包含平局统计）")
    print("-" * 30)
    
    # 模拟统计数据
    stats = {
        'total_trades': 10,
        'wins': 4,
        'losses': 5,
        'ties': 1,  # 新增平局统计
        'win_rate': 0.4,
        'total_pnl': -28.0,  # 4*16 - 5*20 + 1*0 = 64 - 100 + 0 = -36
        'avg_bet': 20.0,
        'current_streak': -2
    }
    
    risk_summary = {
        'daily_loss': 100.0,
        'daily_loss_ratio': 0.1,
        'remaining_soft_limit': 900.0
    }
    
    success, error = notifier.send_daily_summary(stats, risk_summary)
    
    if success:
        print("✅ 每日总结通知发送成功")
        print(f"   总交易: {stats['total_trades']}")
        print(f"   胜/负/平: {stats['wins']}/{stats['losses']}/{stats['ties']}")
        print(f"   胜率: {stats['win_rate']:.1%}")
        print(f"   总盈亏: {stats['total_pnl']:+.2f} USDT")
    else:
        print(f"❌ 每日总结通知发送失败: {error}")
    
    print("\n5. 盈亏规则验证")
    print("-" * 30)
    
    # 验证盈亏计算
    test_cases = [
        ("胜利", "win", 20.0 * 0.8, "获得80%收益"),
        ("亏损", "loss", -20.0, "损失全部投入"),
        ("平局", "tie", 0.0, "回本处理")
    ]
    
    for name, result, expected_pnl, description in test_cases:
        print(f"  {name}: {expected_pnl:+.2f} USDT ({description})")
    
    print("\n=" * 50)
    print("✅ 钉钉通知功能测试完成！")
    print("🎯 新增功能：")
    print("   - 平局结算通知（🤝 价格相等·回本）")
    print("   - 80%收益规则说明")
    print("   - 每日总结包含平局统计")
    print("   - 更清晰的盈亏规则说明")

if __name__ == "__main__":
    test_updated_dingtalk_notifications() 