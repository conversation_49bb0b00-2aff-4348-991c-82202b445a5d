#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复潜在信号入场点问题的解决方案
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from typing import Dict, List
from dataclasses import dataclass, field


@dataclass
class OptimizedFactorFilter:
    """优化后的因子过滤器"""
    threshold: float = 25.0  # 降低阈值从40.0到25.0
    weights: Dict[str, float] = field(default_factory=lambda: {
        "price_action": 15,  # 降低从20到15
        "volume": 20,        # 增加从15到20
        "momentum": 20,      # 增加从15到20
        "structure": 10,     # 保持不变
        "time_decay": 0,     # 特殊处理
    })
    
    # 新增: 更宽松的时间限制
    min_remaining_time: int = 300  # 从600秒降低到300秒（5分钟）
    
    def evaluate_entry(
        self,
        pending_created_at: datetime,
        now: datetime,
        minute_klines: List,
        indicators: Dict[str, float],
    ) -> Dict[str, object]:
        """优化的入场评估逻辑"""
        factors: Dict[str, float] = {}
        
        # 1. 价格行为因子 - 优化计算逻辑
        if len(minute_klines) >= 3:
            pct_changes = [
                (minute_klines[-i].close - minute_klines[-i].open) / minute_klines[-i].open * 100
                for i in range(1, 4)
            ]
            # 降低阈值从0.2%到0.1%，更容易得分
            price_action_score = sum(1 for p in pct_changes if abs(p) > 0.1) / 3 * self.weights["price_action"]
        else:
            price_action_score = 0.0
        factors["price_action"] = round(price_action_score, 2)

        # 2. 成交量因子 - 优化计算逻辑
        vols = [k.volume for k in minute_klines[-20:]] if minute_klines else []
        if vols:
            avg_vol = sum(vols) / len(vols)
            current_vol = minute_klines[-1].volume
            vol_ratio = current_vol / avg_vol if avg_vol else 1.0
            # 降低满分要求从2倍到1.5倍
            volume_score = min(vol_ratio / 1.5, 1.0) * self.weights["volume"]
        else:
            volume_score = 0.0
        factors["volume"] = round(volume_score, 2)

        # 3. 动能因子 - 优化RSI计算
        rsi = indicators.get("rsi", 50)
        # 更宽松的RSI评估，任何偏离都给分
        rsi_deviation = abs(rsi - 50)
        momentum_score = min(rsi_deviation / 30, 1.0) * self.weights["momentum"]  # 30点偏离给满分
        factors["momentum"] = round(momentum_score, 2)

        # 4. K线结构因子 - 保持原有逻辑
        if minute_klines:
            last = minute_klines[-1]
            body = abs(last.close - last.open)
            rng = last.high - last.low if last.high - last.low else 0.0001
            body_ratio = body / rng
            structure_score = min(body_ratio, 1.0) * self.weights["structure"]
        else:
            structure_score = 0.0
        factors["structure"] = round(structure_score, 2)

        # 5. 时间衰减 - 优化时间限制
        remaining = 600 - int((now - pending_created_at).total_seconds())
        if remaining < 0:
            time_decay_score = -10
        elif remaining < 180:  # 3分钟内给轻微惩罚
            time_decay_score = -2
        else:
            time_decay_score = 0
        factors["time_decay"] = time_decay_score

        # 计算总分
        total = sum(factors.values())
        # 使用更宽松的时间限制
        recommend = total >= self.threshold and remaining >= self.min_remaining_time
        reason = (
            "满足入场条件" if recommend else f"得分{total:.1f}<阈值{self.threshold}或剩余{remaining}s<{self.min_remaining_time}s"
        )

        return {
            "score": round(total, 2),
            "factors": factors,
            "remaining_time": remaining,
            "recommend_entry": recommend,
            "reason": reason,
        }


def test_optimized_filter():
    """测试优化后的过滤器"""
    print("🔧 测试优化后的FactorFilter")
    print("=" * 50)
    
    # 模拟数据
    from tests.debug_entry_signal_issue import MinuteKline
    
    base_time = datetime.now()
    base_price = 67000.0
    
    minute_klines = []
    for i in range(20):
        price_change = (i % 3 - 1) * 0.3
        open_price = base_price * (1 + price_change/100)
        close_price = open_price * (1 + (i % 5 - 2) * 0.2/100)
        high_price = max(open_price, close_price) * 1.001
        low_price = min(open_price, close_price) * 0.999
        volume = 1000 + i * 50
        
        kline = MinuteKline(
            timestamp=base_time - timedelta(minutes=20-i),
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=volume
        )
        minute_klines.append(kline)
    
    # 测试不同时间点
    optimized_filter = OptimizedFactorFilter()
    
    scenarios = [
        ("刚生成pending信号", 0),
        ("5分钟后", 300),
        ("8分钟后", 480),
        ("10分钟后", 600),
    ]
    
    for scenario_name, elapsed_seconds in scenarios:
        pending_created_at = datetime.now() - timedelta(seconds=elapsed_seconds)
        now = datetime.now()
        
        indicators = {'rsi': 35.0}  # 更明显的RSI偏离
        
        factor_eval = optimized_filter.evaluate_entry(
            pending_created_at=pending_created_at,
            now=now,
            minute_klines=minute_klines,
            indicators=indicators
        )
        
        print(f"\n📊 {scenario_name} (已过去{elapsed_seconds}秒):")
        print(f"   总得分: {factor_eval['score']:.1f}/{optimized_filter.threshold}")
        print(f"   剩余时间: {factor_eval['remaining_time']}秒")
        print(f"   是否推荐入场: {'✅' if factor_eval['recommend_entry'] else '❌'}")
        print(f"   原因: {factor_eval['reason']}")
        
        print("   详细因子得分:")
        for factor_name, score in factor_eval['factors'].items():
            weight = optimized_filter.weights.get(factor_name, 0)
            if factor_name == 'time_decay':
                print(f"     {factor_name}: {score} (特殊处理)")
            else:
                percentage = (score / weight * 100) if weight > 0 else 0
                print(f"     {factor_name}: {score:.1f}/{weight} ({percentage:.1f}%)")


def create_optimized_factor_filter_file():
    """创建优化后的FactorFilter文件"""
    
    optimized_content = '''# -*- coding: utf-8 -*-

"""
优化后的因子过滤器
解决潜在信号入场点检测问题
"""

from datetime import datetime
from typing import Dict, List
from dataclasses import dataclass, field


@dataclass
class MinuteKline:
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float


@dataclass
class OptimizedFactorFilter:
    """优化后的因子过滤器 - 更容易触发入场信号"""
    threshold: float = 25.0  # 从40.0降低到25.0
    weights: Dict[str, float] = field(default_factory=lambda: {
        "price_action": 15,  # 从20降低到15
        "volume": 20,        # 从15增加到20
        "momentum": 20,      # 从15增加到20
        "structure": 10,     # 保持不变
        "time_decay": 0,     # 特殊处理
    })
    
    # 更宽松的时间限制
    min_remaining_time: int = 300  # 从600秒降低到300秒（5分钟）

    def evaluate_entry(
        self,
        pending_created_at: datetime,
        now: datetime,
        minute_klines: List[MinuteKline],
        indicators: Dict[str, float],
    ) -> Dict[str, object]:
        """评估是否可入场（优化版）"""
        factors: Dict[str, float] = {}
        
        # 1. 价格行为因子 - 降低门槛
        if len(minute_klines) >= 3:
            pct_changes = [
                (minute_klines[-i].close - minute_klines[-i].open) / minute_klines[-i].open * 100
                for i in range(1, 4)
            ]
            # 降低阈值从0.2%到0.1%
            price_action_score = sum(1 for p in pct_changes if abs(p) > 0.1) / 3 * self.weights["price_action"]
        else:
            price_action_score = 0.0
        factors["price_action"] = round(price_action_score, 2)

        # 2. 成交量因子 - 降低满分要求
        vols = [k.volume for k in minute_klines[-20:]] if minute_klines else []
        if vols:
            avg_vol = sum(vols) / len(vols)
            current_vol = minute_klines[-1].volume
            vol_ratio = current_vol / avg_vol if avg_vol else 1.0
            # 降低满分要求从2倍到1.5倍
            volume_score = min(vol_ratio / 1.5, 1.0) * self.weights["volume"]
        else:
            volume_score = 0.0
        factors["volume"] = round(volume_score, 2)

        # 3. 动能因子 - 更宽松的RSI评估
        rsi = indicators.get("rsi", 50)
        rsi_deviation = abs(rsi - 50)
        # 30点偏离给满分，而不是50点
        momentum_score = min(rsi_deviation / 30, 1.0) * self.weights["momentum"]
        factors["momentum"] = round(momentum_score, 2)

        # 4. K线结构因子 - 保持原有逻辑
        if minute_klines:
            last = minute_klines[-1]
            body = abs(last.close - last.open)
            rng = last.high - last.low if last.high - last.low else 0.0001
            body_ratio = body / rng
            structure_score = min(body_ratio, 1.0) * self.weights["structure"]
        else:
            structure_score = 0.0
        factors["structure"] = round(structure_score, 2)

        # 5. 时间衰减 - 优化时间限制
        remaining = 600 - int((now - pending_created_at).total_seconds())
        if remaining < 0:
            time_decay_score = -10
        elif remaining < 180:  # 3分钟内轻微惩罚
            time_decay_score = -2
        else:
            time_decay_score = 0
        factors["time_decay"] = time_decay_score

        # 计算总分
        total = sum(factors.values())
        # 使用更宽松的时间限制
        recommend = total >= self.threshold and remaining >= self.min_remaining_time
        reason = (
            "满足入场条件" if recommend else f"得分{total:.1f}<阈值{self.threshold}或剩余{remaining}s<{self.min_remaining_time}s"
        )

        return {
            "score": round(total, 2),
            "factors": factors,
            "remaining_time": remaining,
            "recommend_entry": recommend,
            "reason": reason,
        }


# 为了兼容性，创建一个别名
FactorFilter = OptimizedFactorFilter
'''
    
    with open("quant/strategies/optimized_factor_filter.py", "w", encoding="utf-8") as f:
        f.write(optimized_content)
    
    print("✅ 已创建优化后的FactorFilter文件: quant/strategies/optimized_factor_filter.py")


if __name__ == "__main__":
    test_optimized_filter()
    print("\n" + "="*50)
    create_optimized_factor_filter_file()