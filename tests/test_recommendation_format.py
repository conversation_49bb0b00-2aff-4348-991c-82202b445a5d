"""单元测试：RecommendationEngine.make_recommendation 格式
"""

from datetime import datetime, timedelta

from quant.strategies.recommendation_engine import RecommendationEngine
from types import SimpleNamespace


class DummySignal:
    """简化版 SignalResult 供测试"""

    def __init__(self, direction: str = "UP", confidence: float = 80.0, has_signal: bool = True):
        self.direction = direction
        self.confidence = confidence
        self.has_signal = has_signal


def test_recommendation_no_entry():
    engine = RecommendationEngine()
    signal = DummySignal()
    factor_eval = {
        "score": 30,
        "factors": {},
        "remaining_time": 500,
        "recommend_entry": False,
        "reason": "得分不足",
    }
    rec = engine.make_recommendation(signal, factor_eval)
    assert rec["has_recommendation"] is False
    assert rec["stake"] == 0.0


def test_recommendation_success():
    engine = RecommendationEngine(base_stake=20)
    signal = DummySignal()
    factor_eval = {
        "score": 45,
        "factors": {"price_action": 20},
        "remaining_time": 650,
        "recommend_entry": True,
        "reason": "满足入场条件",
    }
    rec = engine.make_recommendation(signal, factor_eval, bet_multiplier=1.5)
    assert rec["has_recommendation"] is True
    assert rec["direction"] == "UP"
    assert abs(rec["stake"] - 30.0) < 1e-6  # 20*1.5 