#!/usr/bin/env python3
"""
验证主策略修复的快速测试
模拟主策略的关键部分，确保不再出现kline_manager错误
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.utils import logger


def simulate_signal_generator_usage():
    """模拟信号生成器的使用"""
    logger.info("🧪 模拟信号生成器使用...")
    
    try:
        from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple
        
        # 创建信号生成器
        signal_generator = EventContractSignalGeneratorSimple()
        
        # 模拟添加一些K线数据
        import time
        current_time = int(time.time() * 1000)
        
        for i in range(25):  # 添加25根K线
            signal_generator.add_kline_data(
                timestamp=current_time + i * 60000,  # 每分钟一根
                open_price=43500.0 + i,
                high_price=43510.0 + i,
                low_price=43490.0 + i,
                close_price=43500.0 + i,
                volume=100.0
            )
        
        logger.info(f"✅ 添加了25根K线数据")
        
        # 验证数据结构
        assert hasattr(signal_generator, 'klines'), "缺少klines属性"
        assert '1m' in signal_generator.klines, "缺少1m K线数据"
        
        klines_1m = list(signal_generator.klines['1m'])
        logger.info(f"✅ 1m K线数据: {len(klines_1m)}根")
        
        return signal_generator
        
    except Exception as e:
        logger.error(f"❌ 信号生成器使用失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def simulate_evaluate_pending_signal(signal_generator):
    """模拟_evaluate_pending_signal方法的关键部分"""
    logger.info("🧪 模拟_evaluate_pending_signal方法...")
    
    try:
        # 这是修复后的代码逻辑
        klines_1m_all = list(signal_generator.klines['1m'])
        klines_1m = klines_1m_all[-20:] if len(klines_1m_all) >= 20 else klines_1m_all
        
        logger.info(f"✅ 获取1m K线: 总共{len(klines_1m_all)}根，使用{len(klines_1m)}根")
        
        # 模拟转换为MinuteKline对象
        from quant.strategies.factor_filter import MinuteKline
        
        minute_klines = [MinuteKline(
            timestamp=k.timestamp,
            open=k.open,
            high=k.high,
            low=k.low,
            close=k.close,
            volume=k.volume,
        ) for k in klines_1m]
        
        logger.info(f"✅ 转换为MinuteKline对象: {len(minute_klines)}个")
        
        # 验证数据内容
        if minute_klines:
            first_kline = minute_klines[0]
            logger.info(f"✅ 第一根K线: 时间戳={first_kline.timestamp}, 收盘价={first_kline.close}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 模拟_evaluate_pending_signal失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def simulate_factor_filter_usage(signal_generator):
    """模拟FactorFilter的使用"""
    logger.info("🧪 模拟FactorFilter使用...")
    
    try:
        from quant.strategies.factor_filter import FactorFilter, MinuteKline
        
        # 创建FactorFilter实例
        factor_filter = FactorFilter()
        
        # 获取1m K线数据
        klines_1m_all = list(signal_generator.klines['1m'])
        klines_1m = klines_1m_all[-20:] if len(klines_1m_all) >= 20 else klines_1m_all
        
        # 转换为MinuteKline
        minute_klines = [MinuteKline(
            timestamp=k.timestamp,
            open=k.open,
            high=k.high,
            low=k.low,
            close=k.close,
            volume=k.volume,
        ) for k in klines_1m]
        
        # 模拟时间参数
        pending_created_at = datetime.now() - timedelta(minutes=5)
        now = datetime.now()
        indicators = {'rsi': 50}
        
        # 调用evaluate_entry方法
        factor_eval = factor_filter.evaluate_entry(
            pending_created_at, now, minute_klines, indicators
        )
        
        logger.info(f"✅ FactorFilter评估完成: {factor_eval}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ FactorFilter使用失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主验证函数"""
    logger.info("🚀 开始主策略修复验证")
    logger.info("=" * 60)
    
    # 1. 模拟信号生成器使用
    signal_generator = simulate_signal_generator_usage()
    if not signal_generator:
        logger.error("❌ 信号生成器创建失败")
        return False
    
    # 2. 模拟_evaluate_pending_signal方法
    if not simulate_evaluate_pending_signal(signal_generator):
        logger.error("❌ _evaluate_pending_signal模拟失败")
        return False
    
    # 3. 模拟FactorFilter使用
    if not simulate_factor_filter_usage(signal_generator):
        logger.error("❌ FactorFilter使用失败")
        return False
    
    # 成功完成所有验证
    logger.info("\\n🎉 所有验证都通过了！")
    logger.info("\\n📊 验证结果:")
    logger.info("   ✅ 信号生成器正常工作")
    logger.info("   ✅ K线数据访问正常")
    logger.info("   ✅ 不再出现kline_manager错误")
    logger.info("   ✅ MinuteKline转换正常")
    logger.info("   ✅ FactorFilter集成正常")
    
    logger.info("\\n🔧 修复总结:")
    logger.info("   - 原错误: 'EventContractSignalGeneratorSimple' object has no attribute 'kline_manager'")
    logger.info("   - 修复方案: 改为直接访问 signal_generator.klines['1m']")
    logger.info("   - 添加保护: 检查数据长度，防止索引错误")
    logger.info("   - 测试结果: 所有功能正常，不再出现错误")
    
    return True


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\\n🎉 主策略修复验证完全成功！")
        print("\\n✅ 系统现在应该可以正常运行，不再出现kline_manager错误")
    else:
        print("\\n❌ 主策略修复验证失败，请检查实现")