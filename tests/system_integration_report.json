{"summary": {"total_tests": 10, "passed_tests": 10, "failed_tests": 0, "success_rate": 1.0, "test_time": "2025-07-12T11:11:53.492288"}, "test_results": [{"test_name": "环境设置和依赖检查", "success": true, "details": "环境准备完成，配置文件存在: True", "timestamp": "2025-07-12 11:11:47.867844"}, {"test_name": "组件初始化测试", "success": true, "details": "组件初始化状态: {'api_client': True, 'signal_generator': True, 'decision_engine': True, 'dingtalk_notifier': True, 'settlement_checker': True, 'trade_history_manager': True}", "timestamp": "2025-07-12 11:11:48.976531"}, {"test_name": "信号生成流程测试", "success": true, "details": "信号生成成功: {'should_trade': False, 'direction': 'UP', 'confidence': 0.2, 'current_price': 50000, 'indicators': {'rsi': 65.5, 'macd': 0.8, 'bollinger_upper': 51000, 'bollinger_lower': 49000, 'sma_20': 49900}}", "timestamp": "2025-07-12 11:11:49.477187"}, {"test_name": "决策引擎流程测试", "success": true, "details": "决策生成成功: {'should_execute': True, 'amount': 14.0, 'risk_level': 'MEDIUM', 'reason': '信号强度适中，风险可控'}", "timestamp": "2025-07-12 11:11:49.978496"}, {"test_name": "交易执行流程测试", "success": true, "details": "交易执行成功: {'success': True, 'order_id': 'test_order_1752289910.479824', 'symbol': 'BTCUSDT', 'side': 'UP', 'amount': 14.0, 'price': 50000, 'timestamp': datetime.datetime(2025, 7, 12, 11, 11, 50, 479884)}", "timestamp": "2025-07-12 11:11:50.479925"}, {"test_name": "通知系统测试", "success": true, "details": "通知发送成功: 4/4", "timestamp": "2025-07-12 11:11:50.980616"}, {"test_name": "结算检查测试", "success": true, "details": "结算完成: 总盈亏=-4.00, 胜率=50.0%", "timestamp": "2025-07-12 11:11:51.481791"}, {"test_name": "风险管理测试", "success": true, "details": "风险管理测试: [{'scenario': '正常交易', 'passed': True, 'actual_action': 'CONTINUE', 'expected_action': 'CONTINUE'}, {'scenario': '软限制触发', 'passed': True, 'actual_action': 'WARNING', 'expected_action': 'WARNING'}, {'scenario': '硬限制触发', 'passed': True, 'actual_action': 'STOP', 'expected_action': 'STOP'}]", "timestamp": "2025-07-12 11:11:51.983256"}, {"test_name": "数据持久化测试", "success": true, "details": "数据持久化: 记录数=3, 总盈亏=12.0, 胜率=66.7%", "timestamp": "2025-07-12 11:11:52.488621"}, {"test_name": "性能监控测试", "success": true, "details": "性能监控: {'signal_generation_time': 0.05, 'decision_making_time': 0.03, 'order_execution_time': 0.2, 'notification_time': 0.1, 'total_response_time': 0.38}", "timestamp": "2025-07-12 11:11:52.989992"}], "test_data": {"btc_price": 50000, "test_trades": [{"signal_time": "2025-07-12 11:11:50.479887", "symbol": "BTCUSDT", "direction": "UP", "amount": 14.0, "entry_price": 50000, "order_id": "test_order_1752289910.479824", "status": "PLACED"}], "notifications_sent": [{"type": "startup", "content": "🚀 系统启动通知测试", "sent": true}, {"type": "trade_signal", "content": "🚀 交易信号生成 - 测试交易小火箭", "sent": true}, {"type": "settlement", "content": "📊 合约结算通知测试", "sent": true}, {"type": "risk_warning", "content": "⚠️ 风险提醒测试", "sent": true}], "settlements_checked": [{"contract_id": "test_1", "result": "WIN", "pnl": 16.0, "final_price": 50100}, {"contract_id": "test_2", "result": "LOSS", "pnl": -20, "final_price": 50100}]}}