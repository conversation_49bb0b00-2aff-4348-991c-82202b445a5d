#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试市场质量过滤功能

验证信号生成器是否能正确识别和过滤低质量的市场条件
"""

import random
from datetime import datetime
from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple


def generate_low_volatility_data(base_price, duration_minutes):
    """生成低波动性数据（振幅小于0.5%）"""
    klines_data = []
    current_price = base_price
    start_time = int(datetime.now().timestamp() * 1000)
    
    for i in range(duration_minutes):
        timestamp = start_time + i * 60 * 1000
        
        # 生成极小振幅的K线（0.1%-0.3%）
        small_change = (random.random() - 0.5) * 0.006  # ±0.3%
        open_price = current_price
        close_price = open_price * (1 + small_change)
        
        # 高低价也保持很小的范围
        high_price = max(open_price, close_price) * (1 + random.random() * 0.001)
        low_price = min(open_price, close_price) * (1 - random.random() * 0.001)
        
        volume = 1000 + random.random() * 500  # 正常成交量
        
        klines_data.append({
            'timestamp': timestamp,
            'open_price': open_price,
            'high_price': high_price,
            'low_price': low_price,
            'close_price': close_price,
            'volume': volume
        })
        
        current_price = close_price
    
    return klines_data


def generate_high_volatility_data(base_price, duration_minutes):
    """生成高波动性数据（有明显趋势和机会）"""
    klines_data = []
    current_price = base_price
    start_time = int(datetime.now().timestamp() * 1000)
    
    for i in range(duration_minutes):
        timestamp = start_time + i * 60 * 1000
        
        # 生成较大振幅的K线
        if i < duration_minutes // 2:
            # 前半段：下跌趋势
            trend_change = -0.008  # 每分钟下跌0.8%
        else:
            # 后半段：上涨趋势
            trend_change = 0.012   # 每分钟上涨1.2%
        
        random_change = (random.random() - 0.5) * 0.004  # ±0.2%随机波动
        total_change = trend_change + random_change
        
        open_price = current_price
        close_price = open_price * (1 + total_change)
        
        # 生成更大的高低价范围
        if close_price > open_price:  # 阳线
            high_price = close_price * (1 + random.random() * 0.008)
            low_price = open_price * (1 - random.random() * 0.003)
        else:  # 阴线
            high_price = open_price * (1 + random.random() * 0.003)
            low_price = close_price * (1 - random.random() * 0.008)
        
        # 成交量在趋势转换时放大
        if i == duration_minutes // 2:
            volume = 3000 + random.random() * 2000  # 转折点成交量放大
        else:
            volume = 1200 + random.random() * 800
        
        klines_data.append({
            'timestamp': timestamp,
            'open_price': open_price,
            'high_price': high_price,
            'low_price': low_price,
            'close_price': close_price,
            'volume': volume
        })
        
        current_price = close_price
    
    return klines_data


def test_market_quality_filtering():
    """测试市场质量过滤功能"""
    
    print("🧪 开始测试市场质量过滤功能")
    print("=" * 70)
    
    # 测试用例1：低波动性市场（应被过滤）
    print("\n📊 测试用例1: 低波动性市场（振幅<0.5%）")
    print("-" * 50)
    
    generator1 = EventContractSignalGeneratorSimple(
        signal_threshold=70.0,
        min_timeframe_consensus=1,
        confidence_threshold=60.0
    )
    
    # 生成低波动性数据
    low_vol_data = generate_low_volatility_data(95000, 180)  # 增加到180分钟
    
    print(f"📈 生成{len(low_vol_data)}分钟低波动性数据...")
    for data in low_vol_data:
        generator1.add_kline_data(**data)
    
    # 检查最新K线的振幅
    latest_kline = generator1.klines['15m'][-1] if generator1.klines['15m'] else None
    if latest_kline:
        amplitude = abs(latest_kline.close - latest_kline.open) / latest_kline.open * 100
        high_low_range = (latest_kline.high - latest_kline.low) / latest_kline.low * 100
        print(f"📊 最新15分K线振幅: {amplitude:.3f}% (实体)")
        print(f"📊 最新15分K线范围: {high_low_range:.3f}% (高低点)")
    
    # 生成信号（应该被过滤）
    signal1 = generator1.generate_signal()
    
    print(f"\n✅ 测试结果: {'符合预期 - 被正确过滤' if not signal1.has_signal else '❌ 异常 - 应该被过滤但没有'}")
    
    # 测试用例2：高波动性市场（应通过过滤）
    print("\n" + "=" * 70)
    print("\n📊 测试用例2: 高波动性市场（有明显趋势）")
    print("-" * 50)
    
    generator2 = EventContractSignalGeneratorSimple(
        signal_threshold=70.0,
        min_timeframe_consensus=1,
        confidence_threshold=60.0
    )
    
    # 生成高波动性数据
    high_vol_data = generate_high_volatility_data(95000, 180)  # 增加到180分钟
    
    print(f"📈 生成{len(high_vol_data)}分钟高波动性数据...")
    for data in high_vol_data:
        generator2.add_kline_data(**data)
    
    # 检查最新K线的振幅
    latest_kline = generator2.klines['15m'][-1] if generator2.klines['15m'] else None
    if latest_kline:
        amplitude = abs(latest_kline.close - latest_kline.open) / latest_kline.open * 100
        high_low_range = (latest_kline.high - latest_kline.low) / latest_kline.low * 100
        print(f"📊 最新15分K线振幅: {amplitude:.3f}% (实体)")
        print(f"📊 最新15分K线范围: {high_low_range:.3f}% (高低点)")
    
    # 生成信号（应该通过过滤）
    signal2 = generator2.generate_signal()
    
    print(f"\n✅ 测试结果: {'符合预期 - 通过质量检查' if signal2.has_signal or 'market_quality' in (signal2.timeframe_analysis or {}) else '❌ 可能异常'}")
    
    # 测试用例3：混合市场条件
    print("\n" + "=" * 70)
    print("\n📊 测试用例3: 混合市场条件测试")
    print("-" * 50)
    
    # 创建包含不同质量K线的数据
    mixed_data = []
    base_price = 95000
    start_time = int(datetime.now().timestamp() * 1000)
    
    # 前120分钟：正常波动
    for i in range(120):
        timestamp = start_time + i * 60 * 1000
        change = (random.random() - 0.5) * 0.02  # ±1%
        
        open_price = base_price
        close_price = open_price * (1 + change)
        high_price = max(open_price, close_price) * (1 + random.random() * 0.005)
        low_price = min(open_price, close_price) * (1 - random.random() * 0.005)
        volume = 1000 + random.random() * 1000
        
        mixed_data.append({
            'timestamp': timestamp,
            'open_price': open_price,
            'high_price': high_price,
            'low_price': low_price,
            'close_price': close_price,
            'volume': volume
        })
        base_price = close_price
    
    # 后60分钟：低波动整理
    for i in range(120, 180):
        timestamp = start_time + i * 60 * 1000
        small_change = (random.random() - 0.5) * 0.004  # ±0.2%
        
        open_price = base_price
        close_price = open_price * (1 + small_change)
        high_price = max(open_price, close_price) * (1 + random.random() * 0.001)
        low_price = min(open_price, close_price) * (1 - random.random() * 0.001)
        volume = 800 + random.random() * 400  # 成交量萎缩
        
        mixed_data.append({
            'timestamp': timestamp,
            'open_price': open_price,
            'high_price': high_price,
            'low_price': low_price,
            'close_price': close_price,
            'volume': volume
        })
        base_price = close_price
    
    generator3 = EventContractSignalGeneratorSimple(
        signal_threshold=70.0,
        min_timeframe_consensus=1,
        confidence_threshold=60.0
    )
    
    print(f"📈 生成{len(mixed_data)}分钟混合质量数据...")
    for data in mixed_data:
        generator3.add_kline_data(**data)
    
    # 生成信号
    signal3 = generator3.generate_signal()
    
    print(f"\n✅ 混合市场测试完成")
    
    # 总结测试结果
    print("\n" + "=" * 70)
    print("📋 测试总结:")
    print(f"   🧪 测试用例1 (低波动): {'✅ 正确过滤' if not signal1.has_signal else '❌ 过滤失败'}")
    print(f"   🧪 测试用例2 (高波动): {'✅ 通过检查' if signal2.has_signal or 'market_quality' in (signal2.timeframe_analysis or {}) else '❌ 检查异常'}")
    print(f"   🧪 测试用例3 (混合条件): {'✅ 处理正常' if signal3 else '❌ 处理异常'}")
    
    print("\n🎉 市场质量过滤功能测试完成!")
    print("💡 系统现在能够智能识别并过滤不适合交易的市场条件")
    
    return signal1, signal2, signal3


if __name__ == "__main__":
    try:
        test_market_quality_filtering()
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()