#!/usr/bin/env python3
"""
测试钉钉消息格式修复效果
"""
import sys
sys.path.insert(0, '/Users/<USER>/PycharmProjects/mitchquant1')

from datetime import datetime
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier
from quant.strategies.event_contract_signal_generator_simple import SignalResult

def test_pending_signal_format():
    """测试潜在信号消息格式"""
    
    # 创建钉钉通知器
    notifier = EventContractDingtalkNotifier()
    
    # 创建测试信号结果
    signal_result = SignalResult(
        direction="UP",
        confidence=75.3,
        technical_score=82.5,
        risk_level="MEDIUM",
        signal_id="signal_1752673911999_9329",
        signal_price=118353.62,
        user_reminder="检测到强势信号！建议关注交易机会",
        market_status="强势突破"
    )
    
    # 创建测试市场数据
    market_data = {
        'kline_sequence': 88,
        'signal_count': 10,
        'kline_time': '21:51',
        'signal_id': 'signal_1752673911999_9329',
        'signal_price': 118353.62
    }
    
    # 构建消息
    message = notifier._build_pending_signal_message(signal_result, market_data)
    
    print("=== 修复后的潜在信号消息格式 ===")
    print(message)
    print("\n" + "="*50 + "\n")
    
    return message

def test_recommendation_format():
    """测试推荐消息格式"""
    
    # 创建钉钉通知器
    notifier = EventContractDingtalkNotifier()
    
    # 创建测试推荐数据
    recommendation = {
        "direction": "UP",
        "stake": 20,
        "confidence": 73.0,
        "score": 100.2,
        "remaining_time": 300,
        "generated_at": "2025-07-16 21:51:52",
        "signal_result": SignalResult(
            direction="UP",
            confidence=73.0,
            technical_score=82.5,
            risk_level="MEDIUM",
            signal_id="signal_1752673911999_9329",
            signal_price=118353.62
        )
    }
    
    # 构建消息
    message = notifier._build_recommendation_message(recommendation)
    
    print("=== 修复后的推荐消息格式 ===")
    print(message)
    print("\n" + "="*50 + "\n")
    
    return message

def main():
    """主函数"""
    print("开始测试钉钉消息格式修复...")
    
    try:
        # 测试潜在信号格式
        pending_message = test_pending_signal_format()
        
        # 测试推荐格式
        recommendation_message = test_recommendation_format()
        
        print("✅ 格式修复测试完成！")
        print("现在消息格式应该更加清晰和易读。")
        
        # 检查是否还有格式问题
        if "\\\\n" in pending_message or "\\\\n" in recommendation_message:
            print("⚠️ 警告：仍然存在格式问题！")
        else:
            print("✅ 格式问题已解决！")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()