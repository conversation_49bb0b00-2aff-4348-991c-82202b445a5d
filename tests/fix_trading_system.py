#!/usr/bin/env python3
"""
交易系统修复脚本
自动应用修复方案解决潜在信号通知、择时信号通知和结算时间异常问题
"""

import asyncio
import sys
import os
import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier
from quant.strategies.signal_settlement_checker import SignalSettlementChecker
from quant.config import config
from quant.utils import logger


class TradingSystemFixer:
    """交易系统修复器"""
    
    def __init__(self):
        self.fixes_applied = []
        self.errors = []
        
    def fix_dingtalk_notification_limits(self):
        """修复钉钉通知限制问题"""
        print("🔧 修复钉钉通知限制问题...")
        
        try:
            # 检查是否有钉钉通知器实例需要重置
            # 这里我们创建一个新的实例并重置计数器
            notifier = EventContractDingtalkNotifier()
            
            # 重置通知计数器
            notifier.daily_notification_count = 0
            notifier.last_notification_time = None
            notifier._last_reset_date = datetime.now().date()
            
            print("✅ 钉钉通知计数器已重置")
            print(f"   - 每日通知限制: {notifier.config.max_daily_notifications}")
            print(f"   - 最小间隔: {notifier.config.min_signal_interval}秒")
            
            self.fixes_applied.append("钉钉通知限制重置")
            return True
            
        except Exception as e:
            error_msg = f"重置钉钉通知限制失败: {e}"
            print(f"❌ {error_msg}")
            self.errors.append(error_msg)
            return False
    
    def fix_signal_settlement_database(self):
        """修复信号结算数据库问题"""
        print("🔧 修复信号结算数据库问题...")
        
        try:
            db_path = "./data/signal_settlement.db"
            
            # 确保数据目录存在
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            # 检查数据库
            checker = SignalSettlementChecker(db_path)
            
            # 获取当前状态
            pending_count = checker.get_pending_signals_count()
            stats = checker.get_settlement_stats(days=1)
            
            print(f"✅ 信号结算数据库状态:")
            print(f"   - 待结算信号: {pending_count}")
            print(f"   - 今日结算: {stats['today_stats']['settled']}")
            print(f"   - 数据库路径: {db_path}")
            
            # 清理超时的待结算信号（超过15分钟的）
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查找超时的信号
            timeout_threshold = datetime.now() - timedelta(minutes=15)
            cursor.execute('''
                SELECT COUNT(*) FROM signal_records 
                WHERE result = 'PENDING' 
                AND datetime(timestamp) < ?
            ''', (timeout_threshold.isoformat(),))
            
            timeout_count = cursor.fetchone()[0]
            
            if timeout_count > 0:
                print(f"⚠️ 发现 {timeout_count} 个超时的待结算信号，正在清理...")
                
                # 将超时信号标记为过期
                cursor.execute('''
                    UPDATE signal_records 
                    SET result = 'EXPIRED', 
                        settlement_time = CURRENT_TIMESTAMP,
                        pnl = 0.0
                    WHERE result = 'PENDING' 
                    AND datetime(timestamp) < ?
                ''', (timeout_threshold.isoformat(),))
                
                conn.commit()
                print(f"✅ 已清理 {timeout_count} 个超时信号")
            
            conn.close()
            
            self.fixes_applied.append("信号结算数据库清理")
            return True
            
        except Exception as e:
            error_msg = f"修复信号结算数据库失败: {e}"
            print(f"❌ {error_msg}")
            self.errors.append(error_msg)
            return False
    
    def fix_trade_history_database(self):
        """修复交易历史数据库问题"""
        print("🔧 修复交易历史数据库问题...")
        
        try:
            db_path = "./data/trade_history.db"
            
            # 确保数据目录存在
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            # 检查数据库文件
            if os.path.exists(db_path):
                print(f"✅ 交易历史数据库存在: {db_path}")
                
                # 检查数据库内容
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 获取表信息
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                print(f"📊 数据库表: {[table[0] for table in tables]}")
                
                # 检查记录数量
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"   - {table_name}: {count} 条记录")
                
                conn.close()
                
            else:
                print(f"⚠️ 交易历史数据库不存在，将在首次使用时创建")
            
            self.fixes_applied.append("交易历史数据库检查")
            return True
            
        except Exception as e:
            error_msg = f"修复交易历史数据库失败: {e}"
            print(f"❌ {error_msg}")
            self.errors.append(error_msg)
            return False
    
    def fix_config_file(self):
        """修复配置文件问题"""
        print("🔧 检查配置文件问题...")
        
        try:
            config_path = "config.json"
            
            if not os.path.exists(config_path):
                print(f"❌ 配置文件不存在: {config_path}")
                return False
            
            # 加载配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 检查关键配置
            required_keys = ['PLATFORMS', 'DINGTALK']
            missing_keys = []
            
            for key in required_keys:
                if key not in config_data:
                    missing_keys.append(key)
            
            if missing_keys:
                print(f"❌ 配置文件缺少必要字段: {missing_keys}")
                return False
            
            # 检查币安配置
            if 'binance' not in config_data['PLATFORMS']:
                print("❌ 缺少币安交易所配置")
                return False
            
            binance_config = config_data['PLATFORMS']['binance']
            if not binance_config.get('access_key') or not binance_config.get('secret_key'):
                print("❌ 币安API密钥配置不完整")
                return False
            
            # 检查钉钉配置
            if not config_data.get('DINGTALK'):
                print("❌ 钉钉配置缺失")
                return False
            
            print("✅ 配置文件检查通过")
            print(f"   - 币安配置: 存在")
            print(f"   - 钉钉配置: 存在")
            
            self.fixes_applied.append("配置文件检查")
            return True
            
        except Exception as e:
            error_msg = f"检查配置文件失败: {e}"
            print(f"❌ {error_msg}")
            self.errors.append(error_msg)
            return False
    
    def fix_log_directory(self):
        """修复日志目录问题"""
        print("🔧 修复日志目录问题...")
        
        try:
            log_dir = "logs"
            
            # 确保日志目录存在
            os.makedirs(log_dir, exist_ok=True)
            
            # 检查日志文件
            log_files = list(Path(log_dir).glob("*.log"))
            
            if log_files:
                print(f"✅ 找到 {len(log_files)} 个日志文件")
                
                # 显示最新的日志文件
                latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
                print(f"   - 最新日志: {latest_log}")
                
                # 检查日志文件大小
                log_size = latest_log.stat().st_size
                if log_size > 10 * 1024 * 1024:  # 10MB
                    print(f"⚠️ 日志文件过大: {log_size / 1024 / 1024:.2f}MB")
                    
                    # 建议轮转日志
                    backup_name = f"{latest_log}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    latest_log.rename(backup_name)
                    print(f"✅ 日志文件已备份: {backup_name}")
                    
            else:
                print("⚠️ 没有找到日志文件，可能是日志配置问题")
                
                # 创建一个测试日志文件
                test_log = Path(log_dir) / "test.log"
                test_log.write_text(f"测试日志创建于: {datetime.now()}\n")
                print(f"✅ 创建测试日志文件: {test_log}")
            
            self.fixes_applied.append("日志目录修复")
            return True
            
        except Exception as e:
            error_msg = f"修复日志目录失败: {e}"
            print(f"❌ {error_msg}")
            self.errors.append(error_msg)
            return False
    
    def cleanup_old_data(self):
        """清理旧数据"""
        print("🔧 清理旧数据...")
        
        try:
            # 清理旧的导出文件
            exports_dir = Path("exports")
            if exports_dir.exists():
                old_exports = list(exports_dir.glob("*.json"))
                old_exports.extend(list(exports_dir.glob("*.csv")))
                
                # 保留最近30天的文件
                cutoff_date = datetime.now() - timedelta(days=30)
                cleaned_count = 0
                
                for export_file in old_exports:
                    file_time = datetime.fromtimestamp(export_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        export_file.unlink()
                        cleaned_count += 1
                
                if cleaned_count > 0:
                    print(f"✅ 清理了 {cleaned_count} 个旧导出文件")
                else:
                    print("✅ 没有需要清理的旧导出文件")
            
            # 清理旧的临时文件
            temp_files = list(Path(".").glob("*.tmp"))
            temp_files.extend(list(Path(".").glob("*.temp")))
            
            for temp_file in temp_files:
                temp_file.unlink()
                
            if temp_files:
                print(f"✅ 清理了 {len(temp_files)} 个临时文件")
            
            self.fixes_applied.append("旧数据清理")
            return True
            
        except Exception as e:
            error_msg = f"清理旧数据失败: {e}"
            print(f"❌ {error_msg}")
            self.errors.append(error_msg)
            return False
    
    async def test_system_components(self):
        """测试系统组件"""
        print("🧪 测试系统组件...")
        
        try:
            # 测试配置加载
            config.loads("config.json")
            print("✅ 配置加载成功")
            
            # 测试钉钉通知器
            notifier = EventContractDingtalkNotifier()
            
            # 发送测试消息
            test_message = (
                f"🔧 **系统修复测试** 🔧\n\n"
                f"📅 修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"🎯 修复内容:\n"
            )
            
            for fix in self.fixes_applied:
                test_message += f"   ✅ {fix}\n"
            
            if self.errors:
                test_message += f"\n⚠️ 遇到的问题:\n"
                for error in self.errors:
                    test_message += f"   ❌ {error}\n"
            
            test_message += f"\n🚀 系统修复完成，准备恢复正常运行！"
            
            success, error = await notifier.send_message(test_message)
            
            if success:
                print("✅ 系统组件测试通过")
                return True
            else:
                print(f"❌ 钉钉通知测试失败: {error}")
                return False
                
        except Exception as e:
            error_msg = f"测试系统组件失败: {e}"
            print(f"❌ {error_msg}")
            self.errors.append(error_msg)
            return False
    
    def generate_fix_report(self):
        """生成修复报告"""
        print("\n" + "="*60)
        print("📋 修复报告")
        print("="*60)
        
        if self.fixes_applied:
            print("✅ 已应用的修复:")
            for i, fix in enumerate(self.fixes_applied, 1):
                print(f"   {i}. {fix}")
        
        if self.errors:
            print("\n⚠️ 遇到的问题:")
            for i, error in enumerate(self.errors, 1):
                print(f"   {i}. {error}")
        
        # 生成修复报告文件
        report_data = {
            "fix_time": datetime.now().isoformat(),
            "fixes_applied": self.fixes_applied,
            "errors": self.errors,
            "success_rate": len(self.fixes_applied) / (len(self.fixes_applied) + len(self.errors)) if (self.fixes_applied or self.errors) else 0
        }
        
        report_file = f"reports/fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 修复报告已保存: {report_file}")
        
        return report_data


async def main():
    """主修复函数"""
    print("🚀 开始交易系统修复")
    print("="*60)
    
    fixer = TradingSystemFixer()
    
    # 执行各项修复
    fixes = [
        fixer.fix_config_file,
        fixer.fix_log_directory,
        fixer.fix_dingtalk_notification_limits,
        fixer.fix_signal_settlement_database,
        fixer.fix_trade_history_database,
        fixer.cleanup_old_data,
    ]
    
    for fix_func in fixes:
        fix_func()
        print()  # 空行分隔
    
    # 测试系统组件
    await fixer.test_system_components()
    
    # 生成修复报告
    report = fixer.generate_fix_report()
    
    print("\n" + "="*60)
    print("🔚 修复完成")
    print("="*60)
    
    if report["success_rate"] > 0.8:
        print("✅ 修复成功率高，系统应该可以正常运行")
        print("\n🚀 建议下一步操作:")
        print("1. 重新启动交易系统")
        print("2. 运行调试脚本验证修复效果")
        print("3. 监控系统运行状态")
    else:
        print("⚠️ 修复成功率较低，建议手动检查问题")
        print("\n🔧 建议下一步操作:")
        print("1. 检查错误日志")
        print("2. 手动修复剩余问题")
        print("3. 联系技术支持")


if __name__ == "__main__":
    asyncio.run(main())