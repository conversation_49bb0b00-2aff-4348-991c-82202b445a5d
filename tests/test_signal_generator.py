#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
事件合约信号生成器单元测试

验证K线定义、技术指标计算和信号生成逻辑的正确性

Author: HertelQuant Enhanced
Date: 2025-01-31
"""

import unittest
import sys
import os
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_signal_generator import (
    KlineData,
    TechnicalIndicators,
    SignalResult,
    EventContractSignalGenerator,
    KlineDataManager,
    TechnicalIndicatorCalculator,
    ProbabilityAnalyzer
)


class TestKlineData(unittest.TestCase):
    """测试K线数据结构"""
    
    def test_bullish_kline_definition(self):
        """测试阳线定义：收盘价 > 开盘价"""
        # 创建阳线：收盘价 > 开盘价
        bullish_kline = KlineData(
            timestamp=1640995200000,  # 2022-01-01 00:00:00
            open=100.0,
            high=105.0,
            low=99.0,
            close=103.0,  # 收盘价 > 开盘价
            volume=1000.0
        )
        
        self.assertTrue(bullish_kline.is_bullish, "阳线判断错误：收盘价103 > 开盘价100应该是阳线")
        self.assertFalse(bullish_kline.is_bearish, "阳线不应该被判断为阴线")
    
    def test_bearish_kline_definition(self):
        """测试阴线定义：收盘价 < 开盘价"""
        # 创建阴线：收盘价 < 开盘价
        bearish_kline = KlineData(
            timestamp=1640995200000,
            open=100.0,
            high=101.0,
            low=95.0,
            close=97.0,  # 收盘价 < 开盘价
            volume=1000.0
        )
        
        self.assertTrue(bearish_kline.is_bearish, "阴线判断错误：收盘价97 < 开盘价100应该是阴线")
        self.assertFalse(bearish_kline.is_bullish, "阴线不应该被判断为阳线")
    
    def test_doji_kline_definition(self):
        """测试十字线定义：收盘价 = 开盘价"""
        # 创建十字线：收盘价 = 开盘价
        doji_kline = KlineData(
            timestamp=1640995200000,
            open=100.0,
            high=102.0,
            low=98.0,
            close=100.0,  # 收盘价 = 开盘价
            volume=1000.0
        )
        
        self.assertFalse(doji_kline.is_bullish, "十字线不应该被判断为阳线")
        self.assertFalse(doji_kline.is_bearish, "十字线不应该被判断为阴线")
    
    def test_kline_properties(self):
        """测试K线属性计算"""
        kline = KlineData(
            timestamp=1640995200000,
            open=100.0,
            high=105.0,
            low=95.0,
            close=103.0,
            volume=1000.0
        )
        
        # 测试实体大小
        expected_body_size = abs(103.0 - 100.0)  # |收盘价 - 开盘价|
        self.assertEqual(kline.body_size, expected_body_size, "K线实体大小计算错误")
        
        # 测试上影线长度
        expected_upper_shadow = 105.0 - max(100.0, 103.0)  # 最高价 - max(开盘价, 收盘价)
        self.assertEqual(kline.upper_shadow, expected_upper_shadow, "上影线长度计算错误")
        
        # 测试下影线长度
        expected_lower_shadow = min(100.0, 103.0) - 95.0  # min(开盘价, 收盘价) - 最低价
        self.assertEqual(kline.lower_shadow, expected_lower_shadow, "下影线长度计算错误")


class TestKlineDataManager(unittest.TestCase):
    """测试K线数据管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.manager = KlineDataManager(max_periods=100)
    
    def test_add_minute_kline(self):
        """测试添加1分钟K线"""
        kline = KlineData(
            timestamp=1640995200000,
            open=100.0,
            high=105.0,
            low=95.0,
            close=103.0,
            volume=1000.0
        )
        
        self.manager.add_minute_kline(kline)
        
        # 检查1分钟K线是否正确添加
        minute_klines = self.manager.get_klines('1m')
        self.assertEqual(len(minute_klines), 1, "1分钟K线未正确添加")
        self.assertEqual(minute_klines[0].close, 103.0, "K线数据不匹配")
    
    def test_timeframe_aggregation(self):
        """测试时间周期聚合"""
        # 添加15根1分钟K线，应该能聚合成1根15分钟K线
        base_timestamp = 1640995200000  # 2022-01-01 00:00:00
        
        for i in range(15):
            kline = KlineData(
                timestamp=base_timestamp + i * 60 * 1000,  # 每分钟递增
                open=100.0 + i * 0.1,
                high=105.0 + i * 0.1,
                low=95.0 + i * 0.1,
                close=102.0 + i * 0.1,
                volume=100.0
            )
            self.manager.add_minute_kline(kline)
        
        # 检查1分钟K线数量
        minute_klines = self.manager.get_klines('1m')
        self.assertEqual(len(minute_klines), 15, "1分钟K线数量不正确")
        
        # 注意：时间周期聚合需要特定的时间点才会触发
        # 这里主要测试数据结构是否正确


class TestTechnicalIndicatorCalculator(unittest.TestCase):
    """测试技术指标计算器"""
    
    def setUp(self):
        """设置测试环境"""
        self.calculator = TechnicalIndicatorCalculator()
    
    def create_sample_klines(self, count: int = 100) -> list:
        """创建示例K线数据"""
        klines = []
        base_price = 100.0
        base_timestamp = 1640995200000
        
        for i in range(count):
            # 生成有趋势的价格数据
            price_trend = i * 0.1  # 轻微上涨趋势
            noise = np.sin(i * 0.1) * 2  # 添加一些波动
            
            close_price = base_price + price_trend + noise
            open_price = close_price - np.random.uniform(-1, 1)
            high_price = max(open_price, close_price) + np.random.uniform(0, 2)
            low_price = min(open_price, close_price) - np.random.uniform(0, 2)
            
            kline = KlineData(
                timestamp=base_timestamp + i * 60 * 1000,
                open=open_price,
                high=high_price,
                low=low_price,
                close=close_price,
                volume=1000 + np.random.uniform(0, 500)
            )
            klines.append(kline)
        
        return klines
    
    def test_calculate_indicators_with_sufficient_data(self):
        """测试有足够数据时的指标计算"""
        klines = self.create_sample_klines(100)
        indicators = self.calculator.calculate_indicators(klines)
        
        # 检查指标是否正确计算
        self.assertIsInstance(indicators, TechnicalIndicators, "返回的不是TechnicalIndicators实例")
        self.assertGreater(indicators.sma_5, 0, "SMA5应该大于0")
        self.assertGreater(indicators.ema_10, 0, "EMA10应该大于0")
        self.assertTrue(0 <= indicators.rsi_14 <= 100, f"RSI应该在0-100之间，实际值: {indicators.rsi_14}")
        self.assertTrue(0 <= indicators.bb_position <= 1, f"布林带位置应该在0-1之间，实际值: {indicators.bb_position}")
        
    def test_calculate_indicators_insufficient_data(self):
        """测试数据不足时的指标计算"""
        klines = self.create_sample_klines(10)  # 数据不足
        indicators = self.calculator.calculate_indicators(klines)
        
        # 应该返回默认值
        self.assertEqual(indicators.rsi_14, 50.0, "数据不足时RSI应该返回默认值50")


class TestProbabilityAnalyzer(unittest.TestCase):
    """测试概率分析器"""
    
    def setUp(self):
        """设置测试环境"""
        self.analyzer = ProbabilityAnalyzer(lookback_periods=50)
        self.calculator = TechnicalIndicatorCalculator()
    
    def create_trending_klines(self, count: int, trend: str) -> list:
        """创建有明确趋势的K线数据"""
        klines = []
        base_price = 100.0
        base_timestamp = 1640995200000
        
        for i in range(count):
            if trend == "bullish":
                # 创建上涨趋势
                close_price = base_price + i * 0.5 + np.random.uniform(0, 0.2)
                open_price = close_price - np.random.uniform(0.1, 0.3)  # 多数是阳线
            elif trend == "bearish":
                # 创建下跌趋势
                close_price = base_price - i * 0.5 + np.random.uniform(-0.2, 0)
                open_price = close_price + np.random.uniform(0.1, 0.3)  # 多数是阴线
            else:
                # 横盘整理
                close_price = base_price + np.random.uniform(-2, 2)
                open_price = close_price + np.random.uniform(-0.5, 0.5)
            
            high_price = max(open_price, close_price) + np.random.uniform(0, 1)
            low_price = min(open_price, close_price) - np.random.uniform(0, 1)
            
            kline = KlineData(
                timestamp=base_timestamp + i * 60 * 1000,
                open=open_price,
                high=high_price,
                low=low_price,
                close=close_price,
                volume=1000 + np.random.uniform(0, 500)
            )
            klines.append(kline)
        
        return klines
    
    def test_probability_analysis_bullish_trend(self):
        """测试上涨趋势中的概率分析"""
        klines = self.create_trending_klines(100, "bullish")
        indicators = self.calculator.calculate_indicators(klines)
        
        bullish_prob, bearish_prob = self.analyzer.analyze_next_candle_probability(klines, indicators)
        
        # 在上涨趋势中，看涨概率应该更高
        self.assertGreater(bullish_prob, 40, f"上涨趋势中看涨概率应该较高，实际: {bullish_prob:.1f}%")
        self.assertAlmostEqual(bullish_prob + bearish_prob, 100.0, places=1, 
                              msg="看涨和看跌概率之和应该等于100%")
    
    def test_probability_analysis_bearish_trend(self):
        """测试下跌趋势中的概率分析"""
        klines = self.create_trending_klines(100, "bearish")
        indicators = self.calculator.calculate_indicators(klines)
        
        bullish_prob, bearish_prob = self.analyzer.analyze_next_candle_probability(klines, indicators)
        
        # 在下跌趋势中，看跌概率应该更高
        self.assertGreater(bearish_prob, 40, f"下跌趋势中看跌概率应该较高，实际: {bearish_prob:.1f}%")
        self.assertAlmostEqual(bullish_prob + bearish_prob, 100.0, places=1,
                              msg="看涨和看跌概率之和应该等于100%")


class TestEventContractSignalGenerator(unittest.TestCase):
    """测试事件合约信号生成器"""
    
    def setUp(self):
        """设置测试环境"""
        self.generator = EventContractSignalGenerator(
            signal_threshold=90.0,
            min_timeframe_consensus=2,
            confidence_threshold=80.0
        )
    
    def add_sample_data(self, count: int = 200):
        """添加示例数据"""
        base_price = 95000.0
        base_timestamp = int((datetime.now() - timedelta(hours=4)).timestamp() * 1000)
        
        for i in range(count):
            # 生成有趋势的数据
            trend = i * 0.5  # 轻微上涨趋势
            noise = np.sin(i * 0.05) * 10  # 添加波动
            
            close_price = base_price + trend + noise
            open_price = close_price - np.random.uniform(-5, 5)
            high_price = max(open_price, close_price) + np.random.uniform(0, 8)
            low_price = min(open_price, close_price) - np.random.uniform(0, 8)
            volume = 100 + np.random.uniform(0, 200)
            
            self.generator.add_kline_data(
                timestamp=base_timestamp + i * 60 * 1000,
                open_price=open_price,
                high_price=high_price,
                low_price=low_price,
                close_price=close_price,
                volume=volume
            )
    
    def test_signal_generation_with_sufficient_data(self):
        """测试有足够数据时的信号生成"""
        self.add_sample_data(200)
        
        signal = self.generator.generate_signal()
        
        # 检查信号结果结构
        self.assertIsInstance(signal, SignalResult, "返回的不是SignalResult实例")
        self.assertIn(signal.direction, ["UP", "DOWN", "NEUTRAL"], "信号方向无效")
        self.assertTrue(0 <= signal.confidence <= 100, f"置信度应该在0-100之间，实际: {signal.confidence}")
        self.assertTrue(0 <= signal.bullish_probability <= 100, f"看涨概率应该在0-100之间")
        self.assertTrue(0 <= signal.bearish_probability <= 100, f"看跌概率应该在0-100之间")
        self.assertAlmostEqual(signal.bullish_probability + signal.bearish_probability, 100.0, places=1,
                              msg="看涨和看跌概率之和应该等于100%")
    
    def test_signal_generation_insufficient_data(self):
        """测试数据不足时的信号生成"""
        # 只添加少量数据
        self.add_sample_data(5)
        
        signal = self.generator.generate_signal()
        
        # 数据不足时应该没有信号
        self.assertFalse(signal.has_signal, "数据不足时不应该生成信号")
        self.assertEqual(signal.direction, "NEUTRAL", "数据不足时方向应该是NEUTRAL")
    
    def test_signal_threshold_logic(self):
        """测试信号阈值逻辑"""
        # 创建一个低阈值的生成器用于测试
        low_threshold_generator = EventContractSignalGenerator(
            signal_threshold=60.0,  # 较低的阈值
            min_timeframe_consensus=1,
            confidence_threshold=50.0
        )
        
        # 添加数据
        base_price = 95000.0
        base_timestamp = int(datetime.now().timestamp() * 1000)
        
        for i in range(100):
            # 创建明显的上涨趋势
            close_price = base_price + i * 2  # 每分钟上涨2美元
            open_price = close_price - 1.5  # 大部分是阳线
            high_price = close_price + 0.5
            low_price = open_price - 0.5
            
            low_threshold_generator.add_kline_data(
                timestamp=base_timestamp + i * 60 * 1000,
                open_price=open_price,
                high_price=high_price,
                low_price=low_price,
                close_price=close_price,
                volume=100
            )
        
        signal = low_threshold_generator.generate_signal()
        
        # 在明显上涨趋势中，应该更容易生成UP信号
        if signal.has_signal:
            self.assertEqual(signal.direction, "UP", "明显上涨趋势中应该生成UP信号")
    
    def test_kline_direction_accuracy(self):
        """测试K线方向判断的准确性"""
        # 添加明确的阳线和阴线数据
        base_timestamp = int(datetime.now().timestamp() * 1000)
        
        test_cases = [
            # (开盘价, 收盘价, 预期是否为阳线)
            (100.0, 105.0, True),   # 阳线
            (100.0, 95.0, False),   # 阴线
            (100.0, 108.0, True),   # 阳线
            (100.0, 92.0, False),   # 阴线
            (100.0, 100.0, False),  # 十字线（既不是阳线也不是阴线）
        ]
        
        for i, (open_price, close_price, expected_bullish) in enumerate(test_cases):
            high_price = max(open_price, close_price) + 2
            low_price = min(open_price, close_price) - 2
            
            self.generator.add_kline_data(
                timestamp=base_timestamp + i * 60 * 1000,
                open_price=open_price,
                high_price=high_price,
                low_price=low_price,
                close_price=close_price,
                volume=100
            )
            
            # 获取最新添加的K线
            latest_kline = self.generator.kline_manager.get_latest_kline('1m')
            
            if latest_kline:
                self.assertEqual(latest_kline.is_bullish, expected_bullish,
                               f"K线方向判断错误: 开盘价{open_price}, 收盘价{close_price}, "
                               f"预期阳线:{expected_bullish}, 实际阳线:{latest_kline.is_bullish}")


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行事件合约信号生成器单元测试")
    print("="*80)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestKlineData,
        TestKlineDataManager,
        TestTechnicalIndicatorCalculator,
        TestProbabilityAnalyzer,
        TestEventContractSignalGenerator
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 显示测试结果
    print("\n" + "="*80)
    print("🧪 测试结果总结:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback.split(chr(10))[-2]}")
    
    if result.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback.split(chr(10))[-2]}")
    
    if result.wasSuccessful():
        print(f"\n✅ 所有测试通过！K线定义和信号生成逻辑正确。")
    else:
        print(f"\n❌ 有测试失败，请检查代码实现。")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)