#!/usr/bin/env python3
"""
简单测试钉钉消息格式
"""
import sys
sys.path.insert(0, '/Users/<USER>/PycharmProjects/mitchquant1')

from datetime import datetime

class MockSignalResult:
    def __init__(self):
        self.direction = "UP"
        self.confidence = 75.3
        self.technical_score = 82.5
        self.risk_level = "MEDIUM"
        self.signal_id = "signal_1752673911999_9329"
        self.signal_price = 118353.62
        self.user_reminder = "检测到强势信号！建议关注交易机会"
        self.market_status = "强势突破"

def build_fixed_pending_signal_message(signal_result, market_data=None):
    """构建修复后的pending信号消息"""
    # 根据方向选择图标
    direction_icon = "🚀" if signal_result.direction == "UP" else "📉"
    
    # 获取准确的K线序号信息
    kline_sequence = market_data.get('kline_sequence', 0) if market_data else 0
    signal_count = market_data.get('signal_count', 0) if market_data else 0
    kline_time = market_data.get('kline_time', '未知') if market_data else '未知'
    
    # 获取信号ID和价格信息
    signal_id = market_data.get('signal_id', '未分配') if market_data else '未分配'
    signal_price = market_data.get('signal_price', 0.0) if market_data else 0.0
    
    # 构建清晰的消息格式
    message = f"🔔 **潜在信号检测** {direction_icon}\n\n"
    
    # 信号ID和价格信息
    message += f"🆔 信号ID: {signal_id}\n"
    message += f"💰 信号价格: {signal_price:.2f} USDT\n\n"
    
    # K线和信号序号信息
    message += f"📊 K线序号: 第{kline_sequence}根15分钟K线 ({kline_time})\n"
    message += f"🔢 信号序号: 今日第{signal_count}个信号\n\n"
    
    # 交易信息
    message += f"📈 交易方向: {signal_result.direction} {direction_icon}\n"
    message += f"🎯 置信度: {signal_result.confidence:.1f}%\n"
    message += f"📊 技术分: {signal_result.technical_score:.1f}分\n"
    message += f"⚠️ 风险等级: {signal_result.risk_level}\n\n"
    
    # 市场条件
    market_status = getattr(signal_result, 'market_status', None)
    if market_status and market_status != "":
        message += f"🌟 市场条件: {market_status}\n"
    else:
        # 根据置信度推断市场条件
        if signal_result.confidence >= 70:
            market_condition = "强势信号"
        elif signal_result.confidence >= 60:
            market_condition = "中等信号"
        elif signal_result.confidence >= 50:
            market_condition = "弱势信号"
        else:
            market_condition = "信号待确认"
        message += f"🌟 市场条件: {market_condition}\n"
    
    # 决策原因
    reason = f"满足交易条件: 信心度{signal_result.confidence:.1f}%, 风险{signal_result.risk_level}"
    message += f"💡 决策原因: {reason}\n\n"
    
    # 市场提醒
    if hasattr(signal_result, 'user_reminder') and signal_result.user_reminder:
        message += f"🔍 市场提醒: 【交易信号提醒】📈 检测到信号！建议关注交易机会\n\n"
    
    # 时间戳
    message += f"⏰ 信号时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    
    # K线进度信息
    message += f"📈 今日进度: {kline_sequence}/96 (15分钟K线)\n\n"
    
    # 等待提醒
    message += f"⏳ 等待入场时机评估... 系统将在接下来的15分钟内寻找最佳入场点\n\n"
    
    # 跟踪提醒
    message += f"🏷️ 跟踪提醒: 请记住信号ID [{signal_id}], 用于结算通知对应\n\n"
    
    # 小火箭祝福
    message += f"🎉 祝**交易**顺利！{direction_icon}小火箭{direction_icon}起飞！"
    
    return message

def main():
    """主函数"""
    print("=== 钉钉消息格式修复测试 ===\n")
    
    # 创建测试信号结果
    signal_result = MockSignalResult()
    
    # 创建测试市场数据
    market_data = {
        'kline_sequence': 88,
        'signal_count': 10,
        'kline_time': '21:51',
        'signal_id': 'signal_1752673911999_9329',
        'signal_price': 118353.62
    }
    
    # 构建修复后的消息
    message = build_fixed_pending_signal_message(signal_result, market_data)
    
    print("=== 修复后的消息格式 ===")
    print(message)
    print("\n" + "="*50)
    
    # 检查是否还有格式问题
    if "\\\\n" in message:
        print("⚠️ 警告：仍然存在格式问题！")
    else:
        print("✅ 格式问题已解决！")
    
    print("\n=== 消息长度和结构检查 ===")
    print(f"消息总长度: {len(message)} 字符")
    print(f"包含换行符数量: {message.count(chr(10))}")
    
    # 检查关键信息是否正确显示
    key_info = [
        "signal_1752673911999_9329",
        "118353.62",
        "第88根15分钟K线",
        "今日第10个信号",
        "75.3%",
        "小火箭"
    ]
    
    missing_info = []
    for info in key_info:
        if info not in message:
            missing_info.append(info)
    
    if missing_info:
        print(f"⚠️ 缺少关键信息: {missing_info}")
    else:
        print("✅ 所有关键信息都已正确显示！")

if __name__ == "__main__":
    main()