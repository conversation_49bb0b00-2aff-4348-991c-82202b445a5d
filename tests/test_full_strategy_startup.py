#!/usr/bin/env python3
"""
测试完整的策略启动过程
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.event_contract_main_strategy import EventContractMainStrategy


async def test_full_startup():
    """测试完整的策略启动过程"""
    print("🚀 开始测试完整的策略启动过程...")
    
    # 创建策略实例
    strategy = EventContractMainStrategy()
    
    # 手动调用初始化历史数据
    print("📊 手动初始化历史数据...")
    await strategy._initialize_historical_data()
    
    # 检查数据状态
    print("\n🔍 检查数据状态:")
    klines_1m = list(strategy.signal_generator.klines['1m'])
    klines_5m = list(strategy.signal_generator.klines['5m'])
    klines_15m = list(strategy.signal_generator.klines['15m'])
    
    print(f"  1分钟K线: {len(klines_1m)} 根")
    print(f"  5分钟K线: {len(klines_5m)} 根")
    print(f"  15分钟K线: {len(klines_15m)} 根")
    
    # 生成信号测试
    print("\n🎯 测试信号生成...")
    signal = strategy.signal_generator.generate_signal()
    print(f"  信号结果: {signal.has_signal}")
    print(f"  方向: {signal.direction}")
    print(f"  置信度: {signal.confidence:.2f}%")
    print(f"  市场状态: {signal.market_status}")
    
    # 如果有信号，显示更多详细信息
    if signal.has_signal:
        print(f"  看涨概率: {signal.bullish_probability:.2f}%")
        print(f"  看跌概率: {signal.bearish_probability:.2f}%")
        print(f"  支持时间周期: {signal.supporting_timeframes}")
        print(f"  技术评分: {signal.technical_score:.2f}")
        print(f"  风险等级: {signal.risk_level}")
    
    # 显示最新价格
    if len(klines_1m) > 0:
        latest_kline = klines_1m[-1]
        print(f"  最新价格: {latest_kline.close:.2f} USDT")
        print(f"  最新成交量: {latest_kline.volume:.2f}")
    
    print("\n✅ 策略启动测试完成！")


if __name__ == "__main__":
    asyncio.run(test_full_startup()) 