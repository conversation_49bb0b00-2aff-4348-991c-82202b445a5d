#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
潜在信号入场点调试工具
用于分析为什么潜在信号没有转化为最佳入场点信号
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from typing import Dict, List
from dataclasses import dataclass
from quant.strategies.factor_filter import FactorFilter
from quant.strategies.recommendation_engine import RecommendationEngine
from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple


@dataclass
class MinuteKline:
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float


class SignalResult:
    def __init__(self, direction: str, confidence: float, has_signal: bool = True):
        self.direction = direction
        self.confidence = confidence
        self.has_signal = has_signal


def debug_entry_signal_evaluation():
    """调试入场信号评估过程"""
    
    print("🔍 潜在信号入场点调试工具")
    print("=" * 50)
    
    # 1. 创建FactorFilter实例
    factor_filter = FactorFilter()
    print(f"📊 当前阈值设置: {factor_filter.threshold}")
    print(f"📊 权重设置: {factor_filter.weights}")
    
    # 计算最大可能得分
    max_possible_score = sum(factor_filter.weights.values())
    print(f"📊 最大可能得分: {max_possible_score}")
    print(f"📊 需要达到: {factor_filter.threshold}/{max_possible_score} = {factor_filter.threshold/max_possible_score:.1%}")
    
    # 2. 模拟一些测试数据
    print("\n🧪 模拟测试数据:")
    
    # 创建模拟的1分钟K线数据
    base_time = datetime.now()
    base_price = 67000.0
    
    minute_klines = []
    for i in range(20):
        # 模拟一些价格波动
        price_change = (i % 3 - 1) * 0.3  # -0.3%, 0%, +0.3%
        open_price = base_price * (1 + price_change/100)
        close_price = open_price * (1 + (i % 5 - 2) * 0.2/100)  # 小幅波动
        high_price = max(open_price, close_price) * 1.001
        low_price = min(open_price, close_price) * 0.999
        volume = 1000 + i * 50  # 递增的成交量
        
        kline = MinuteKline(
            timestamp=base_time - timedelta(minutes=20-i),
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=volume
        )
        minute_klines.append(kline)
    
    print(f"   生成了{len(minute_klines)}根1分钟K线数据")
    
    # 3. 测试不同的时间场景
    scenarios = [
        ("刚生成pending信号", 0),
        ("5分钟后", 300),
        ("8分钟后", 480),
        ("10分钟后", 600),
        ("12分钟后", 720),
        ("15分钟后", 900),
    ]
    
    print("\n📈 不同时间场景测试:")
    print("-" * 80)
    
    for scenario_name, elapsed_seconds in scenarios:
        pending_created_at = datetime.now() - timedelta(seconds=elapsed_seconds)
        now = datetime.now()
        
        # 模拟indicators
        indicators = {
            'rsi': 45.0,  # 略微偏离50，应该有一定得分
        }
        
        # 评估入场条件
        factor_eval = factor_filter.evaluate_entry(
            pending_created_at=pending_created_at,
            now=now,
            minute_klines=minute_klines,
            indicators=indicators
        )
        
        print(f"\n📊 {scenario_name} (已过去{elapsed_seconds}秒):")
        print(f"   总得分: {factor_eval['score']:.1f}/{factor_filter.threshold}")
        print(f"   剩余时间: {factor_eval['remaining_time']}秒")
        print(f"   是否推荐入场: {'✅' if factor_eval['recommend_entry'] else '❌'}")
        print(f"   原因: {factor_eval['reason']}")
        
        # 详细因子分析
        print("   详细因子得分:")
        for factor_name, score in factor_eval['factors'].items():
            weight = factor_filter.weights.get(factor_name, 0)
            if factor_name == 'time_decay':
                print(f"     {factor_name}: {score} (特殊处理)")
            else:
                percentage = (score / weight * 100) if weight > 0 else 0
                print(f"     {factor_name}: {score:.1f}/{weight} ({percentage:.1f}%)")
    
    # 4. 建议的解决方案
    print("\n💡 建议的解决方案:")
    print("=" * 50)
    
    print("1. 降低阈值:")
    print("   - 当前阈值40.0可能过高")
    print("   - 建议降低到25-30之间")
    
    print("\n2. 调整权重分配:")
    print("   - 增加volume权重到20")
    print("   - 降低price_action权重到15")
    
    print("\n3. 优化时间限制:")
    print("   - 当前要求剩余时间≥600秒(10分钟)")
    print("   - 可以考虑降低到300秒(5分钟)")
    
    print("\n4. 改进因子计算:")
    print("   - price_action: 当前只看涨跌幅>0.2%")
    print("   - volume: 当前要求2倍平均成交量才满分")
    print("   - momentum: RSI偏离50的程度")
    print("   - structure: K线实体比例")
    
    # 5. 测试推荐的参数
    print("\n🧪 测试推荐参数:")
    print("-" * 50)
    
    # 创建调整后的FactorFilter
    adjusted_filter = FactorFilter(
        threshold=25.0,  # 降低阈值
        weights={
            "price_action": 15,  # 降低
            "volume": 20,        # 增加
            "momentum": 15,      # 保持
            "structure": 10,     # 保持
            "time_decay": 0,     # 特殊处理
        }
    )
    
    # 使用8分钟后的场景测试
    pending_created_at = datetime.now() - timedelta(seconds=480)
    now = datetime.now()
    
    factor_eval = adjusted_filter.evaluate_entry(
        pending_created_at=pending_created_at,
        now=now,
        minute_klines=minute_klines,
        indicators={'rsi': 45.0}
    )
    
    print(f"调整后的评估结果:")
    print(f"   总得分: {factor_eval['score']:.1f}/{adjusted_filter.threshold}")
    print(f"   剩余时间: {factor_eval['remaining_time']}秒")
    print(f"   是否推荐入场: {'✅' if factor_eval['recommend_entry'] else '❌'}")
    print(f"   原因: {factor_eval['reason']}")


if __name__ == "__main__":
    debug_entry_signal_evaluation()