#!/usr/bin/env python3
"""
简化的信号统计测试
验证信号统计逻辑是否正确
"""
from datetime import datetime, timedelta
from typing import Dict, List, Any


class MockDingtalkNotifier:
    """模拟钉钉通知器"""
    def __init__(self):
        self.notification_history = []
    
    def get_notification_stats(self):
        return {}


class MockDailyKlineTracker:
    """模拟K线跟踪器"""
    def __init__(self):
        self.kline_15m_count = 80
        

def get_daily_signal_history_fixed(notifier: MockDingtalkNotifier, daily_kline_tracker: MockDailyKlineTracker) -> Dict[str, Any]:
    """修复后的信号历史获取方法"""
    try:
        # 获取钉钉通知器的通知历史
        notification_stats = notifier.get_notification_stats()
        
        # 构建今日信号历史
        today_signals = []
        
        # 🔧 修复：统计两阶段流程的所有信号类型
        for notification in notifier.notification_history:
            if (notification['type'] in ['pending_signal', 'recommendation', 'trading_signal'] and 
                notification['timestamp'].date() == datetime.now().date()):
                today_signals.append(notification)
        
        # 按时间排序（即K线序号排序）
        today_signals.sort(key=lambda x: x['timestamp'])
        
        # 构建返回数据
        signal_history = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'total_signals': len(today_signals),
            'signals': [],
            'kline_tracker': daily_kline_tracker.__dict__.copy(),
            'summary': {
                'up_signals': 0,
                'down_signals': 0,
                'pending_signals': 0,
                'recommendation_signals': 0,
                'high_confidence': 0,
                'medium_confidence': 0,
                'low_confidence': 0
            }
        }
        
        # 处理每个信号
        for i, signal in enumerate(today_signals, 1):
            signal_info = {
                'sequence': i,
                'timestamp': signal['timestamp'].strftime('%H:%M:%S'),
                'direction': signal['content'],
                'kline_time': signal['timestamp'].strftime('%H:%M'),
                'signal_type': signal['type'],
                # 计算15分钟K线序号
                'kline_sequence': (signal['timestamp'].hour * 4) + (signal['timestamp'].minute // 15) + 1
            }
            
            signal_history['signals'].append(signal_info)
            
            # 统计汇总
            if 'UP' in signal['content']:
                signal_history['summary']['up_signals'] += 1
            elif 'DOWN' in signal['content']:
                signal_history['summary']['down_signals'] += 1
            
            # 统计信号类型
            if signal['type'] == 'pending_signal':
                signal_history['summary']['pending_signals'] += 1
            elif signal['type'] == 'recommendation':
                signal_history['summary']['recommendation_signals'] += 1
        
        return signal_history
        
    except Exception as e:
        return {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'total_signals': 0,
            'signals': [],
            'error': str(e)
        }


def test_signal_statistics():
    """测试信号统计功能"""
    print("🔍 测试信号统计功能...")
    
    # 创建模拟对象
    notifier = MockDingtalkNotifier()
    tracker = MockDailyKlineTracker()
    
    # 模拟添加不同类型的通知历史
    now = datetime.now()
    
    # 添加一些测试通知记录
    notifier.notification_history = [
        {
            'timestamp': now - timedelta(hours=2),
            'type': 'pending_signal',
            'content': 'DOWN 方向信号'
        },
        {
            'timestamp': now - timedelta(hours=1, minutes=30),
            'type': 'recommendation',
            'content': 'UP 推荐信号'
        },
        {
            'timestamp': now - timedelta(hours=1),
            'type': 'pending_signal',
            'content': 'UP 方向信号'
        },
        {
            'timestamp': now - timedelta(minutes=30),
            'type': 'recommendation',
            'content': 'DOWN 推荐信号'
        },
        {
            'timestamp': now - timedelta(minutes=15),
            'type': 'pending_signal',
            'content': 'UP 方向信号'
        }
    ]
    
    # 获取信号历史
    signal_history = get_daily_signal_history_fixed(notifier, tracker)
    
    print(f"📊 信号统计结果:")
    print(f"总信号数: {signal_history['total_signals']}")
    print(f"潜在信号: {signal_history['summary']['pending_signals']}")
    print(f"推荐信号: {signal_history['summary']['recommendation_signals']}")
    print(f"看涨信号: {signal_history['summary']['up_signals']}")
    print(f"看跌信号: {signal_history['summary']['down_signals']}")
    
    # 验证结果
    assert signal_history['total_signals'] == 5, f"总信号数应该为5，实际为{signal_history['total_signals']}"
    assert signal_history['summary']['pending_signals'] == 3, f"潜在信号应该为3，实际为{signal_history['summary']['pending_signals']}"
    assert signal_history['summary']['recommendation_signals'] == 2, f"推荐信号应该为2，实际为{signal_history['summary']['recommendation_signals']}"
    assert signal_history['summary']['up_signals'] == 3, f"看涨信号应该为3，实际为{signal_history['summary']['up_signals']}"
    assert signal_history['summary']['down_signals'] == 2, f"看跌信号应该为2，实际为{signal_history['summary']['down_signals']}"
    
    print("✅ 信号统计测试通过！")
    
    # 计算信号触发率
    pending_count = signal_history['summary']['pending_signals']
    recommendation_count = signal_history['summary']['recommendation_signals']
    signal_trigger_rate = (recommendation_count / pending_count) if pending_count > 0 else 0.0
    
    print(f"📊 信号触发率: {signal_trigger_rate:.1%}")
    print(f"💡 这意味着有 {recommendation_count}/{pending_count} 个潜在信号转化为实际推荐")
    
    return signal_history


def test_hourly_report_format():
    """测试小时报告数据格式"""
    print("\n🔍 测试小时报告数据格式...")
    
    signal_history = test_signal_statistics()
    
    # 模拟小时报告数据
    pending_count = signal_history['summary']['pending_signals']
    recommendation_count = signal_history['summary']['recommendation_signals']
    signal_trigger_rate = (recommendation_count / pending_count) if pending_count > 0 else 0.0
    
    # 构建小时报告内容
    report_data = {
        'time': datetime.now().strftime('%H:%M'),
        'total_signals': signal_history['total_signals'],
        'pending_signals': pending_count,
        'recommendation_signals': recommendation_count,
        'up_signals': signal_history['summary']['up_signals'],
        'down_signals': signal_history['summary']['down_signals'],
        'signal_trigger_rate': signal_trigger_rate,
        'current_kline': 80
    }
    
    print(f"📊 小时报告数据:")
    for key, value in report_data.items():
        if key == 'signal_trigger_rate':
            print(f"  {key}: {value:.1%}")
        else:
            print(f"  {key}: {value}")
    
    print("✅ 小时报告数据格式测试通过！")
    
    return report_data


def main():
    """主测试函数"""
    print("🚀 开始测试信号统计修复...")
    
    test_signal_statistics()
    test_hourly_report_format()
    
    print("\n✅ 所有测试完成！")
    print("\n💡 修复摘要:")
    print("1. ✅ 修复了信号类型统计（包含pending_signal和recommendation）")
    print("2. ✅ 修复了信号方向统计（UP/DOWN）")
    print("3. ✅ 添加了信号触发率计算")
    print("4. ✅ 完善了小时报告数据格式")


if __name__ == "__main__":
    main()