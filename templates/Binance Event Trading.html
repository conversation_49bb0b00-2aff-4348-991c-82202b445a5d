<!DOCTYPE html>
<!-- saved from url=(0012)about:srcdoc -->
<html lang="zh-CN"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>币安事件合约交易机器人</title>
    <script src="https://cdn.tailwindcss.com/"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://unpkg.com/technicalindicators@3.1.0/dist/browser.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #1f2937 0%, #1e3a8a 50%, #1f2937 100%);
            min-height: 100vh;
            color: white;
        }
        
        .card {
            background: rgba(31, 41, 55, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(55, 65, 81, 0.5);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid;
        }
        
        .badge-up {
            background: rgba(34, 197, 94, 0.2);
            color: rgb(134, 239, 172);
            border-color: rgba(34, 197, 94, 0.3);
        }
        
        .badge-down {
            background: rgba(239, 68, 68, 0.2);
            color: rgb(252, 165, 165);
            border-color: rgba(239, 68, 68, 0.3);
        }
        
        .badge-hold {
            background: rgba(107, 114, 128, 0.5);
            color: rgb(229, 231, 235);
            border-color: rgba(107, 114, 128, 0.5);
        }
        
        .badge-win {
            background: rgb(59, 130, 246);
            color: white;
        }
        
        .badge-loss {
            background: rgb(239, 68, 68);
            color: white;
        }
        
        .badge-tie {
            background: rgb(107, 114, 128);
            color: white;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid rgba(55, 65, 81, 0.5);
        }
        
        .table th {
            background: rgba(31, 41, 55, 0.8);
            font-weight: 600;
        }
        
        .text-positive {
            color: rgb(147, 197, 253);
        }
        
        .text-negative {
            color: rgb(248, 113, 113);
        }
        
        .countdown {
            font-family: 'Courier New', monospace;
            font-size: 2.5rem;
            font-weight: bold;
        }
        
        .signal-badge {
            font-size: 1.5rem;
            padding: 0.75rem 2rem;
        }
        
        .price-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: rgb(251, 191, 36);
        }
        
        .icon {
            width: 1.5rem;
            height: 1.5rem;
            margin-right: 0.5rem;
        }
        
        .icon-large {
            width: 2rem;
            height: 2rem;
        }
        
        .scrollable {
            max-height: 24rem;
            overflow-y: auto;
        }
        
        .grid {
            display: grid;
            gap: 1.5rem;
        }
        
        @media (min-width: 1024px) {
            .grid-main {
                grid-template-columns: 2fr 1fr;
            }
            
            .grid-full {
                grid-column: 1 / -1;
            }
        }
        
        .flex {
            display: flex;
        }
        
        .flex-col {
            flex-direction: column;
        }
        
        .items-center {
            align-items: center;
        }
        
        .justify-between {
            justify-content: space-between;
        }
        
        .justify-around {
            justify-content: space-around;
        }
        
        .gap-2 {
            gap: 0.5rem;
        }
        
        .gap-3 {
            gap: 0.75rem;
        }
        
        .gap-4 {
            gap: 1rem;
        }
        
        .gap-6 {
            gap: 1.5rem;
        }
        
        .mb-6 {
            margin-bottom: 1.5rem;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-sm {
            font-size: 0.875rem;
        }
        
        .text-lg {
            font-size: 1.125rem;
        }
        
        .text-xl {
            font-size: 1.25rem;
        }
        
        .text-2xl {
            font-size: 1.5rem;
        }
        
        .font-semibold {
            font-weight: 600;
        }
        
        .font-bold {
            font-weight: 700;
        }
        
        .text-gray-200 {
            color: rgb(229, 231, 235);
        }
        
        .text-gray-300 {
            color: rgb(209, 213, 219);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
    </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.flex{display:flex}.table{display:table}.grid{display:grid}.flex-col{flex-direction:column}.items-center{align-items:center}.justify-between{justify-content:space-between}.justify-around{justify-content:space-around}.gap-2{gap:0.5rem}.gap-3{gap:0.75rem}.gap-4{gap:1rem}.gap-6{gap:1.5rem}.text-center{text-align:center}.text-2xl{font-size:1.5rem;line-height:2rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.font-bold{font-weight:700}.font-semibold{font-weight:600}.text-gray-200{--tw-text-opacity:1;color:rgb(229 231 235 / var(--tw-text-opacity, 1))}.text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219 / var(--tw-text-opacity, 1))}</style></head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="flex items-center justify-between gap-4 mb-6">
            <div class="flex items-center gap-3">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="bot" class="lucide lucide-bot icon-large" style="color: rgb(96, 165, 250);"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg>
                <div>
                    <h1 class="text-2xl font-bold">币安事件合约交易机器人</h1>
                    <p class="text-gray-200">自动化技术分析与交易决策系统</p>
                </div>
            </div>
            <div id="stopTradingBadge" class="badge badge-loss" style="display: none; font-size: 1rem; padding: 0.5rem 1rem;">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="shield-alert" class="lucide lucide-shield-alert icon"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="M12 8v4"></path><path d="M12 16h.01"></path></svg>
                交易已停止 - 达到日亏损上限
            </div>
        </header>

        <!-- Main Content -->
        <main class="grid grid-main">
            <!-- Left Column -->
            <div>
                <!-- Signal Monitor -->
                <div class="card">
                    <div class="flex items-center gap-2 mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="rocket" class="lucide lucide-rocket icon" style="color: rgb(251, 146, 60);"><path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"></path><path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"></path><path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></path><path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path></svg>
                        <h2 class="text-xl font-bold">实时信号监控台</h2>
                    </div>
                    <p class="text-gray-300 mb-6">根据技术指标和概率模型生成交易信号</p>
                    
                    <div class="flex flex-col items-center justify-around gap-6" style="min-height: 200px;">
                        <div class="flex flex-col items-center gap-2">
                            <span class="text-gray-200">15M K线收盘倒计时</span>
                            <span id="countdown" class="countdown">14:05</span>
                        </div>
                        
                        <div class="flex flex-col items-center gap-3">
                            <span class="text-gray-200">当前交易信号</span>
                            <div id="signalBadge" class="badge signal-badge badge-hold"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="minus" class="lucide lucide-minus icon"><path d="M5 12h14"></path></svg>等待</div>
                            <p id="signalReason" class="text-sm text-gray-300 text-center" style="max-width: 300px;">初始化中...</p>
                        </div>
                    </div>
                </div>

                <!-- Active Trade -->
                <div class="card">
                    <h3 class="text-xl font-bold mb-4">当前持仓</h3>
                    <div id="activeTradeContent"><p class="text-gray-200">当前无持仓</p></div>
                </div>
            </div>

            <!-- Right Column -->
            <div>
                <!-- Market Data -->
                <div class="card">
                    <h3 class="text-xl font-bold mb-4">市场数据 &amp; 资金</h3>
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-200">BTC/USDT 价格</span>
                            <span id="currentPrice" class="price-display">67961.90</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-200">账户余额</span>
                            <span id="balance" class="font-semibold">10000.00 USDT</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-200">今日盈亏</span>
                            <span id="dailyPnl" class="font-semibold text-positive">0.00 USDT</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-200">胜率</span>
                            <span id="winRate" class="font-semibold">0.00%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-200">下次投注额</span>
                            <span id="nextBetSize" class="font-semibold">20 USDT</span>
                        </div>
                    </div>
                </div>

                <!-- Technical Indicators -->
                <div class="card">
                    <h3 class="text-xl font-bold mb-4">技术指标</h3>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem; font-size: 0.875rem;">
                        <div class="flex justify-between">
                            <span class="text-gray-200">RSI (14)</span>
                            <span id="rsiValue" class="">57.12</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-200">MACD</span>
                            <span id="macdValue" class="text-positive">75.26</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-200">布林上轨</span>
                            <span id="bbUpper">68122.13</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-200">布林下轨</span>
                            <span id="bbLower">67758.03</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trade History -->
            <div class="grid-full">
                <div class="card">
                    <div class="flex items-center gap-2 mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-lucide="history" class="lucide lucide-history icon"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path><path d="M12 7v5l4 2"></path></svg>
                        <h2 class="text-xl font-bold">交易历史</h2>
                    </div>
                    
                    <div class="scrollable">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>方向</th>
                                    <th>结果</th>
                                    <th>入场价</th>
                                    <th>出场价</th>
                                    <th>盈亏 (USDT)</th>
                                    <th>入场时间</th>
                                </tr>
                            </thead>
                            <tbody id="tradeHistoryBody"><tr><td colspan="6" class="text-center text-gray-300" style="padding: 2rem;">暂无历史交易记录</td></tr></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Trading Bot State
        let klines = [];
        let currentPrice = 68000;
        let signal = { direction: "HOLD", reason: "初始化中..." };
        let activeTrade = null;
        let tradeHistory = [];
        let balance = 10000;
        let isTradingStopped = false;
        let countdown = 15 * 60; // 15 minutes in seconds

        const INITIAL_BALANCE = 10000;
        const DAILY_LOSS_SOFT_LIMIT = 1000;
        const DAILY_LOSS_HARD_LIMIT = 10000;

        // Utility Functions
        function formatCountdown(seconds) {
            const m = Math.floor(seconds / 60).toString().padStart(2, '0');
            const s = (seconds % 60).toString().padStart(2, '0');
            return `${m}:${s}`;
        }

        function calculateIndicators(klines) {
            if (klines.length < 20) {
                return { rsi: 50, macd: 0, bb: { upper: 0, middle: 0, lower: 0 } };
            }
            
            const closes = klines.map(k => k.close);
            
            // RSI calculation (simplified)
            let gains = 0, losses = 0;
            for (let i = 1; i < Math.min(15, closes.length); i++) {
                const change = closes[i] - closes[i-1];
                if (change > 0) gains += change;
                else losses -= change;
            }
            const avgGain = gains / 14;
            const avgLoss = losses / 14;
            const rs = avgGain / avgLoss;
            const rsi = 100 - (100 / (1 + rs));
            
            // MACD (simplified)
            const ema12 = closes.slice(-12).reduce((a, b) => a + b, 0) / 12;
            const ema26 = closes.slice(-26).reduce((a, b) => a + b, 0) / 26;
            const macd = ema12 - ema26;
            
            // Bollinger Bands (simplified)
            const sma20 = closes.slice(-20).reduce((a, b) => a + b, 0) / 20;
            const variance = closes.slice(-20).reduce((sum, price) => sum + Math.pow(price - sma20, 2), 0) / 20;
            const stdDev = Math.sqrt(variance);
            
            return {
                rsi: rsi,
                macd: macd,
                bb: {
                    upper: sma20 + (2 * stdDev),
                    middle: sma20,
                    lower: sma20 - (2 * stdDev)
                }
            };
        }

        function generateSignal(klines) {
            if (klines.length < 2) {
                return { direction: "HOLD", reason: "数据不足，无法生成信号。" };
            }

            const indicators = calculateIndicators(klines);
            const lastKline = klines[klines.length - 1];
            const prevKline = klines[klines.length - 2];

            const isUpMomentum = lastKline.close > prevKline.close;
            const isDownMomentum = lastKline.close < prevKline.close;

            let upProbability = 0.5;
            let downProbability = 0.5;

            if (isUpMomentum) upProbability += 0.2;
            if (isDownMomentum) downProbability += 0.2;
            if (indicators.macd > 0) upProbability += 0.1;
            if (indicators.macd < 0) downProbability += 0.1;
            if (indicators.rsi > 50) upProbability += (indicators.rsi - 50) / 100;
            if (indicators.rsi < 50) downProbability += (50 - indicators.rsi) / 100;

            if (indicators.rsi < 5 && upProbability > 0.7) {
                return { direction: "UP", reason: `RSI极度超卖 (${indicators.rsi.toFixed(2)}), 存在反弹可能。` };
            }
            if (indicators.rsi > 95 && downProbability > 0.7) {
                return { direction: "DOWN", reason: `RSI极度超买 (${indicators.rsi.toFixed(2)}), 存在回调可能。` };
            }
            if (lastKline.close < indicators.bb.lower && upProbability > 0.7) {
                return { direction: "UP", reason: `价格突破布林带下轨，可能超卖反弹。` };
            }
            if (lastKline.close > indicators.bb.upper && downProbability > 0.7) {
                return { direction: "DOWN", reason: `价格突破布林带上轨，可能超买回调。` };
            }

            if (upProbability >= 0.9) {
                return {
                    direction: "UP",
                    reason: `看涨概率 (${(upProbability * 100).toFixed(0)}%) 超过阈值。MACD: ${indicators.macd.toFixed(2)}, RSI: ${indicators.rsi.toFixed(2)}`
                };
            }
            if (downProbability >= 0.9) {
                return {
                    direction: "DOWN",
                    reason: `看跌概率 (${(downProbability * 100).toFixed(0)}%) 超过阈值。MACD: ${indicators.macd.toFixed(2)}, RSI: ${indicators.rsi.toFixed(2)}`
                };
            }

            return { direction: "HOLD", reason: "当前市场信号不明确，等待有利时机。" };
        }

        function getBetSize() {
            const winRate = getWinRate();
            if (winRate > 70) return 50;
            if (winRate < 50 && tradeHistory.length > 10) return 5;
            return 20;
        }

        function getWinRate() {
            if (tradeHistory.length === 0) return 0;
            const wins = tradeHistory.filter(t => t.result === "WIN").length;
            return (wins / tradeHistory.length) * 100;
        }

        function getDailyPnl() {
            const today = new Date().toISOString().split("T")[0];
            return tradeHistory
                .filter(trade => new Date(trade.entryTime).toISOString().split("T")[0] === today)
                .reduce((acc, trade) => acc + (trade.pnl || 0), 0);
        }

        function showNotification(title, message) {
            console.log(`🚀 ${title}: ${message}`);
            // In a real implementation, this would send to DingTalk
        }

        function updateUI() {
            // Update countdown
            document.getElementById('countdown').textContent = formatCountdown(countdown);
            
            // Update current price
            document.getElementById('currentPrice').textContent = currentPrice.toFixed(2);
            
            // Update balance
            document.getElementById('balance').textContent = balance.toFixed(2) + ' USDT';
            
            // Update daily P&L
            const dailyPnl = getDailyPnl();
            const dailyPnlElement = document.getElementById('dailyPnl');
            dailyPnlElement.textContent = dailyPnl.toFixed(2) + ' USDT';
            dailyPnlElement.className = dailyPnl >= 0 ? 'font-semibold text-positive' : 'font-semibold text-negative';
            
            // Update win rate
            document.getElementById('winRate').textContent = getWinRate().toFixed(2) + '%';
            
            // Update next bet size
            document.getElementById('nextBetSize').textContent = getBetSize() + ' USDT';
            
            // Update signal
            const signalBadge = document.getElementById('signalBadge');
            const signalReason = document.getElementById('signalReason');
            
            signalBadge.className = 'badge signal-badge';
            if (signal.direction === 'UP') {
                signalBadge.className += ' badge-up';
                signalBadge.innerHTML = '<i data-lucide="arrow-up" class="icon"></i>上涨';
            } else if (signal.direction === 'DOWN') {
                signalBadge.className += ' badge-down';
                signalBadge.innerHTML = '<i data-lucide="arrow-down" class="icon"></i>下跌';
            } else {
                signalBadge.className += ' badge-hold';
                signalBadge.innerHTML = '<i data-lucide="minus" class="icon"></i>等待';
            }
            signalReason.textContent = signal.reason;
            
            // Update active trade
            const activeTradeContent = document.getElementById('activeTradeContent');
            if (activeTrade) {
                const direction = activeTrade.direction === 'UP' ? '做多' : '做空';
                const iconClass = activeTrade.direction === 'UP' ? 'arrow-up' : 'arrow-down';
                const iconColor = activeTrade.direction === 'UP' ? 'color: rgb(96, 165, 250);' : 'color: rgb(239, 68, 68);';
                
                activeTradeContent.innerHTML = `
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-lg font-semibold flex items-center gap-2">
                                <i data-lucide="${iconClass}" class="icon" style="${iconColor}"></i>
                                ${direction}
                            </p>
                            <p class="text-sm text-gray-200">入场价格: ${activeTrade.entryPrice.toFixed(2)} USDT</p>
                        </div>
                        <div>
                            <p class="text-lg font-semibold">${activeTrade.size} USDT</p>
                            <p class="text-sm text-gray-300 text-right">合约费用</p>
                        </div>
                    </div>
                `;
            } else {
                activeTradeContent.innerHTML = '<p class="text-gray-200">当前无持仓</p>';
            }
            
            // Update indicators
            const indicators = calculateIndicators(klines);
            
            const rsiElement = document.getElementById('rsiValue');
            rsiElement.textContent = indicators.rsi.toFixed(2);
            rsiElement.className = indicators.rsi > 70 ? 'text-negative' : indicators.rsi < 30 ? 'text-positive' : '';
            
            const macdElement = document.getElementById('macdValue');
            macdElement.textContent = indicators.macd.toFixed(2);
            macdElement.className = indicators.macd > 0 ? 'text-positive' : 'text-negative';
            
            document.getElementById('bbUpper').textContent = indicators.bb.upper.toFixed(2);
            document.getElementById('bbLower').textContent = indicators.bb.lower.toFixed(2);
            
            // Update trade history
            updateTradeHistory();
            
            // Update trading stopped status
            document.getElementById('stopTradingBadge').style.display = isTradingStopped ? 'flex' : 'none';
            
            // Recreate icons
            lucide.createIcons();
        }

        function updateTradeHistory() {
            const tbody = document.getElementById('tradeHistoryBody');
            
            if (tradeHistory.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-gray-300" style="padding: 2rem;">暂无历史交易记录</td></tr>';
                return;
            }
            
            tbody.innerHTML = tradeHistory.map(trade => {
                const directionBadge = trade.direction === 'UP' 
                    ? '<span class="badge" style="border-color: rgb(96, 165, 250); color: rgb(96, 165, 250);">上涨</span>'
                    : '<span class="badge" style="border-color: rgb(239, 68, 68); color: rgb(239, 68, 68);">下跌</span>';
                
                const resultBadge = trade.result === 'WIN' 
                    ? '<span class="badge badge-win">WIN</span>'
                    : trade.result === 'LOSS' 
                    ? '<span class="badge badge-loss">LOSS</span>'
                    : '<span class="badge badge-tie">TIE</span>';
                
                const pnlClass = trade.pnl >= 0 ? 'text-positive' : 'text-negative';
                
                return `
                    <tr>
                        <td>${directionBadge}</td>
                        <td>${resultBadge}</td>
                        <td>${trade.entryPrice.toFixed(2)}</td>
                        <td>${trade.exitPrice?.toFixed(2) || '-'}</td>
                        <td class="${pnlClass}">${trade.pnl?.toFixed(2) || '-'}</td>
                        <td>${new Date(trade.entryTime).toLocaleTimeString()}</td>
                    </tr>
                `;
            }).join('');
        }

        // Initialize with historical k-lines
        function initializeKlines() {
            klines = Array.from({ length: 50 }, (_, i) => {
                const open = 67500 + i * 10 + Math.random() * 100;
                const close = open + (Math.random() - 0.5) * 200;
                const high = Math.max(open, close) + Math.random() * 50;
                const low = Math.min(open, close) - Math.random() * 50;
                return {
                    timestamp: Date.now() - (50 - i) * 15 * 60 * 1000,
                    open,
                    high,
                    low,
                    close,
                    volume: 1000 + Math.random() * 500,
                };
            });
            currentPrice = klines[klines.length - 1].close;
        }

        // Main simulation loop
        function runSimulation() {
            if (isTradingStopped) return;

            // Simulate price movement
            currentPrice += (Math.random() - 0.5) * 20;

            // Simulate 15-minute k-line countdown
            countdown--;
            if (countdown <= 0) {
                // A new 15-minute k-line is formed
                const lastKline = klines[klines.length - 1];
                const newKline = {
                    timestamp: Date.now(),
                    open: lastKline.close,
                    high: lastKline.close + Math.random() * 50,
                    low: lastKline.close - Math.random() * 50,
                    close: currentPrice,
                    volume: 1000 + Math.random() * 500,
                };

                klines = [...klines.slice(1), newKline];

                // Generate a new signal for the new k-line
                signal = generateSignal(klines);

                // Automated Decision Engine
                if (signal.direction !== "HOLD" && !activeTrade) {
                    const betSize = getBetSize();
                    if (balance >= betSize) {
                        activeTrade = {
                            id: Date.now(),
                            entryTime: Date.now(),
                            direction: signal.direction,
                            entryPrice: currentPrice,
                            size: betSize,
                            status: "ACTIVE",
                        };

                        showNotification("交易信号已发送至钉钉", `方向: ${activeTrade.direction}, 金额: ${activeTrade.size} USDT`);
                    }
                }
                countdown = 15 * 60; // Reset countdown
            }

            // Signal Settlement Checker (contracts expire in 10 mins, sped up for demo)
            if (activeTrade && Date.now() - activeTrade.entryTime > (10 * 60 * 1000) / 180) {
                const exitPrice = currentPrice;
                let result, pnl;

                if (
                    (activeTrade.direction === "UP" && exitPrice > activeTrade.entryPrice) ||
                    (activeTrade.direction === "DOWN" && exitPrice < activeTrade.entryPrice)
                ) {
                    result = "WIN";
                    pnl = activeTrade.size;
                } else if (exitPrice === activeTrade.entryPrice) {
                    result = "TIE";
                    pnl = 0;
                } else {
                    result = "LOSS";
                    pnl = -activeTrade.size;
                }

                const settledTrade = {
                    ...activeTrade,
                    exitTime: Date.now(),
                    exitPrice,
                    status: "SETTLED",
                    result,
                    pnl,
                };

                tradeHistory = [settledTrade, ...tradeHistory];
                balance += pnl;
                activeTrade = null;
            }

            // Daily loss control
            const dailyPnl = getDailyPnl();
            if (dailyPnl <= -DAILY_LOSS_HARD_LIMIT) {
                isTradingStopped = true;
                console.error(`[风控] 已达到单日止损硬上限 ${DAILY_LOSS_HARD_LIMIT} USDT. 今日交易已停止.`);
            }

            updateUI();
        }

        // Initialize and start simulation
        initializeKlines();
        updateUI();
        setInterval(runSimulation, 1000); // Run simulation every second
    </script>

</body></html>