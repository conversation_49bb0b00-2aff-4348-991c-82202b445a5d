"""
回测数据模拟器
模拟历史K线数据流，用于信号时机优化测试
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any, Iterator
from datetime import datetime, timedelta
import logging
import pandas as pd
import numpy as np
from pathlib import Path

# 尝试导入自定义logger，如果不存在则使用标准logger
try:
    from quant.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

try:
    from quant.api.binance_spot_api import BinanceSpotApi
    from quant.config import config
    from quant import const
except ImportError:
    logger.warning("无法导入币安API，将使用模拟数据")
    BinanceSpotApi = None
    config = None
    const = None


@dataclass
class BacktestConfig:
    """回测配置"""
    symbol: str = "BTC/USDT"
    start_date: str = "2024-01-01"  # 开始日期
    end_date: str = "2024-01-31"    # 结束日期
    timeframe: str = "1m"           # 基础时间周期
    data_source: str = "binance"    # 数据源：binance, file, mock
    data_file_path: Optional[str] = None  # 数据文件路径
    simulation_speed: float = 1.0   # 模拟速度倍数（1.0=实时）
    enable_weekend: bool = False    # 是否包含周末数据


class BacktestDataSimulator:
    """回测数据模拟器"""
    
    def __init__(self, config: BacktestConfig):
        """
        初始化回测数据模拟器
        
        Args:
            config: 回测配置
        """
        self.config = config
        self.kline_data: List[Dict] = []
        self.current_index = 0
        self.is_loaded = False
        
        logger.info(f"回测数据模拟器初始化完成")
        logger.info(f"交易对: {config.symbol}, 时间范围: {config.start_date} ~ {config.end_date}")
    
    async def load_historical_data(self) -> bool:
        """
        加载历史数据
        
        Returns:
            是否成功加载
        """
        try:
            if self.config.data_source == "binance":
                success = await self._load_from_binance()
            elif self.config.data_source == "file":
                success = self._load_from_file()
            else:  # mock
                success = self._generate_mock_data()
            
            if success:
                self.is_loaded = True
                logger.info(f"历史数据加载完成，共 {len(self.kline_data)} 根K线")
                
                # 显示数据范围
                if self.kline_data:
                    first_time = datetime.fromtimestamp(self.kline_data[0]['timestamp'] / 1000)
                    last_time = datetime.fromtimestamp(self.kline_data[-1]['timestamp'] / 1000)
                    logger.info(f"数据时间范围: {first_time} ~ {last_time}")
            
            return success
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            return False
    
    async def _load_from_binance(self) -> bool:
        """从币安API加载数据"""
        if not BinanceSpotApi or not config:
            logger.error("币安API未可用，无法加载数据")
            return False
        
        try:
            api = BinanceSpotApi(config.binance_api_key, config.binance_secret_key)
            
            # 转换日期格式
            start_time = datetime.strptime(self.config.start_date, "%Y-%m-%d")
            end_time = datetime.strptime(self.config.end_date, "%Y-%m-%d")
            
            # 分批获取数据（每次最多1000根K线）
            current_time = start_time
            all_klines = []
            
            while current_time < end_time:
                # 计算批次结束时间
                batch_end = min(current_time + timedelta(hours=16), end_time)  # 1000分钟约16小时
                
                logger.info(f"获取K线数据: {current_time.strftime('%Y-%m-%d %H:%M')} ~ "
                           f"{batch_end.strftime('%Y-%m-%d %H:%M')}")
                
                # 获取K线数据
                success, error = api.get_kline(
                    symbol=self.config.symbol,
                    interval=const.KLINE_1M,
                    start_time=int(current_time.timestamp() * 1000),
                    end_time=int(batch_end.timestamp() * 1000),
                    limit=1000
                )
                
                if not success:
                    logger.error(f"获取K线数据失败: {error}")
                    return False
                
                # 转换数据格式
                for kline in success:
                    kline_data = {
                        'timestamp': int(kline[0]),
                        'open': float(kline[1]),
                        'high': float(kline[2]),
                        'low': float(kline[3]),
                        'close': float(kline[4]),
                        'volume': float(kline[5])
                    }
                    all_klines.append(kline_data)
                
                current_time = batch_end
                
                # 避免API限制
                import asyncio
                await asyncio.sleep(0.1)
            
            self.kline_data = all_klines
            return True
            
        except Exception as e:
            logger.error(f"从币安加载数据异常: {e}")
            return False
    
    def _load_from_file(self) -> bool:
        """从文件加载数据"""
        if not self.config.data_file_path:
            logger.error("未指定数据文件路径")
            return False
        
        try:
            file_path = Path(self.config.data_file_path)
            if not file_path.exists():
                logger.error(f"数据文件不存在: {file_path}")
                return False
            
            # 根据文件扩展名选择读取方式
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path)
            elif file_path.suffix.lower() == '.json':
                df = pd.read_json(file_path)
            else:
                logger.error(f"不支持的文件格式: {file_path.suffix}")
                return False
            
            # 转换数据格式
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_columns):
                logger.error(f"数据文件缺少必要列: {required_columns}")
                return False
            
            self.kline_data = df[required_columns].to_dict('records')
            return True
            
        except Exception as e:
            logger.error(f"从文件加载数据异常: {e}")
            return False
    
    def _generate_mock_data(self) -> bool:
        """生成模拟数据"""
        try:
            start_time = datetime.strptime(self.config.start_date, "%Y-%m-%d")
            end_time = datetime.strptime(self.config.end_date, "%Y-%m-%d")
            
            # 生成时间序列（1分钟间隔）
            current_time = start_time
            base_price = 50000.0  # BTC基础价格
            
            while current_time < end_time:
                # 跳过周末（如果配置不包含）
                if not self.config.enable_weekend and current_time.weekday() >= 5:
                    current_time += timedelta(minutes=1)
                    continue
                
                # 生成随机价格变动
                price_change = np.random.normal(0, base_price * 0.001)  # 0.1%标准差
                base_price = max(base_price + price_change, base_price * 0.9)  # 防止价格过低
                
                # 生成OHLCV数据
                open_price = base_price
                high_price = open_price + abs(np.random.normal(0, open_price * 0.002))
                low_price = open_price - abs(np.random.normal(0, open_price * 0.002))
                close_price = low_price + (high_price - low_price) * np.random.random()
                volume = np.random.uniform(10, 1000)
                
                kline_data = {
                    'timestamp': int(current_time.timestamp() * 1000),
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': round(volume, 4)
                }
                
                self.kline_data.append(kline_data)
                base_price = close_price
                current_time += timedelta(minutes=1)
            
            return True
            
        except Exception as e:
            logger.error(f"生成模拟数据异常: {e}")
            return False
    
    def get_kline_iterator(self) -> Iterator[Dict]:
        """获取K线数据迭代器"""
        if not self.is_loaded:
            logger.error("数据未加载，请先调用 load_historical_data()")
            return
        
        for kline in self.kline_data:
            yield kline
    
    def get_kline_batch(self, batch_size: int = 100) -> List[Dict]:
        """
        获取批量K线数据
        
        Args:
            batch_size: 批量大小
            
        Returns:
            K线数据列表
        """
        if not self.is_loaded:
            logger.error("数据未加载")
            return []
        
        start_idx = self.current_index
        end_idx = min(start_idx + batch_size, len(self.kline_data))
        
        batch = self.kline_data[start_idx:end_idx]
        self.current_index = end_idx
        
        return batch
    
    def has_more_data(self) -> bool:
        """是否还有更多数据"""
        return self.current_index < len(self.kline_data)
    
    def reset(self):
        """重置数据指针"""
        self.current_index = 0
    
    def get_data_info(self) -> Dict[str, Any]:
        """获取数据信息"""
        if not self.is_loaded:
            return {"loaded": False}
        
        first_kline = self.kline_data[0] if self.kline_data else None
        last_kline = self.kline_data[-1] if self.kline_data else None
        
        return {
            "loaded": True,
            "total_klines": len(self.kline_data),
            "current_index": self.current_index,
            "remaining_klines": len(self.kline_data) - self.current_index,
            "first_timestamp": first_kline['timestamp'] if first_kline else None,
            "last_timestamp": last_kline['timestamp'] if last_kline else None,
            "first_time": datetime.fromtimestamp(first_kline['timestamp'] / 1000).isoformat() if first_kline else None,
            "last_time": datetime.fromtimestamp(last_kline['timestamp'] / 1000).isoformat() if last_kline else None,
            "price_range": {
                "min": min(k['low'] for k in self.kline_data),
                "max": max(k['high'] for k in self.kline_data)
            } if self.kline_data else None
        }
