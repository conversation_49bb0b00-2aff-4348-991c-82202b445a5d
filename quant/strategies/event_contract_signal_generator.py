#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
事件合约信号生成器

专门针对币安事件合约的信号生成系统，基于技术分析预测15分钟K线方向

Author: HertelQuant Enhanced
Date: 2025-01-31
"""

import numpy as np
import pandas as pd
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    # 使用简化版技术指标
    from .technical_indicators_simple import (
        calculate_sma, calculate_ema, calculate_rsi, calculate_macd,
        calculate_bollinger_bands, calculate_stochastic, calculate_atr, calculate_volatility
    )
from datetime import datetime, timedelta
from collections import deque
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

from quant.utils import logger


@dataclass
class KlineData:
    """K线数据结构"""
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: float
    
    @property
    def is_bullish(self) -> bool:
        """判断是否为阳线：收盘价 > 开盘价"""
        return self.close > self.open
    
    @property
    def is_bearish(self) -> bool:
        """判断是否为阴线：收盘价 < 开盘价"""
        return self.close < self.open
    
    @property
    def body_size(self) -> float:
        """K线实体大小"""
        return abs(self.close - self.open)
    
    @property
    def upper_shadow(self) -> float:
        """上影线长度"""
        return self.high - max(self.open, self.close)
    
    @property
    def lower_shadow(self) -> float:
        """下影线长度"""
        return min(self.open, self.close) - self.low


@dataclass
class TechnicalIndicators:
    """技术指标数据结构"""
    # 趋势指标
    sma_5: float = 0.0
    sma_10: float = 0.0
    sma_20: float = 0.0
    ema_5: float = 0.0
    ema_10: float = 0.0
    ema_20: float = 0.0
    
    # 振荡指标
    rsi_14: float = 50.0
    rsi_21: float = 50.0
    stoch_k: float = 50.0
    stoch_d: float = 50.0
    
    # MACD指标
    macd_line: float = 0.0
    macd_signal: float = 0.0
    macd_histogram: float = 0.0
    
    # 布林带
    bb_upper: float = 0.0
    bb_middle: float = 0.0
    bb_lower: float = 0.0
    bb_position: float = 0.5  # 价格在布林带中的位置 (0-1)
    
    # KDJ指标
    kdj_k: float = 50.0
    kdj_d: float = 50.0
    kdj_j: float = 50.0
    
    # 成交量指标
    volume_sma: float = 0.0
    volume_ratio: float = 1.0
    
    # 波动率
    atr: float = 0.0
    volatility: float = 0.0


@dataclass
class SignalResult:
    """信号生成结果"""
    has_signal: bool = False
    direction: str = "NEUTRAL"  # UP, DOWN, NEUTRAL
    confidence: float = 0.0
    bullish_probability: float = 50.0
    bearish_probability: float = 50.0
    supporting_timeframes: List[str] = None
    technical_score: float = 0.0
    risk_level: str = "MEDIUM"  # LOW, MEDIUM, HIGH
    timeframe_analysis: Dict[str, Dict] = None
    generated_at: str = ""
    
    def __post_init__(self):
        if self.supporting_timeframes is None:
            self.supporting_timeframes = []
        if self.timeframe_analysis is None:
            self.timeframe_analysis = {}
        if not self.generated_at:
            self.generated_at = datetime.now().isoformat()


class KlineDataManager:
    """K线数据管理器"""
    
    def __init__(self, max_periods: int = 200):
        """
        初始化K线数据管理器
        
        Args:
            max_periods: 每个时间周期保留的最大K线数量
        """
        self.max_periods = max_periods
        
        # 不同时间周期的K线数据存储
        self.klines = {
            '1m': deque(maxlen=max_periods),
            '5m': deque(maxlen=max_periods),
            '15m': deque(maxlen=max_periods),
            '30m': deque(maxlen=max_periods),
            '1h': deque(maxlen=max_periods)
        }
        
        # 1分钟K线聚合缓存
        self._minute_buffer = deque(maxlen=60)
        
        logger.info(f"KlineDataManager initialized with max_periods={max_periods}", caller=self)
    
    def add_minute_kline(self, kline: KlineData):
        """
        添加1分钟K线数据
        
        Args:
            kline: 1分钟K线数据
        """
        # 添加到1分钟序列
        self.klines['1m'].append(kline)
        self._minute_buffer.append(kline)
        
        # 聚合生成更大时间周期的K线
        self._aggregate_timeframes(kline)
        
        logger.debug(f"Added 1m kline: O={kline.open}, H={kline.high}, L={kline.low}, C={kline.close}", caller=self)
    
    def _aggregate_timeframes(self, current_kline: KlineData):
        """
        聚合生成更大时间周期的K线
        
        Args:
            current_kline: 当前1分钟K线
        """
        # 获取当前时间戳的分钟数
        dt = datetime.fromtimestamp(current_kline.timestamp / 1000)
        
        # 5分钟K线聚合
        if dt.minute % 5 == 4:  # 每5分钟的最后一分钟
            self._aggregate_period('5m', 5)
        
        # 15分钟K线聚合
        if dt.minute % 15 == 14:  # 每15分钟的最后一分钟
            self._aggregate_period('15m', 15)
        
        # 30分钟K线聚合
        if dt.minute % 30 == 29:  # 每30分钟的最后一分钟
            self._aggregate_period('30m', 30)
        
        # 1小时K线聚合
        if dt.minute == 59:  # 每小时的最后一分钟
            self._aggregate_period('1h', 60)
    
    def _aggregate_period(self, timeframe: str, minutes: int):
        """
        聚合指定时间周期的K线
        
        Args:
            timeframe: 时间周期
            minutes: 分钟数
        """
        if len(self._minute_buffer) < minutes:
            return
        
        # 获取最近N分钟的K线数据
        recent_klines = list(self._minute_buffer)[-minutes:]
        
        if not recent_klines:
            return
        
        # 聚合数据
        open_price = recent_klines[0].open
        close_price = recent_klines[-1].close
        high_price = max(k.high for k in recent_klines)
        low_price = min(k.low for k in recent_klines)
        total_volume = sum(k.volume for k in recent_klines)
        
        # 使用最新K线的时间戳
        timestamp = recent_klines[-1].timestamp
        
        # 创建聚合K线
        aggregated_kline = KlineData(
            timestamp=timestamp,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=total_volume
        )
        
        # 添加到对应时间周期
        self.klines[timeframe].append(aggregated_kline)
        
        logger.debug(f"Aggregated {timeframe} kline: O={open_price}, H={high_price}, L={low_price}, C={close_price}", caller=self)
    
    def get_klines(self, timeframe: str, count: int = None) -> List[KlineData]:
        """
        获取指定时间周期的K线数据
        
        Args:
            timeframe: 时间周期
            count: 返回数量，None表示全部
            
        Returns:
            K线数据列表
        """
        if timeframe not in self.klines:
            return []
        
        klines = list(self.klines[timeframe])
        if count is not None:
            klines = klines[-count:]
        
        return klines
    
    def get_latest_kline(self, timeframe: str) -> Optional[KlineData]:
        """
        获取最新的K线数据
        
        Args:
            timeframe: 时间周期
            
        Returns:
            最新K线数据
        """
        klines = self.get_klines(timeframe, 1)
        return klines[0] if klines else None


class TechnicalIndicatorCalculator:
    """技术指标计算器"""
    
    def __init__(self):
        logger.info("TechnicalIndicatorCalculator initialized", caller=self)
    
    def calculate_indicators(self, klines: List[KlineData]) -> TechnicalIndicators:
        """
        计算技术指标
        
        Args:
            klines: K线数据列表
            
        Returns:
            技术指标数据
        """
        if len(klines) < 50:  # 需要足够的数据计算指标
            logger.warning(f"Insufficient klines for indicators calculation: {len(klines)}", caller=self)
            return TechnicalIndicators()
        
        # 转换为numpy数组
        closes = np.array([k.close for k in klines])
        highs = np.array([k.high for k in klines])
        lows = np.array([k.low for k in klines])
        volumes = np.array([k.volume for k in klines])
        
        indicators = TechnicalIndicators()
        
        try:
            if TALIB_AVAILABLE:
                # 使用talib计算技术指标
                indicators.sma_5 = float(talib.SMA(closes, timeperiod=5)[-1])
                indicators.sma_10 = float(talib.SMA(closes, timeperiod=10)[-1])
                indicators.sma_20 = float(talib.SMA(closes, timeperiod=20)[-1])
                indicators.ema_5 = float(talib.EMA(closes, timeperiod=5)[-1])
                indicators.ema_10 = float(talib.EMA(closes, timeperiod=10)[-1])
                indicators.ema_20 = float(talib.EMA(closes, timeperiod=20)[-1])
                
                # RSI指标
                indicators.rsi_14 = float(talib.RSI(closes, timeperiod=14)[-1])
                indicators.rsi_21 = float(talib.RSI(closes, timeperiod=21)[-1])
                
                # 随机指标
                stoch_k, stoch_d = talib.STOCH(highs, lows, closes, 
                                              fastk_period=14, slowk_period=3, slowd_period=3)
                indicators.stoch_k = float(stoch_k[-1])
                indicators.stoch_d = float(stoch_d[-1])
                
                # MACD指标
                macd_line, macd_signal, macd_histogram = talib.MACD(closes, 
                                                                   fastperiod=12, slowperiod=26, signalperiod=9)
                indicators.macd_line = float(macd_line[-1])
                indicators.macd_signal = float(macd_signal[-1])
                indicators.macd_histogram = float(macd_histogram[-1])
                
                # 布林带
                bb_upper, bb_middle, bb_lower = talib.BBANDS(closes, timeperiod=20, nbdevup=2, nbdevdn=2)
                indicators.bb_upper = float(bb_upper[-1])
                indicators.bb_middle = float(bb_middle[-1])
                indicators.bb_lower = float(bb_lower[-1])
                
                # ATR
                indicators.atr = float(talib.ATR(highs, lows, closes, timeperiod=14)[-1])
                
            else:
                # 使用简化版技术指标计算
                closes_list = closes.tolist()
                highs_list = highs.tolist()
                lows_list = lows.tolist()
                volumes_list = volumes.tolist()
                
                # 移动平均线
                sma_5 = calculate_sma(closes_list, 5)
                sma_10 = calculate_sma(closes_list, 10)
                sma_20 = calculate_sma(closes_list, 20)
                ema_5 = calculate_ema(closes_list, 5)
                ema_10 = calculate_ema(closes_list, 10)
                ema_20 = calculate_ema(closes_list, 20)
                
                indicators.sma_5 = sma_5[-1] if not pd.isna(sma_5[-1]) else 0.0
                indicators.sma_10 = sma_10[-1] if not pd.isna(sma_10[-1]) else 0.0
                indicators.sma_20 = sma_20[-1] if not pd.isna(sma_20[-1]) else 0.0
                indicators.ema_5 = ema_5[-1] if not pd.isna(ema_5[-1]) else 0.0
                indicators.ema_10 = ema_10[-1] if not pd.isna(ema_10[-1]) else 0.0
                indicators.ema_20 = ema_20[-1] if not pd.isna(ema_20[-1]) else 0.0
                
                # RSI指标
                rsi_14 = calculate_rsi(closes_list, 14)
                rsi_21 = calculate_rsi(closes_list, 21)
                indicators.rsi_14 = rsi_14[-1] if not pd.isna(rsi_14[-1]) else 50.0
                indicators.rsi_21 = rsi_21[-1] if not pd.isna(rsi_21[-1]) else 50.0
                
                # 随机指标
                stoch_k, stoch_d = calculate_stochastic(highs_list, lows_list, closes_list, 14, 3)
                indicators.stoch_k = stoch_k[-1] if not pd.isna(stoch_k[-1]) else 50.0
                indicators.stoch_d = stoch_d[-1] if not pd.isna(stoch_d[-1]) else 50.0
                
                # MACD指标
                macd_line, macd_signal, macd_histogram = calculate_macd(closes_list, 12, 26, 9)
                indicators.macd_line = macd_line[-1] if not pd.isna(macd_line[-1]) else 0.0
                indicators.macd_signal = macd_signal[-1] if not pd.isna(macd_signal[-1]) else 0.0
                indicators.macd_histogram = macd_histogram[-1] if not pd.isna(macd_histogram[-1]) else 0.0
                
                # 布林带
                bb_upper, bb_middle, bb_lower = calculate_bollinger_bands(closes_list, 20, 2.0)
                indicators.bb_upper = bb_upper[-1] if not pd.isna(bb_upper[-1]) else closes[-1] * 1.02
                indicators.bb_middle = bb_middle[-1] if not pd.isna(bb_middle[-1]) else closes[-1]
                indicators.bb_lower = bb_lower[-1] if not pd.isna(bb_lower[-1]) else closes[-1] * 0.98
                
                # ATR
                atr_values = calculate_atr(highs_list, lows_list, closes_list, 14)
                indicators.atr = atr_values[-1] if not pd.isna(atr_values[-1]) else 0.0
            
            # 通用计算部分
            # 计算价格在布林带中的位置
            current_price = closes[-1]
            bb_range = indicators.bb_upper - indicators.bb_lower
            if bb_range > 0:
                indicators.bb_position = (current_price - indicators.bb_lower) / bb_range
            else:
                indicators.bb_position = 0.5
            
            # KDJ指标（基于随机指标计算）
            indicators.kdj_k = indicators.stoch_k
            indicators.kdj_d = indicators.stoch_d
            indicators.kdj_j = 3 * indicators.kdj_k - 2 * indicators.kdj_d
            
            # 成交量指标
            if TALIB_AVAILABLE:
                indicators.volume_sma = float(talib.SMA(volumes, timeperiod=20)[-1])
            else:
                volume_sma = calculate_sma(volumes.tolist(), 20)
                indicators.volume_sma = volume_sma[-1] if not pd.isna(volume_sma[-1]) else volumes[-1]
                
            if indicators.volume_sma > 0:
                indicators.volume_ratio = volumes[-1] / indicators.volume_sma
            
            # 计算波动率（价格变化的标准差）
            if TALIB_AVAILABLE:
                returns = np.diff(closes) / closes[:-1]
                indicators.volatility = float(np.std(returns[-20:]) * np.sqrt(20))  # 20期年化波动率
            else:
                volatility_values = calculate_volatility(closes.tolist(), 20)
                indicators.volatility = volatility_values[-1] if not pd.isna(volatility_values[-1]) else 0.5
            
            logger.debug(f"Calculated indicators: RSI={indicators.rsi_14:.2f}, BB_pos={indicators.bb_position:.3f}", caller=self)
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}", caller=self)
        
        return indicators


class ProbabilityAnalyzer:
    """概率分析器"""
    
    def __init__(self, lookback_periods: int = 100):
        """
        初始化概率分析器
        
        Args:
            lookback_periods: 回看期数，用于计算历史概率
        """
        self.lookback_periods = lookback_periods
        logger.info(f"ProbabilityAnalyzer initialized with lookback_periods={lookback_periods}", caller=self)
    
    def analyze_next_candle_probability(self, klines: List[KlineData], 
                                      indicators: TechnicalIndicators) -> Tuple[float, float]:
        """
        分析下一根K线方向的概率
        
        Args:
            klines: 历史K线数据
            indicators: 当前技术指标
            
        Returns:
            (看涨概率, 看跌概率) 百分比
        """
        if len(klines) < self.lookback_periods:
            logger.warning(f"Insufficient historical data for probability analysis: {len(klines)}", caller=self)
            return 50.0, 50.0
        
        # 基于多种因素计算概率
        bullish_signals = 0
        bearish_signals = 0
        total_weight = 0
        
        # 1. 历史模式分析 (权重: 30%)
        pattern_bullish, pattern_bearish = self._analyze_historical_patterns(klines)
        pattern_weight = 30
        bullish_signals += pattern_bullish * pattern_weight
        bearish_signals += pattern_bearish * pattern_weight
        total_weight += pattern_weight
        
        # 2. 技术指标分析 (权重: 40%)
        indicator_bullish, indicator_bearish = self._analyze_technical_indicators(indicators)
        indicator_weight = 40
        bullish_signals += indicator_bullish * indicator_weight
        bearish_signals += indicator_bearish * indicator_weight
        total_weight += indicator_weight
        
        # 3. 趋势分析 (权重: 20%)
        trend_bullish, trend_bearish = self._analyze_trend_momentum(klines)
        trend_weight = 20
        bullish_signals += trend_bullish * trend_weight
        bearish_signals += trend_bearish * trend_weight
        total_weight += trend_weight
        
        # 4. 成交量分析 (权重: 10%)
        volume_bullish, volume_bearish = self._analyze_volume_confirmation(klines)
        volume_weight = 10
        bullish_signals += volume_bullish * volume_weight
        bearish_signals += volume_bearish * volume_weight
        total_weight += volume_weight
        
        # 计算最终概率
        if total_weight > 0:
            bullish_prob = (bullish_signals / total_weight) * 100
            bearish_prob = (bearish_signals / total_weight) * 100
        else:
            bullish_prob = bearish_prob = 50.0
        
        # 确保概率和为100%
        total_prob = bullish_prob + bearish_prob
        if total_prob > 0:
            bullish_prob = (bullish_prob / total_prob) * 100
            bearish_prob = (bearish_prob / total_prob) * 100
        else:
            bullish_prob = bearish_prob = 50.0
        
        logger.debug(f"Probability analysis: Bullish={bullish_prob:.1f}%, Bearish={bearish_prob:.1f}%", caller=self)
        
        return bullish_prob, bearish_prob
    
    def _analyze_historical_patterns(self, klines: List[KlineData]) -> Tuple[float, float]:
        """
        分析历史K线模式
        
        Args:
            klines: K线数据
            
        Returns:
            (看涨权重, 看跌权重) 0-1之间
        """
        if len(klines) < 20:
            return 0.5, 0.5
        
        # 统计最近相似市场条件下的K线方向
        recent_klines = klines[-self.lookback_periods:]
        current_kline = klines[-1]
        
        bullish_count = 0
        bearish_count = 0
        similar_count = 0
        
        # 寻找相似的市场条件
        for i in range(len(recent_klines) - 1):
            kline = recent_klines[i]
            next_kline = recent_klines[i + 1]
            
            # 定义相似条件：价格范围相近、成交量相近
            price_diff = abs(kline.close - current_kline.close) / current_kline.close
            volume_diff = abs(kline.volume - current_kline.volume) / max(current_kline.volume, 1)
            
            if price_diff < 0.02 and volume_diff < 0.5:  # 2%价格差和50%成交量差
                similar_count += 1
                if next_kline.is_bullish:
                    bullish_count += 1
                elif next_kline.is_bearish:
                    bearish_count += 1
        
        if similar_count > 0:
            bullish_ratio = bullish_count / similar_count
            bearish_ratio = bearish_count / similar_count
        else:
            # 如果没有相似条件，使用整体统计
            total_bullish = sum(1 for k in recent_klines if k.is_bullish)
            total_bearish = sum(1 for k in recent_klines if k.is_bearish)
            total_candles = len(recent_klines)
            
            bullish_ratio = total_bullish / total_candles if total_candles > 0 else 0.5
            bearish_ratio = total_bearish / total_candles if total_candles > 0 else 0.5
        
        return bullish_ratio, bearish_ratio
    
    def _analyze_technical_indicators(self, indicators: TechnicalIndicators) -> Tuple[float, float]:
        """
        基于技术指标分析方向概率
        
        Args:
            indicators: 技术指标
            
        Returns:
            (看涨权重, 看跌权重) 0-1之间
        """
        bullish_score = 0
        bearish_score = 0
        total_indicators = 0
        
        # RSI分析
        if indicators.rsi_14 < 30:  # 超卖
            bullish_score += 2
        elif indicators.rsi_14 > 70:  # 超买
            bearish_score += 2
        elif indicators.rsi_14 < 45:
            bullish_score += 1
        elif indicators.rsi_14 > 55:
            bearish_score += 1
        total_indicators += 2
        
        # MACD分析
        if indicators.macd_histogram > 0:
            if indicators.macd_line > indicators.macd_signal:
                bullish_score += 2
            else:
                bullish_score += 1
        else:
            if indicators.macd_line < indicators.macd_signal:
                bearish_score += 2
            else:
                bearish_score += 1
        total_indicators += 2
        
        # 布林带分析
        if indicators.bb_position < 0.2:  # 接近下轨
            bullish_score += 2
        elif indicators.bb_position > 0.8:  # 接近上轨
            bearish_score += 2
        elif indicators.bb_position < 0.4:
            bullish_score += 1
        elif indicators.bb_position > 0.6:
            bearish_score += 1
        total_indicators += 2
        
        # 随机指标分析
        if indicators.stoch_k < 20 and indicators.stoch_d < 20:
            bullish_score += 2
        elif indicators.stoch_k > 80 and indicators.stoch_d > 80:
            bearish_score += 2
        total_indicators += 2
        
        # 移动平均线分析
        ema_score = 0
        if indicators.ema_5 > indicators.ema_10 > indicators.ema_20:
            ema_score = 2  # 强烈看涨
        elif indicators.ema_5 > indicators.ema_10:
            ema_score = 1  # 轻微看涨
        elif indicators.ema_5 < indicators.ema_10 < indicators.ema_20:
            ema_score = -2  # 强烈看跌
        elif indicators.ema_5 < indicators.ema_10:
            ema_score = -1  # 轻微看跌
        
        if ema_score > 0:
            bullish_score += ema_score
        else:
            bearish_score += abs(ema_score)
        total_indicators += 2
        
        # 标准化得分
        if total_indicators > 0:
            bullish_ratio = bullish_score / total_indicators
            bearish_ratio = bearish_score / total_indicators
        else:
            bullish_ratio = bearish_ratio = 0.5
        
        return bullish_ratio, bearish_ratio
    
    def _analyze_trend_momentum(self, klines: List[KlineData]) -> Tuple[float, float]:
        """
        分析趋势动量
        
        Args:
            klines: K线数据
            
        Returns:
            (看涨权重, 看跌权重) 0-1之间
        """
        if len(klines) < 10:
            return 0.5, 0.5
        
        recent_klines = klines[-10:]  # 最近10根K线
        
        # 计算价格动量
        prices = [k.close for k in recent_klines]
        price_changes = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        
        positive_changes = [c for c in price_changes if c > 0]
        negative_changes = [c for c in price_changes if c < 0]
        
        # 计算趋势强度
        if len(price_changes) > 0:
            bullish_momentum = len(positive_changes) / len(price_changes)
            bearish_momentum = len(negative_changes) / len(price_changes)
        else:
            bullish_momentum = bearish_momentum = 0.5
        
        # 考虑连续性
        consecutive_bullish = 0
        consecutive_bearish = 0
        
        for kline in reversed(recent_klines):
            if kline.is_bullish:
                consecutive_bullish += 1
                break
            elif kline.is_bearish:
                consecutive_bearish += 1
                break
        
        # 调整权重
        if consecutive_bullish >= 3:
            bullish_momentum += 0.2
        elif consecutive_bearish >= 3:
            bearish_momentum += 0.2
        
        return bullish_momentum, bearish_momentum
    
    def _analyze_volume_confirmation(self, klines: List[KlineData]) -> Tuple[float, float]:
        """
        分析成交量确认
        
        Args:
            klines: K线数据
            
        Returns:
            (看涨权重, 看跌权重) 0-1之间
        """
        if len(klines) < 5:
            return 0.5, 0.5
        
        recent_klines = klines[-5:]
        current_kline = klines[-1]
        
        # 计算平均成交量
        avg_volume = sum(k.volume for k in recent_klines[:-1]) / len(recent_klines[:-1])
        
        # 当前成交量相对于平均成交量的比率
        volume_ratio = current_kline.volume / avg_volume if avg_volume > 0 else 1.0
        
        # 分析成交量与价格变化的关系
        price_change = (current_kline.close - current_kline.open) / current_kline.open
        
        if volume_ratio > 1.5:  # 成交量放大
            if price_change > 0:  # 价格上涨且成交量放大
                return 0.8, 0.2
            elif price_change < 0:  # 价格下跌且成交量放大
                return 0.2, 0.8
        elif volume_ratio < 0.7:  # 成交量萎缩
            # 成交量萎缩通常表示趋势可能反转
            if price_change > 0:
                return 0.3, 0.7  # 价格上涨但成交量萎缩，看跌信号
            elif price_change < 0:
                return 0.7, 0.3  # 价格下跌但成交量萎缩，看涨信号
        
        return 0.5, 0.5


class MultiTimeframeAnalyzer:
    """多时间周期分析器"""
    
    def __init__(self, timeframes: List[str] = None):
        """
        初始化多时间周期分析器
        
        Args:
            timeframes: 要分析的时间周期列表
        """
        self.timeframes = timeframes or ['5m', '15m', '30m', '1h']
        logger.info(f"MultiTimeframeAnalyzer initialized with timeframes: {self.timeframes}", caller=self)
    
    def analyze_multiple_timeframes(self, kline_manager: KlineDataManager,
                              indicator_calculator: TechnicalIndicatorCalculator,
                              probability_analyzer: ProbabilityAnalyzer) -> Dict[str, Dict]:
        """
        分析多个时间周期
        
        Args:
            kline_manager: K线数据管理器
            indicator_calculator: 技术指标计算器
            probability_analyzer: 概率分析器
            
        Returns:
            各时间周期的分析结果
        """
        timeframe_results = {}
        
        for timeframe in self.timeframes:
            klines = kline_manager.get_klines(timeframe, 100)
            
            # 大幅降低数据要求
            min_required = {
                '5m': 3,    # 从50降到3
                '15m': 3,   # 从50降到3  
                '30m': 2,   # 从50降到2
                '1h': 1     # 从50降到1
            }
            
            required_count = min_required.get(timeframe, 3)
            
            if len(klines) < required_count:
                logger.warning(f"Insufficient data for {timeframe} analysis: {len(klines)}", caller=self)
                continue
            
            # 计算技术指标
            indicators = indicator_calculator.calculate_indicators(klines)
            
            # 分析概率
            bullish_prob, bearish_prob = probability_analyzer.analyze_next_candle_probability(klines, indicators)
            
            # 计算信号强度
            signal_strength = max(bullish_prob, bearish_prob)
            dominant_direction = "UP" if bullish_prob > bearish_prob else "DOWN"
            
            # 计算置信度
            confidence = abs(bullish_prob - bearish_prob)
            
            timeframe_results[timeframe] = {
                'bullish_probability': bullish_prob,
                'bearish_probability': bearish_prob,
                'dominant_direction': dominant_direction,
                'signal_strength': signal_strength,
                'confidence': confidence,
                'indicators': indicators,
                'latest_kline': klines[-1] if klines else None
            }
            
            logger.debug(f"{timeframe} analysis: {dominant_direction} {signal_strength:.1f}% confidence={confidence:.1f}%", caller=self)
        
        return timeframe_results


class EventContractSignalGenerator:
    """事件合约信号生成器主类"""
    
    def __init__(self, 
                 signal_threshold: float = 60.0,  # 从90%降到60%
                 min_timeframe_consensus: int = 1,  # 从2降到1
                 confidence_threshold: float = 50.0):  # 从80%降到50%
        """
        初始化信号生成器
        
        Args:
            signal_threshold: 信号生成阈值（概率百分比）
            min_timeframe_consensus: 最少时间周期共识数量
            confidence_threshold: 置信度阈值
        """
        self.signal_threshold = signal_threshold
        self.min_timeframe_consensus = min_timeframe_consensus
        self.confidence_threshold = confidence_threshold
        
        # 初始化组件
        self.kline_manager = KlineDataManager()
        self.indicator_calculator = TechnicalIndicatorCalculator()
        self.probability_analyzer = ProbabilityAnalyzer()
        self.timeframe_analyzer = MultiTimeframeAnalyzer()
        
        logger.info(f"EventContractSignalGenerator initialized: threshold={signal_threshold}%, "
                   f"min_consensus={min_timeframe_consensus}, confidence_threshold={confidence_threshold}%", caller=self)
    
    def add_kline_data(self, timestamp: int, open_price: float, high_price: float, 
                      low_price: float, close_price: float, volume: float):
        """
        添加1分钟K线数据
        
        Args:
            timestamp: 时间戳（毫秒）
            open_price: 开盘价
            high_price: 最高价
            low_price: 最低价
            close_price: 收盘价
            volume: 成交量
        """
        kline = KlineData(
            timestamp=timestamp,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=volume
        )
        
        self.kline_manager.add_minute_kline(kline)
        logger.debug(f"Added kline: {'阳线' if kline.is_bullish else '阴线' if kline.is_bearish else '十字线'} "
                    f"O={open_price} H={high_price} L={low_price} C={close_price}", caller=self)
    
    def generate_signal(self) -> SignalResult:
        """
        生成交易信号
        
        Returns:
            信号生成结果
        """
        logger.info("Starting signal generation...", caller=self)
        
        # 检查是否有足够的数据 - 降低要求到5根K线
        klines_15m = self.kline_manager.get_klines('15m')
        if len(klines_15m) < 5:  # 从10降到5
            logger.warning(f"Insufficient 15m klines for signal generation: {len(klines_15m)}", caller=self)
            return SignalResult(
                has_signal=False,
                direction="NEUTRAL",
                confidence=0.0,
                technical_score=0.0
            )
        
        # 多时间周期分析
        timeframe_analysis = self.timeframe_analyzer.analyze_multiple_timeframes(
            self.kline_manager, self.indicator_calculator, self.probability_analyzer
        )
        
        if not timeframe_analysis:
            logger.warning("No valid timeframe analysis results", caller=self)
            return SignalResult(has_signal=False)
        
        # 分析各时间周期的一致性
        consensus_result = self._analyze_timeframe_consensus(timeframe_analysis)
        
        # 生成最终信号
        signal_result = self._generate_final_signal(timeframe_analysis, consensus_result)
        
        logger.info(f"Signal generated: {signal_result.direction} confidence={signal_result.confidence:.1f}% "
                   f"bullish_prob={signal_result.bullish_probability:.1f}%", caller=self)
        
        return signal_result
    
    def _analyze_timeframe_consensus(self, timeframe_analysis: Dict[str, Dict]) -> Dict:
        """
        分析时间周期共识
        
        Args:
            timeframe_analysis: 各时间周期分析结果
            
        Returns:
            共识分析结果
        """
        up_votes = 0
        down_votes = 0
        total_confidence = 0
        consensus_timeframes = []
        
        for timeframe, analysis in timeframe_analysis.items():
            direction = analysis['dominant_direction']
            confidence = analysis['confidence']
            signal_strength = analysis['signal_strength']
            
            # 降低共识要求：信号强度≥50%且置信度≥40%即可参与共识
            if signal_strength >= 50.0 and confidence >= 40.0:
                consensus_timeframes.append(timeframe)
                total_confidence += confidence
                
                if direction == "UP":
                    up_votes += 1
                elif direction == "DOWN":
                    down_votes += 1
        
        # 计算平均置信度
        avg_confidence = total_confidence / len(consensus_timeframes) if consensus_timeframes else 0
        
        # 确定共识方向 - 降低要求：只要有1个时间周期支持即可
        if up_votes >= self.min_timeframe_consensus and up_votes > down_votes:
            consensus_direction = "UP"
            consensus_strength = up_votes
        elif down_votes >= self.min_timeframe_consensus and down_votes > up_votes:
            consensus_direction = "DOWN"
            consensus_strength = down_votes
        else:
            # 如果没有明显共识，选择票数较多的方向
            if up_votes > down_votes:
                consensus_direction = "UP"
                consensus_strength = up_votes
            elif down_votes > up_votes:
                consensus_direction = "DOWN"
                consensus_strength = down_votes
            else:
                consensus_direction = "NEUTRAL"
                consensus_strength = 0
        
        return {
            'direction': consensus_direction,
            'strength': consensus_strength,
            'confidence': avg_confidence,
            'supporting_timeframes': consensus_timeframes,
            'up_votes': up_votes,
            'down_votes': down_votes
        }
    
    def _generate_final_signal(self, timeframe_analysis: Dict[str, Dict], 
                             consensus_result: Dict) -> SignalResult:
        """
        生成最终信号
        
        Args:
            timeframe_analysis: 时间周期分析结果
            consensus_result: 共识分析结果
            
        Returns:
            最终信号结果
        """
        # 获取15分钟时间周期的分析结果（主要参考）
        primary_analysis = timeframe_analysis.get('15m', {})
        
        if not primary_analysis:
            return SignalResult(has_signal=False)
        
        # 基础概率信息
        bullish_prob = primary_analysis.get('bullish_probability', 50.0)
        bearish_prob = primary_analysis.get('bearish_probability', 50.0)
        
        # 检查是否满足信号生成条件
        has_signal = False
        direction = "NEUTRAL"
        confidence = consensus_result['confidence']
        
        # 降低信号生成条件：看涨概率≥60%或看跌概率≥60%即可生成信号
        if bullish_prob >= self.signal_threshold:
            has_signal = True
            direction = "UP"
            confidence = min(95.0, (bullish_prob + confidence) / 2)  # 综合置信度
            
        elif bearish_prob >= self.signal_threshold:
            has_signal = True
            direction = "DOWN"
            confidence = min(95.0, (bearish_prob + confidence) / 2)  # 综合置信度
        
        # 如果仍然没有信号，进一步降低条件
        if not has_signal:
            # 如果任意方向概率超过55%，也生成信号
            if bullish_prob >= 55.0:
                has_signal = True
                direction = "UP"
                confidence = bullish_prob * 0.8  # 降低置信度
            elif bearish_prob >= 55.0:
                has_signal = True
                direction = "DOWN"
                confidence = bearish_prob * 0.8  # 降低置信度
        
        # 计算技术评分
        technical_score = self._calculate_technical_score(timeframe_analysis)
        
        # 评估风险等级
        risk_level = self._assess_risk_level(timeframe_analysis, consensus_result)
        
        return SignalResult(
            has_signal=has_signal,
            direction=direction,
            confidence=confidence,
            bullish_probability=bullish_prob,
            bearish_probability=bearish_prob,
            supporting_timeframes=consensus_result['supporting_timeframes'],
            technical_score=technical_score,
            risk_level=risk_level,
            timeframe_analysis=timeframe_analysis
        )
    
    def _calculate_technical_score(self, timeframe_analysis: Dict[str, Dict]) -> float:
        """
        计算技术评分
        
        Args:
            timeframe_analysis: 时间周期分析结果
            
        Returns:
            技术评分 (0-100)
        """
        total_score = 0
        timeframe_count = 0
        
        for timeframe, analysis in timeframe_analysis.items():
            indicators = analysis.get('indicators')
            if not indicators:
                continue
            
            score = 0
            
            # RSI评分 (0-20分)
            rsi = indicators.rsi_14
            if 30 <= rsi <= 70:
                score += 20  # 正常区间
            elif 20 <= rsi < 30 or 70 < rsi <= 80:
                score += 15  # 接近极值
            elif rsi < 20 or rsi > 80:
                score += 10  # 极值区间
            
            # MACD评分 (0-20分)
            if abs(indicators.macd_histogram) > 0.01:
                score += 20  # 有明确趋势
            elif abs(indicators.macd_histogram) > 0.005:
                score += 15  # 中等趋势
            else:
                score += 10  # 弱趋势
            
            # 布林带评分 (0-20分)
            bb_pos = indicators.bb_position
            if 0.2 <= bb_pos <= 0.8:
                score += 20  # 正常区间
            elif 0.1 <= bb_pos < 0.2 or 0.8 < bb_pos <= 0.9:
                score += 15  # 接近边界
            else:
                score += 25  # 极值区间，可能反转
            
            # 趋势一致性评分 (0-20分)
            if (indicators.ema_5 > indicators.ema_10 > indicators.ema_20) or \
               (indicators.ema_5 < indicators.ema_10 < indicators.ema_20):
                score += 20  # 趋势明确
            elif (indicators.ema_5 > indicators.ema_20) or (indicators.ema_5 < indicators.ema_20):
                score += 15  # 趋势较明确
            else:
                score += 10  # 趋势不明确
            
            # 成交量评分 (0-20分)
            volume_ratio = indicators.volume_ratio
            if volume_ratio > 1.5:
                score += 20  # 成交量放大
            elif volume_ratio > 1.2:
                score += 15  # 成交量适中放大
            elif volume_ratio > 0.8:
                score += 10  # 成交量正常
            else:
                score += 5   # 成交量萎缩
            
            total_score += score
            timeframe_count += 1
        
        return total_score / timeframe_count if timeframe_count > 0 else 0
    
    def _assess_risk_level(self, timeframe_analysis: Dict[str, Dict], 
                          consensus_result: Dict) -> str:
        """
        评估风险等级
        
        Args:
            timeframe_analysis: 时间周期分析结果
            consensus_result: 共识结果
            
        Returns:
            风险等级: LOW, MEDIUM, HIGH
        """
        risk_factors = 0
        
        # 检查时间周期一致性
        supporting_timeframes = len(consensus_result['supporting_timeframes'])
        if supporting_timeframes < 2:
            risk_factors += 2
        elif supporting_timeframes < 3:
            risk_factors += 1
        
        # 检查置信度
        confidence = consensus_result['confidence']
        if confidence < 70:
            risk_factors += 2
        elif confidence < 80:
            risk_factors += 1
        
        # 检查技术指标极值
        for timeframe, analysis in timeframe_analysis.items():
            indicators = analysis.get('indicators')
            if not indicators:
                continue
            
            # RSI极值检查
            if indicators.rsi_14 > 85 or indicators.rsi_14 < 15:
                risk_factors += 1
            
            # 布林带极值检查
            if indicators.bb_position > 0.95 or indicators.bb_position < 0.05:
                risk_factors += 1
            
            # 波动率检查
            if indicators.volatility > 0.8:
                risk_factors += 1
        
        # 评估风险等级
        if risk_factors <= 2:
            return "LOW"
        elif risk_factors <= 4:
            return "MEDIUM"
        else:
            return "HIGH"
    
    def get_status(self) -> Dict:
        """
        获取信号生成器状态
        
        Returns:
            状态信息字典
        """
        status = {}
        
        for timeframe in ['1m', '5m', '15m', '30m', '1h']:
            klines = self.kline_manager.get_klines(timeframe)
            latest_kline = self.kline_manager.get_latest_kline(timeframe)
            
            status[timeframe] = {
                'total_klines': len(klines),
                'latest_kline': {
                    'timestamp': latest_kline.timestamp if latest_kline else None,
                    'open': latest_kline.open if latest_kline else None,
                    'high': latest_kline.high if latest_kline else None,
                    'low': latest_kline.low if latest_kline else None,
                    'close': latest_kline.close if latest_kline else None,
                    'volume': latest_kline.volume if latest_kline else None,
                    'is_bullish': latest_kline.is_bullish if latest_kline else None,
                    'is_bearish': latest_kline.is_bearish if latest_kline else None
                } if latest_kline else None
            }
        
        return status