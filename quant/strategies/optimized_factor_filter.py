# -*- coding: utf-8 -*-

"""
优化后的因子过滤器
解决潜在信号入场点检测问题
"""

from datetime import datetime
from typing import Dict, List
from dataclasses import dataclass, field


@dataclass
class MinuteKline:
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float


@dataclass
class OptimizedFactorFilter:
    """优化后的因子过滤器 - 更容易触发入场信号"""
    threshold: float = 25.0  # 从40.0降低到25.0
    weights: Dict[str, float] = field(default_factory=lambda: {
        "price_action": 15,  # 从20降低到15
        "volume": 20,        # 从15增加到20
        "momentum": 20,      # 从15增加到20
        "structure": 10,     # 保持不变
        "time_decay": 0,     # 特殊处理
    })
    
    # 更宽松的时间限制
    min_remaining_time: int = 300  # 从600秒降低到300秒（5分钟）

    def evaluate_entry(
        self,
        pending_created_at: datetime,
        now: datetime,
        minute_klines: List[MinuteKline],
        indicators: Dict[str, float],
    ) -> Dict[str, object]:
        """评估是否可入场（优化版）"""
        factors: Dict[str, float] = {}
        
        # 1. 价格行为因子 - 降低门槛
        if len(minute_klines) >= 3:
            pct_changes = [
                (minute_klines[-i].close - minute_klines[-i].open) / minute_klines[-i].open * 100
                for i in range(1, 4)
            ]
            # 降低阈值从0.2%到0.1%
            price_action_score = sum(1 for p in pct_changes if abs(p) > 0.1) / 3 * self.weights["price_action"]
        else:
            price_action_score = 0.0
        factors["price_action"] = round(price_action_score, 2)

        # 2. 成交量因子 - 降低满分要求
        vols = [k.volume for k in minute_klines[-20:]] if minute_klines else []
        if vols:
            avg_vol = sum(vols) / len(vols)
            current_vol = minute_klines[-1].volume
            vol_ratio = current_vol / avg_vol if avg_vol else 1.0
            # 降低满分要求从2倍到1.5倍
            volume_score = min(vol_ratio / 1.5, 1.0) * self.weights["volume"]
        else:
            volume_score = 0.0
        factors["volume"] = round(volume_score, 2)

        # 3. 动能因子 - 更宽松的RSI评估
        rsi = indicators.get("rsi", 50)
        rsi_deviation = abs(rsi - 50)
        # 30点偏离给满分，而不是50点
        momentum_score = min(rsi_deviation / 30, 1.0) * self.weights["momentum"]
        factors["momentum"] = round(momentum_score, 2)

        # 4. K线结构因子 - 保持原有逻辑
        if minute_klines:
            last = minute_klines[-1]
            body = abs(last.close - last.open)
            rng = last.high - last.low if last.high - last.low else 0.0001
            body_ratio = body / rng
            structure_score = min(body_ratio, 1.0) * self.weights["structure"]
        else:
            structure_score = 0.0
        factors["structure"] = round(structure_score, 2)

        # 5. 时间衰减 - 优化时间限制
        remaining = 600 - int((now - pending_created_at).total_seconds())
        if remaining < 0:
            time_decay_score = -10
        elif remaining < 180:  # 3分钟内轻微惩罚
            time_decay_score = -2
        else:
            time_decay_score = 0
        factors["time_decay"] = time_decay_score

        # 计算总分
        total = sum(factors.values())
        # 使用更宽松的时间限制
        recommend = total >= self.threshold and remaining >= self.min_remaining_time
        reason = (
            "满足入场条件" if recommend else f"得分{total:.1f}<阈值{self.threshold}或剩余{remaining}s<{self.min_remaining_time}s"
        )

        return {
            "score": round(total, 2),
            "factors": factors,
            "remaining_time": remaining,
            "recommend_entry": recommend,
            "reason": reason,
        }


# 为了兼容性，创建一个别名
FactorFilter = OptimizedFactorFilter
