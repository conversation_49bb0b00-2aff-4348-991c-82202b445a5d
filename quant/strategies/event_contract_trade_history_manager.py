#!/usr/bin/env python3
"""
事件合约交易历史管理器
统一管理所有交易信号、结果、盈亏数据，生成统计报告
"""
import json
import sqlite3
import pandas as pd
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
import numpy as np

# 尝试导入自定义logger，如果不存在则使用标准logger
try:
    from quant.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

from .event_contract_decision_engine import TradingDecision
from .event_contract_signal_generator_simple import SignalResult
from .event_contract_settlement_checker import ContractRecord


@dataclass
class TradeRecord:
    """完整的交易记录"""
    # 基础信息
    trade_id: str                      # 交易ID
    order_id: str                      # 订单ID
    symbol: str                        # 交易对
    direction: str                     # 方向 (UP/DOWN)
    
    # 信号相关
    signal_timestamp: datetime         # 信号生成时间
    signal_confidence: float           # 信号信心度
    signal_strength: float             # 信号强度
    technical_score: float             # 技术分析得分
    market_condition: str              # 市场条件
    
    # 决策相关
    decision_timestamp: datetime       # 决策时间
    should_trade: bool                 # 是否交易
    bet_amount: float                  # 投注金额
    risk_level: str                    # 风险等级
    decision_reason: str               # 决策原因
    
    # 执行相关
    execution_timestamp: Optional[datetime] = None  # 执行时间
    predicted_price: Optional[float] = None         # 预测价格
    entry_price: Optional[float] = None             # 入场价格
    
    # 结算相关
    settlement_timestamp: Optional[datetime] = None  # 结算时间
    final_price: Optional[float] = None              # 最终价格
    result: Optional[str] = None                     # 结果 (win/loss/tie)
    pnl: Optional[float] = None                      # 盈亏
    return_rate: Optional[float] = None              # 收益率
    
    # 状态
    status: str = "pending"            # 状态 (pending/executed/settled/cancelled)
    
    # 附加信息
    notes: str = ""                    # 备注
    created_at: datetime = None        # 创建时间
    updated_at: datetime = None        # 更新时间
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


@dataclass
class TradingPerformanceMetrics:
    """交易绩效指标"""
    # 基础统计
    total_trades: int = 0
    completed_trades: int = 0
    pending_trades: int = 0
    cancelled_trades: int = 0
    
    # 结果统计
    wins: int = 0
    losses: int = 0
    ties: int = 0
    win_rate: float = 0.0
    
    # 盈亏统计
    total_pnl: float = 0.0
    total_invested: float = 0.0
    avg_bet_amount: float = 0.0
    avg_win_amount: float = 0.0
    avg_loss_amount: float = 0.0
    max_win: float = 0.0
    max_loss: float = 0.0
    
    # 连胜连败
    current_streak: int = 0
    max_win_streak: int = 0
    max_loss_streak: int = 0
    
    # 风险指标
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    volatility: float = 0.0
    
    # 时间统计
    avg_trade_duration: float = 0.0    # 平均交易持续时间（分钟）
    total_trading_time: float = 0.0    # 总交易时间（小时）
    
    # 按方向统计
    up_trades: int = 0
    down_trades: int = 0
    up_win_rate: float = 0.0
    down_win_rate: float = 0.0


@dataclass
class PeriodStats:
    """时期统计"""
    period: str                        # 时期 (daily/weekly/monthly)
    date: str                          # 日期
    trades: int = 0
    wins: int = 0
    losses: int = 0
    ties: int = 0
    pnl: float = 0.0
    invested: float = 0.0
    win_rate: float = 0.0
    roi: float = 0.0                   # 投资回报率


class EventContractTradeHistoryManager:
    """事件合约交易历史管理器"""
    
    def __init__(self, db_path: str = "data/trade_history.db"):
        """
        初始化交易历史管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.trades: Dict[str, TradeRecord] = {}
        
        # 确保数据目录存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        # 加载现有数据
        self._load_trades()
        
        logger.info(f"交易历史管理器已初始化，数据库: {db_path}")
    
    def _init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建交易记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trade_records (
                    trade_id TEXT PRIMARY KEY,
                    order_id TEXT,
                    symbol TEXT,
                    direction TEXT,
                    signal_timestamp TEXT,
                    signal_confidence REAL,
                    signal_strength REAL,
                    technical_score REAL,
                    market_condition TEXT,
                    decision_timestamp TEXT,
                    should_trade INTEGER,
                    bet_amount REAL,
                    risk_level TEXT,
                    decision_reason TEXT,
                    execution_timestamp TEXT,
                    predicted_price REAL,
                    entry_price REAL,
                    settlement_timestamp TEXT,
                    final_price REAL,
                    result TEXT,
                    pnl REAL,
                    return_rate REAL,
                    status TEXT,
                    notes TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_timestamp ON trade_records(signal_timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_status ON trade_records(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_result ON trade_records(result)")
            
            conn.commit()
    
    def _load_trades(self):
        """从数据库加载交易记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM trade_records ORDER BY created_at DESC")
            
            for row in cursor.fetchall():
                trade_record = self._row_to_trade_record(row)
                self.trades[trade_record.trade_id] = trade_record
        
        logger.info(f"已加载 {len(self.trades)} 条交易记录")
    
    def _row_to_trade_record(self, row) -> TradeRecord:
        """将数据库行转换为交易记录"""
        return TradeRecord(
            trade_id=row[0],
            order_id=row[1],
            symbol=row[2],
            direction=row[3],
            signal_timestamp=datetime.fromisoformat(row[4]),
            signal_confidence=row[5],
            signal_strength=row[6],
            technical_score=row[7],
            market_condition=row[8],
            decision_timestamp=datetime.fromisoformat(row[9]),
            should_trade=bool(row[10]),
            bet_amount=row[11],
            risk_level=row[12],
            decision_reason=row[13],
            execution_timestamp=datetime.fromisoformat(row[14]) if row[14] else None,
            predicted_price=row[15],
            entry_price=row[16],
            settlement_timestamp=datetime.fromisoformat(row[17]) if row[17] else None,
            final_price=row[18],
            result=row[19],
            pnl=row[20],
            return_rate=row[21],
            status=row[22],
            notes=row[23],
            created_at=datetime.fromisoformat(row[24]),
            updated_at=datetime.fromisoformat(row[25])
        )
    
    def create_trade_record(self, 
                          signal_result: SignalResult,
                          decision: TradingDecision,
                          trade_id: str = None) -> TradeRecord:
        """
        创建新的交易记录
        
        Args:
            signal_result: 信号结果
            decision: 交易决策
            trade_id: 交易ID，如果为None则自动生成
            
        Returns:
            TradeRecord: 创建的交易记录
        """
        if trade_id is None:
            trade_id = f"TRADE_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        trade_record = TradeRecord(
            trade_id=trade_id,
            order_id="",  # 将在执行时填入
            symbol="BTCUSDT",
            direction=decision.direction,
            signal_timestamp=datetime.now(),
            signal_confidence=signal_result.confidence,
            signal_strength=signal_result.confidence,
            technical_score=signal_result.technical_score,
            market_condition=decision.market_condition.value if hasattr(decision.market_condition, 'value') else str(decision.market_condition),
            decision_timestamp=decision.timestamp,
            should_trade=decision.should_trade,
            bet_amount=decision.bet_amount,
            risk_level=decision.risk_level.value if hasattr(decision.risk_level, 'value') else str(decision.risk_level),
            decision_reason=decision.reason,
            status="pending"
        )
        
        self.trades[trade_id] = trade_record
        self._save_trade_record(trade_record)
        
        logger.info(f"创建交易记录: {trade_id} - {decision.direction} - {decision.bet_amount:.2f} USDT")
        return trade_record
    
    def update_execution(self, 
                        trade_id: str,
                        order_id: str,
                        predicted_price: float,
                        entry_price: float) -> bool:
        """
        更新交易执行信息
        
        Args:
            trade_id: 交易ID
            order_id: 订单ID
            predicted_price: 预测价格
            entry_price: 入场价格
            
        Returns:
            bool: 是否更新成功
        """
        if trade_id not in self.trades:
            logger.error(f"交易记录不存在: {trade_id}")
            return False
        
        trade_record = self.trades[trade_id]
        trade_record.order_id = order_id
        trade_record.predicted_price = predicted_price
        trade_record.entry_price = entry_price
        trade_record.execution_timestamp = datetime.now()
        trade_record.status = "executed"
        trade_record.updated_at = datetime.now()
        
        self._save_trade_record(trade_record)
        
        logger.info(f"更新交易执行: {trade_id} - 订单ID: {order_id}")
        return True
    
    def update_settlement(self, 
                         trade_id: str,
                         final_price: float,
                         result: str,
                         pnl: float) -> bool:
        """
        更新交易结算信息
        
        Args:
            trade_id: 交易ID
            final_price: 最终价格
            result: 结果 (win/loss/tie)
            pnl: 盈亏
            
        Returns:
            bool: 是否更新成功
        """
        if trade_id not in self.trades:
            logger.error(f"交易记录不存在: {trade_id}")
            return False
        
        trade_record = self.trades[trade_id]
        trade_record.final_price = final_price
        trade_record.result = result
        trade_record.pnl = pnl
        trade_record.return_rate = (pnl / trade_record.bet_amount) if trade_record.bet_amount > 0 else 0.0
        trade_record.settlement_timestamp = datetime.now()
        trade_record.status = "settled"
        trade_record.updated_at = datetime.now()
        
        self._save_trade_record(trade_record)
        
        logger.info(f"更新交易结算: {trade_id} - 结果: {result} - 盈亏: {pnl:.2f} USDT")
        return True
    
    def update_from_contract_record(self, contract_record: ContractRecord) -> bool:
        """
        从合约记录更新交易信息
        
        Args:
            contract_record: 合约记录
            
        Returns:
            bool: 是否更新成功
        """
        # 通过order_id查找对应的交易记录
        trade_record = None
        for record in self.trades.values():
            if record.order_id == contract_record.order_id:
                trade_record = record
                break
        
        if not trade_record:
            logger.warning(f"未找到对应的交易记录: {contract_record.order_id}")
            return False
        
        # 更新结算信息
        if contract_record.is_settled:
            return self.update_settlement(
                trade_record.trade_id,
                contract_record.final_price,
                contract_record.result,
                contract_record.pnl
            )
        else:
            # 更新执行信息
            return self.update_execution(
                trade_record.trade_id,
                contract_record.order_id,
                contract_record.predicted_price,
                contract_record.predicted_price  # 使用预测价格作为入场价格
            )
    
    def _save_trade_record(self, trade_record: TradeRecord):
        """保存交易记录到数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO trade_records VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
            """, (
                trade_record.trade_id,
                trade_record.order_id,
                trade_record.symbol,
                trade_record.direction,
                trade_record.signal_timestamp.isoformat(),
                trade_record.signal_confidence,
                trade_record.signal_strength,
                trade_record.technical_score,
                trade_record.market_condition,
                trade_record.decision_timestamp.isoformat(),
                int(trade_record.should_trade),
                trade_record.bet_amount,
                trade_record.risk_level,
                trade_record.decision_reason,
                trade_record.execution_timestamp.isoformat() if trade_record.execution_timestamp else None,
                trade_record.predicted_price,
                trade_record.entry_price,
                trade_record.settlement_timestamp.isoformat() if trade_record.settlement_timestamp else None,
                trade_record.final_price,
                trade_record.result,
                trade_record.pnl,
                trade_record.return_rate,
                trade_record.status,
                trade_record.notes,
                trade_record.created_at.isoformat(),
                trade_record.updated_at.isoformat()
            ))
            
            conn.commit()
    
    def get_trade_record(self, trade_id: str) -> Optional[TradeRecord]:
        """获取交易记录"""
        return self.trades.get(trade_id)
    
    def get_trade_records(self, 
                         status: str = None,
                         result: str = None,
                         start_date: date = None,
                         end_date: date = None,
                         limit: int = None) -> List[TradeRecord]:
        """
        获取交易记录列表
        
        Args:
            status: 状态过滤
            result: 结果过滤
            start_date: 开始日期
            end_date: 结束日期
            limit: 数量限制
            
        Returns:
            List[TradeRecord]: 交易记录列表
        """
        records = list(self.trades.values())
        
        # 按创建时间排序
        records.sort(key=lambda x: x.created_at, reverse=True)
        
        # 过滤条件
        if status:
            records = [r for r in records if r.status == status]
        
        if result:
            records = [r for r in records if r.result == result]
        
        if start_date:
            records = [r for r in records if r.signal_timestamp.date() >= start_date]
        
        if end_date:
            records = [r for r in records if r.signal_timestamp.date() <= end_date]
        
        if limit:
            records = records[:limit]
        
        return records
    
    def calculate_performance_metrics(self, 
                                    start_date: date = None,
                                    end_date: date = None) -> TradingPerformanceMetrics:
        """
        计算交易绩效指标
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            TradingPerformanceMetrics: 绩效指标
        """
        records = self.get_trade_records(start_date=start_date, end_date=end_date)
        
        if not records:
            return TradingPerformanceMetrics()
        
        metrics = TradingPerformanceMetrics()
        
        # 基础统计
        metrics.total_trades = len(records)
        metrics.completed_trades = len([r for r in records if r.status == "settled"])
        metrics.pending_trades = len([r for r in records if r.status in ["pending", "executed"]])
        metrics.cancelled_trades = len([r for r in records if r.status == "cancelled"])
        
        # 只统计已结算的交易
        settled_records = [r for r in records if r.status == "settled" and r.result]
        
        if not settled_records:
            return metrics
        
        # 结果统计
        metrics.wins = len([r for r in settled_records if r.result == "win"])
        metrics.losses = len([r for r in settled_records if r.result == "loss"])
        metrics.ties = len([r for r in settled_records if r.result == "tie"])
        metrics.win_rate = metrics.wins / len(settled_records) if settled_records else 0.0
        
        # 盈亏统计
        pnls = [r.pnl for r in settled_records if r.pnl is not None]
        bet_amounts = [r.bet_amount for r in settled_records if r.bet_amount is not None]
        
        if pnls:
            metrics.total_pnl = sum(pnls)
            metrics.max_win = max(pnls)
            metrics.max_loss = min(pnls)
        
        if bet_amounts:
            metrics.total_invested = sum(bet_amounts)
            metrics.avg_bet_amount = sum(bet_amounts) / len(bet_amounts)
        
        # 平均盈亏
        win_pnls = [r.pnl for r in settled_records if r.result == "win" and r.pnl is not None]
        loss_pnls = [r.pnl for r in settled_records if r.result == "loss" and r.pnl is not None]
        
        if win_pnls:
            metrics.avg_win_amount = sum(win_pnls) / len(win_pnls)
        
        if loss_pnls:
            metrics.avg_loss_amount = sum(loss_pnls) / len(loss_pnls)
        
        # 连胜连败
        metrics.current_streak = self._calculate_current_streak(settled_records)
        metrics.max_win_streak, metrics.max_loss_streak = self._calculate_max_streaks(settled_records)
        
        # 按方向统计
        up_records = [r for r in settled_records if r.direction == "UP"]
        down_records = [r for r in settled_records if r.direction == "DOWN"]
        
        metrics.up_trades = len(up_records)
        metrics.down_trades = len(down_records)
        
        if up_records:
            up_wins = len([r for r in up_records if r.result == "win"])
            metrics.up_win_rate = up_wins / len(up_records)
        
        if down_records:
            down_wins = len([r for r in down_records if r.result == "win"])
            metrics.down_win_rate = down_wins / len(down_records)
        
        # 时间统计
        durations = []
        for record in settled_records:
            if record.execution_timestamp and record.settlement_timestamp:
                duration = (record.settlement_timestamp - record.execution_timestamp).total_seconds() / 60
                durations.append(duration)
        
        if durations:
            metrics.avg_trade_duration = sum(durations) / len(durations)
            metrics.total_trading_time = sum(durations) / 60  # 转换为小时
        
        # 风险指标
        if pnls and len(pnls) > 1:
            metrics.volatility = np.std(pnls)
            if metrics.volatility > 0:
                metrics.sharpe_ratio = np.mean(pnls) / metrics.volatility
            
            # 最大回撤
            cumulative_pnl = np.cumsum(pnls)
            running_max = np.maximum.accumulate(cumulative_pnl)
            drawdown = running_max - cumulative_pnl
            metrics.max_drawdown = np.max(drawdown)
        
        return metrics
    
    def _calculate_current_streak(self, records: List[TradeRecord]) -> int:
        """计算当前连胜/连败"""
        if not records:
            return 0
        
        # 按时间排序
        sorted_records = sorted(records, key=lambda x: x.settlement_timestamp or datetime.min)
        
        current_streak = 0
        for record in reversed(sorted_records):
            if record.result == "win":
                if current_streak >= 0:
                    current_streak += 1
                else:
                    break
            elif record.result == "loss":
                if current_streak <= 0:
                    current_streak -= 1
                else:
                    break
            # 平局不影响连胜连败
        
        return current_streak
    
    def _calculate_max_streaks(self, records: List[TradeRecord]) -> Tuple[int, int]:
        """计算最大连胜和最大连败"""
        if not records:
            return 0, 0
        
        # 按时间排序
        sorted_records = sorted(records, key=lambda x: x.settlement_timestamp or datetime.min)
        
        max_win_streak = 0
        max_loss_streak = 0
        current_win_streak = 0
        current_loss_streak = 0
        
        for record in sorted_records:
            if record.result == "win":
                current_win_streak += 1
                current_loss_streak = 0
                max_win_streak = max(max_win_streak, current_win_streak)
            elif record.result == "loss":
                current_loss_streak += 1
                current_win_streak = 0
                max_loss_streak = max(max_loss_streak, current_loss_streak)
            # 平局不影响连胜连败
        
        return max_win_streak, max_loss_streak
    
    def get_daily_stats(self, target_date: date = None) -> PeriodStats:
        """获取每日统计"""
        if target_date is None:
            target_date = date.today()
        
        records = self.get_trade_records(
            start_date=target_date,
            end_date=target_date,
            status="settled"
        )
        
        stats = PeriodStats(
            period="daily",
            date=target_date.isoformat()
        )
        
        if not records:
            return stats
        
        stats.trades = len(records)
        stats.wins = len([r for r in records if r.result == "win"])
        stats.losses = len([r for r in records if r.result == "loss"])
        stats.ties = len([r for r in records if r.result == "tie"])
        stats.pnl = sum(r.pnl for r in records if r.pnl is not None)
        stats.invested = sum(r.bet_amount for r in records if r.bet_amount is not None)
        stats.win_rate = stats.wins / stats.trades if stats.trades > 0 else 0.0
        stats.roi = (stats.pnl / stats.invested) if stats.invested > 0 else 0.0
        
        return stats
    
    def get_weekly_stats(self, target_date: date = None) -> PeriodStats:
        """获取每周统计"""
        if target_date is None:
            target_date = date.today()
        
        # 计算周的开始和结束日期
        start_date = target_date - timedelta(days=target_date.weekday())
        end_date = start_date + timedelta(days=6)
        
        records = self.get_trade_records(
            start_date=start_date,
            end_date=end_date,
            status="settled"
        )
        
        stats = PeriodStats(
            period="weekly",
            date=f"{start_date.isoformat()}_to_{end_date.isoformat()}"
        )
        
        if not records:
            return stats
        
        stats.trades = len(records)
        stats.wins = len([r for r in records if r.result == "win"])
        stats.losses = len([r for r in records if r.result == "loss"])
        stats.ties = len([r for r in records if r.result == "tie"])
        stats.pnl = sum(r.pnl for r in records if r.pnl is not None)
        stats.invested = sum(r.bet_amount for r in records if r.bet_amount is not None)
        stats.win_rate = stats.wins / stats.trades if stats.trades > 0 else 0.0
        stats.roi = (stats.pnl / stats.invested) if stats.invested > 0 else 0.0
        
        return stats
    
    def get_monthly_stats(self, target_date: date = None) -> PeriodStats:
        """获取每月统计"""
        if target_date is None:
            target_date = date.today()
        
        # 计算月的开始和结束日期
        start_date = target_date.replace(day=1)
        if target_date.month == 12:
            end_date = target_date.replace(year=target_date.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_date = target_date.replace(month=target_date.month + 1, day=1) - timedelta(days=1)
        
        records = self.get_trade_records(
            start_date=start_date,
            end_date=end_date,
            status="settled"
        )
        
        stats = PeriodStats(
            period="monthly",
            date=f"{target_date.year}-{target_date.month:02d}"
        )
        
        if not records:
            return stats
        
        stats.trades = len(records)
        stats.wins = len([r for r in records if r.result == "win"])
        stats.losses = len([r for r in records if r.result == "loss"])
        stats.ties = len([r for r in records if r.result == "tie"])
        stats.pnl = sum(r.pnl for r in records if r.pnl is not None)
        stats.invested = sum(r.bet_amount for r in records if r.bet_amount is not None)
        stats.win_rate = stats.wins / stats.trades if stats.trades > 0 else 0.0
        stats.roi = (stats.pnl / stats.invested) if stats.invested > 0 else 0.0
        
        return stats
    
    def export_to_csv(self, filepath: str = None, start_date: date = None, end_date: date = None) -> str:
        """
        导出交易记录到CSV文件
        
        Args:
            filepath: 文件路径
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            str: 导出的文件路径
        """
        if filepath is None:
            filepath = f"exports/trade_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        # 确保目录存在
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        records = self.get_trade_records(start_date=start_date, end_date=end_date)
        
        if not records:
            logger.warning("没有交易记录可导出")
            return filepath
        
        # 转换为DataFrame
        data = []
        for record in records:
            data.append({
                '交易ID': record.trade_id,
                '订单ID': record.order_id,
                '交易对': record.symbol,
                '方向': record.direction,
                '信号时间': record.signal_timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                '信号信心度': record.signal_confidence,
                '技术得分': record.technical_score,
                '市场条件': record.market_condition,
                '决策时间': record.decision_timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                '是否交易': record.should_trade,
                '投注金额': record.bet_amount,
                '风险等级': record.risk_level,
                '决策原因': record.decision_reason,
                '执行时间': record.execution_timestamp.strftime('%Y-%m-%d %H:%M:%S') if record.execution_timestamp else '',
                '预测价格': record.predicted_price,
                '入场价格': record.entry_price,
                '结算时间': record.settlement_timestamp.strftime('%Y-%m-%d %H:%M:%S') if record.settlement_timestamp else '',
                '最终价格': record.final_price,
                '结果': record.result,
                '盈亏': record.pnl,
                '收益率': f"{record.return_rate:.2%}" if record.return_rate else '',
                '状态': record.status,
                '备注': record.notes
            })
        
        df = pd.DataFrame(data)
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        logger.info(f"交易记录已导出到: {filepath}")
        return filepath
    
    def export_to_json(self, filepath: str = None, start_date: date = None, end_date: date = None) -> str:
        """
        导出交易记录到JSON文件
        
        Args:
            filepath: 文件路径
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            str: 导出的文件路径
        """
        if filepath is None:
            filepath = f"exports/trade_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 确保目录存在
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        records = self.get_trade_records(start_date=start_date, end_date=end_date)
        metrics = self.calculate_performance_metrics(start_date=start_date, end_date=end_date)
        
        # 准备导出数据
        export_data = {
            'export_info': {
                'timestamp': datetime.now().isoformat(),
                'total_records': len(records),
                'date_range': {
                    'start': start_date.isoformat() if start_date else None,
                    'end': end_date.isoformat() if end_date else None
                }
            },
            'performance_metrics': asdict(metrics),
            'trade_records': []
        }
        
        # 转换交易记录
        for record in records:
            record_dict = asdict(record)
            # 转换datetime对象为字符串
            for key, value in record_dict.items():
                if isinstance(value, datetime):
                    record_dict[key] = value.isoformat()
            export_data['trade_records'].append(record_dict)
        
        # 保存到文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"交易记录已导出到: {filepath}")
        return filepath
    
    def generate_performance_report(self, 
                                  start_date: date = None,
                                  end_date: date = None,
                                  save_to_file: bool = True) -> str:
        """
        生成绩效报告
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            save_to_file: 是否保存到文件
            
        Returns:
            str: 报告内容
        """
        metrics = self.calculate_performance_metrics(start_date=start_date, end_date=end_date)
        
        # 构建报告
        report = []
        report.append("=" * 60)
        report.append("📊 事件合约交易绩效报告")
        report.append("=" * 60)
        
        # 基本信息
        report.append(f"📅 报告时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        if start_date:
            report.append(f"📅 统计期间: {start_date} 至 {end_date or date.today()}")
        else:
            report.append("📅 统计期间: 全部历史数据")
        report.append("")
        
        # 交易概览
        report.append("🎯 交易概览")
        report.append("-" * 30)
        report.append(f"总交易数: {metrics.total_trades}")
        report.append(f"已完成交易: {metrics.completed_trades}")
        report.append(f"待处理交易: {metrics.pending_trades}")
        report.append(f"已取消交易: {metrics.cancelled_trades}")
        report.append("")
        
        # 盈亏统计
        report.append("💰 盈亏统计")
        report.append("-" * 30)
        report.append(f"胜利次数: {metrics.wins}")
        report.append(f"失败次数: {metrics.losses}")
        report.append(f"平局次数: {metrics.ties}")
        report.append(f"胜率: {metrics.win_rate:.2%}")
        report.append(f"总盈亏: {metrics.total_pnl:+.2f} USDT")
        report.append(f"总投入: {metrics.total_invested:.2f} USDT")
        report.append(f"投资回报率: {(metrics.total_pnl/metrics.total_invested)*100:.2f}%" if metrics.total_invested > 0 else "投资回报率: 0.00%")
        report.append("")
        
        # 交易分析
        report.append("📈 交易分析")
        report.append("-" * 30)
        report.append(f"平均投注金额: {metrics.avg_bet_amount:.2f} USDT")
        report.append(f"平均盈利金额: {metrics.avg_win_amount:.2f} USDT")
        report.append(f"平均亏损金额: {metrics.avg_loss_amount:.2f} USDT")
        report.append(f"最大单笔盈利: {metrics.max_win:.2f} USDT")
        report.append(f"最大单笔亏损: {metrics.max_loss:.2f} USDT")
        report.append("")
        
        # 连胜连败
        report.append("🔥 连胜连败分析")
        report.append("-" * 30)
        if metrics.current_streak > 0:
            report.append(f"当前连胜: {metrics.current_streak} 次")
        elif metrics.current_streak < 0:
            report.append(f"当前连败: {abs(metrics.current_streak)} 次")
        else:
            report.append("当前连胜连败: 0 次")
        report.append(f"最大连胜: {metrics.max_win_streak} 次")
        report.append(f"最大连败: {metrics.max_loss_streak} 次")
        report.append("")
        
        # 方向分析
        report.append("📊 方向分析")
        report.append("-" * 30)
        report.append(f"看涨交易: {metrics.up_trades} 次，胜率: {metrics.up_win_rate:.2%}")
        report.append(f"看跌交易: {metrics.down_trades} 次，胜率: {metrics.down_win_rate:.2%}")
        report.append("")
        
        # 风险指标
        report.append("⚠️ 风险指标")
        report.append("-" * 30)
        report.append(f"夏普比率: {metrics.sharpe_ratio:.4f}")
        report.append(f"最大回撤: {metrics.max_drawdown:.2f} USDT")
        report.append(f"波动率: {metrics.volatility:.2f}")
        report.append("")
        
        # 时间分析
        report.append("⏰ 时间分析")
        report.append("-" * 30)
        report.append(f"平均交易持续时间: {metrics.avg_trade_duration:.1f} 分钟")
        report.append(f"总交易时间: {metrics.total_trading_time:.1f} 小时")
        report.append("")
        
        # 最近表现
        recent_stats = self.get_daily_stats()
        report.append("📅 今日表现")
        report.append("-" * 30)
        report.append(f"今日交易: {recent_stats.trades} 次")
        report.append(f"今日胜率: {recent_stats.win_rate:.2%}")
        report.append(f"今日盈亏: {recent_stats.pnl:+.2f} USDT")
        report.append(f"今日投资回报率: {recent_stats.roi:.2%}")
        report.append("")
        
        report.append("=" * 60)
        
        report_text = "\n".join(report)
        
        if save_to_file:
            filepath = f"reports/performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_text)
            
            logger.info(f"绩效报告已保存到: {filepath}")
        
        return report_text
    
    def cleanup_old_records(self, days_to_keep: int = 90):
        """
        清理旧的交易记录
        
        Args:
            days_to_keep: 保留的天数
        """
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        # 从内存中删除
        to_delete = []
        for trade_id, record in self.trades.items():
            if record.created_at < cutoff_date:
                to_delete.append(trade_id)
        
        for trade_id in to_delete:
            del self.trades[trade_id]
        
        # 从数据库中删除
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "DELETE FROM trade_records WHERE created_at < ?",
                (cutoff_date.isoformat(),)
            )
            conn.commit()
        
        logger.info(f"清理了 {len(to_delete)} 条旧交易记录")
    
    def get_status(self) -> Dict:
        """获取状态信息"""
        return {
            'total_trades': len(self.trades),
            'database_path': self.db_path,
            'latest_trade': max(self.trades.values(), key=lambda x: x.created_at).created_at.isoformat() if self.trades else None,
            'status_distribution': {
                'pending': len([r for r in self.trades.values() if r.status == "pending"]),
                'executed': len([r for r in self.trades.values() if r.status == "executed"]),
                'settled': len([r for r in self.trades.values() if r.status == "settled"]),
                'cancelled': len([r for r in self.trades.values() if r.status == "cancelled"])
            }
        } 