"""
币安事件合约钉钉通知集成模块（修复版）
发送交易信号、结算通知、风险提醒到钉钉群组
"""
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import logging

# 尝试导入自定义logger，如果不存在则使用标准logger
try:
    from quant.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

from quant.utils.dingtalk import Dingtalk
from quant.config import config
from .event_contract_decision_engine import TradingDecision, RiskLevel
from .event_contract_signal_generator_simple import SignalResult


@dataclass
class NotificationConfig:
    """通知配置"""
    enable_signal_notification: bool = True      # 启用交易信号通知
    enable_settlement_notification: bool = True  # 启用结算通知
    enable_risk_notification: bool = True        # 启用风险提醒
    enable_daily_summary: bool = True            # 启用日报
    
    # 钉钉Webhook URL (如果为None，则使用全局配置)
    dingtalk_token: Optional[str] = None
    
    # 通知频率控制
    min_signal_interval: int = 300  # 最小信号通知间隔（秒）
    max_daily_notifications: int = 50  # 每日最大通知数
    
    # 风险提醒阈值
    risk_notification_threshold: float = 0.5  # 损失比例阈值
    consecutive_loss_threshold: int = 3       # 连续亏损阈值


class EventContractDingtalkNotifier:
    """事件合约钉钉通知器"""
    
    def __init__(self, config_obj: Optional[NotificationConfig] = None):
        """
        初始化通知器
        
        Args:
            config_obj: 通知配置，如果为None则使用默认配置
        """
        self.config = config_obj or NotificationConfig()
        self.last_notification_time = None
        self.daily_notification_count = 0
        self.notification_history = []
        
        # 如果配置中没有指定钉钉token，则使用全局配置
        if not self.config.dingtalk_token:
            self.config.dingtalk_token = config.dingtalk
        
        logger.info(f"事件合约钉钉通知器已初始化，Token: {self.config.dingtalk_token is not None}")
    
    def _build_pending_signal_message(self, signal_result: SignalResult, market_data: Optional[Dict] = None) -> str:
        """构建pending信号消息（第一阶段）"""
        # 根据方向选择图标
        direction_icon = "🚀" if signal_result.direction == "UP" else "📉"
        
        # 获取准确的K线序号信息
        kline_sequence = market_data.get('kline_sequence', 0) if market_data else 0
        signal_count = market_data.get('signal_count', 0) if market_data else 0
        kline_time = market_data.get('kline_time', '未知') if market_data else '未知'
        
        # 获取信号ID和价格信息
        signal_id = market_data.get('signal_id', '未分配') if market_data else '未分配'
        signal_price = market_data.get('signal_price', 0.0) if market_data else 0.0
        
        # 构建清晰的消息格式
        message = f"🔔 **潜在信号检测** {direction_icon}\n\n"
        
        # 信号ID和价格信息
        message += f"🆔 信号ID: {signal_id}\n"
        message += f"💰 信号价格: {signal_price:.2f} USDT\n\n"
        
        # K线和信号序号信息
        message += f"📊 K线序号: 第{kline_sequence}根15分钟K线 ({kline_time})\n"
        message += f"🔢 信号序号: 今日第{signal_count}个信号\n\n"
        
        # 交易信息
        message += f"📈 交易方向: {signal_result.direction} {direction_icon}\n"
        message += f"🎯 置信度: {signal_result.confidence:.1f}%\n"
        message += f"📊 技术分: {signal_result.technical_score:.1f}分\n"
        message += f"⚠️ 风险等级: {signal_result.risk_level}\n\n"
        
        # 市场条件
        market_status = getattr(signal_result, 'market_status', None)
        if market_status and market_status != "":
            message += f"🌟 市场条件: {market_status}\n"
        else:
            # 根据置信度推断市场条件
            if signal_result.confidence >= 70:
                market_condition = "强势信号"
            elif signal_result.confidence >= 60:
                market_condition = "中等信号"
            elif signal_result.confidence >= 50:
                market_condition = "弱势信号"
            else:
                market_condition = "信号待确认"
            message += f"🌟 市场条件: {market_condition}\n"
        
        # 决策原因
        reason = f"满足交易条件: 信心度{signal_result.confidence:.1f}%, 风险{signal_result.risk_level}"
        message += f"💡 决策原因: {reason}\n\n"
        
        # 市场提醒
        if hasattr(signal_result, 'user_reminder') and signal_result.user_reminder:
            message += f"🔍 市场提醒: 【交易信号提醒】📈 检测到信号！建议关注交易机会\n\n"
        
        # 时间戳
        message += f"⏰ 信号时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        # K线进度信息
        message += f"📈 今日进度: {kline_sequence}/96 (15分钟K线)\n\n"
        
        # 等待提醒
        message += f"⏳ 等待入场时机评估... 系统将在接下来的15分钟内寻找最佳入场点\n\n"
        
        # 跟踪提醒
        message += f"🏷️ 跟踪提醒: 请记住信号ID [{signal_id}], 用于结算通知对应\n\n"
        
        # 小火箭祝福
        message += f"🎉 祝**交易**顺利！{direction_icon}小火箭{direction_icon}起飞！"
        
        return message
    
    def _build_recommendation_message(self, rec: Dict) -> str:
        """构造推荐消息文本。"""
        direction_emoji = "🚀" if rec.get("direction") == "UP" else "📉"
        stake = rec.get("stake", 0)
        confidence = rec.get("confidence", 0)
        score = rec.get("score", 0)
        remaining = rec.get("remaining_time", "-")
        
        # 获取信号ID和价格信息
        signal_id = getattr(rec.get("signal_result", None), "signal_id", None) or "未找到"
        signal_price = getattr(rec.get("signal_result", None), "signal_price", 0.0)
        
        # 如果没有从signal_result获取到价格，尝试从recommendation中获取
        if signal_price <= 0:
            signal_price = rec.get("signal_price", 0.0)
        
        message = f"🎯 **事件合约交易推荐** {direction_emoji}\n\n"
        
        # 基础信息
        message += f"🆔 信号ID: {signal_id}\n"
        message += f"💰 信号价格: {signal_price:.2f} USDT\n\n"
        
        # 交易详情
        message += f"📈 方向: **{rec.get('direction')}**\n"
        message += f"💵 建议投入: **{stake} USDT**\n"
        message += f"🎯 置信度: **{confidence:.1f}%**\n"
        message += f"📊 因子得分: **{score:.1f}/100**\n"
        message += f"⏰ 剩余时间: {remaining}s\n"
        message += f"🕒 生成时间: {rec.get('generated_at')}\n\n"
        
        # 提示信息
        message += f"💡 该推荐仅供参考，实际操作请自行评估风控。"
        
        return message
    
    def send_pending_signal(self, signal_result: SignalResult, market_data: Optional[Dict] = None) -> Tuple[bool, Optional[str]]:
        """
        发送pending信号提醒（第一阶段）
        
        Args:
            signal_result: 信号生成结果
            market_data: 市场数据
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_signal_notification:
            return True, None
        
        # 检查通知频率限制
        if not self._check_notification_limit():
            return False, "通知频率超限"
        
        # 构建pending信号消息
        message = self._build_pending_signal_message(signal_result, market_data)
        
        # 发送通知
        success, error = self._send_dingtalk_message(message)
        
        if success:
            self._record_notification("pending_signal", signal_result.direction)
            logger.info(f"Pending信号通知已发送: {signal_result.direction}")
        else:
            logger.error(f"Pending信号通知发送失败: {error}")
        
        return success, error
    
    def send_recommendation(self, recommendation: Dict) -> Tuple[bool, Optional[str]]:
        """发送最终交易推荐（无需 TradingDecision）。"""
        if not self.config.enable_signal_notification:
            return True, None

        # 频率检查
        if not self._check_notification_limit():
            return False, "通知频率超限"

        message = self._build_recommendation_message(recommendation)
        success, error = self._send_dingtalk_message(message)
        if success:
            self._record_notification("recommendation", recommendation.get("direction", ""))
        return success, error
    
    def _send_dingtalk_message(self, message: str) -> Tuple[bool, Optional[str]]:
        """发送钉钉消息"""
        try:
            # 使用配置的token，如果没有则使用全局配置
            token = self.config.dingtalk_token or config.dingtalk
            
            success, error = Dingtalk.markdown(
                content=message,
                token=token
            )
            return success is not None, error
        except Exception as e:
            logger.error(f"钉钉消息发送异常: {e}")
            return False, str(e)
    
    def _check_notification_limit(self) -> bool:
        """检查通知频率限制"""
        now = datetime.now()
        
        # 检查每日通知数量限制
        if self.daily_notification_count >= self.config.max_daily_notifications:
            return False
        
        # 检查最小间隔
        if self.last_notification_time:
            time_diff = (now - self.last_notification_time).total_seconds()
            if time_diff < self.config.min_signal_interval:
                return False
        
        return True
    
    def _record_notification(self, notification_type: str, content: str) -> None:
        """记录通知"""
        self.last_notification_time = datetime.now()
        self.daily_notification_count += 1
        
        self.notification_history.append({
            'timestamp': datetime.now(),
            'type': notification_type,
            'content': content
        })
        
        # 保留最近100条记录
        if len(self.notification_history) > 100:
            self.notification_history = self.notification_history[-100:]