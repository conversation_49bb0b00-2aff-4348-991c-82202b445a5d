"""
信号时机优化测试系统
用于测试不同时间延迟对交易信号胜率的影响
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import logging
import json
import pandas as pd
from collections import defaultdict
import numpy as np

# 尝试导入自定义logger，如果不存在则使用标准logger
try:
    from quant.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

from .event_contract_signal_generator_simple import SignalResult, EventContractSignalGeneratorSimple


@dataclass
class TimingTestConfig:
    """时机测试配置"""
    # 测试时间延迟范围（分钟）
    delay_minutes_range: List[int] = field(default_factory=lambda: list(range(0, 10)))
    
    # 测试数据配置
    test_days: int = 30  # 测试天数
    min_signals_per_delay: int = 50  # 每个延迟至少需要的信号数量
    
    # 胜率计算配置
    settlement_minutes: int = 15  # 结算时间（分钟）
    win_threshold: float = 0.001  # 胜利阈值（0.1%）
    
    # 输出配置
    output_dir: str = "tests/timing_optimization"
    save_detailed_results: bool = True


@dataclass
class SignalRecord:
    """信号记录"""
    signal_id: str
    timestamp: datetime
    delay_minutes: int
    direction: str  # UP/DOWN
    confidence: float
    signal_price: float
    settlement_price: Optional[float] = None
    settlement_time: Optional[datetime] = None
    result: str = 'PENDING'  # WIN/LOSS/TIE
    pnl_percentage: float = 0.0
    technical_score: float = 0.0
    risk_level: str = "MEDIUM"


@dataclass
class DelayStatistics:
    """延迟统计数据"""
    delay_minutes: int
    total_signals: int = 0
    win_count: int = 0
    loss_count: int = 0
    tie_count: int = 0
    win_rate: float = 0.0
    avg_pnl: float = 0.0
    avg_confidence: float = 0.0
    avg_technical_score: float = 0.0
    best_signal: Optional[SignalRecord] = None
    worst_signal: Optional[SignalRecord] = None
    
    def calculate_metrics(self, signals: List[SignalRecord]):
        """计算统计指标"""
        if not signals:
            return
            
        self.total_signals = len(signals)
        self.win_count = len([s for s in signals if s.result == 'WIN'])
        self.loss_count = len([s for s in signals if s.result == 'LOSS'])
        self.tie_count = len([s for s in signals if s.result == 'TIE'])
        
        if self.total_signals > 0:
            self.win_rate = (self.win_count / self.total_signals) * 100
            self.avg_pnl = np.mean([s.pnl_percentage for s in signals])
            self.avg_confidence = np.mean([s.confidence for s in signals])
            self.avg_technical_score = np.mean([s.technical_score for s in signals])
            
            # 找出最佳和最差信号
            if signals:
                self.best_signal = max(signals, key=lambda s: s.pnl_percentage)
                self.worst_signal = min(signals, key=lambda s: s.pnl_percentage)


class SignalTimingOptimizer:
    """信号时机优化器"""
    
    def __init__(self, config: TimingTestConfig):
        """
        初始化优化器
        
        Args:
            config: 测试配置
        """
        self.config = config
        self.signal_records: Dict[int, List[SignalRecord]] = defaultdict(list)
        self.statistics: Dict[int, DelayStatistics] = {}
        
        # 创建输出目录
        import os
        os.makedirs(self.config.output_dir, exist_ok=True)
        
        logger.info(f"信号时机优化器初始化完成")
        logger.info(f"测试延迟范围: {self.config.delay_minutes_range} 分钟")
        logger.info(f"结算时间: {self.config.settlement_minutes} 分钟")
        logger.info(f"胜利阈值: {self.config.win_threshold * 100:.1f}%")
    
    def add_signal_record(self, delay_minutes: int, signal_result: SignalResult, 
                         signal_price: float, timestamp: datetime) -> str:
        """
        添加信号记录
        
        Args:
            delay_minutes: 延迟分钟数
            signal_result: 信号结果
            signal_price: 信号价格
            timestamp: 信号时间戳
            
        Returns:
            信号ID
        """
        signal_id = f"timing_{delay_minutes}m_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        record = SignalRecord(
            signal_id=signal_id,
            timestamp=timestamp,
            delay_minutes=delay_minutes,
            direction=signal_result.direction,
            confidence=signal_result.confidence,
            signal_price=signal_price,
            technical_score=signal_result.technical_score,
            risk_level=signal_result.risk_level
        )
        
        self.signal_records[delay_minutes].append(record)
        logger.debug(f"添加信号记录: {signal_id}, 延迟{delay_minutes}分钟, {signal_result.direction}")
        
        return signal_id
    
    def settle_signal(self, signal_id: str, settlement_price: float, 
                     settlement_time: datetime) -> bool:
        """
        结算信号
        
        Args:
            signal_id: 信号ID
            settlement_price: 结算价格
            settlement_time: 结算时间
            
        Returns:
            是否成功结算
        """
        # 查找信号记录
        for delay_minutes, records in self.signal_records.items():
            for record in records:
                if record.signal_id == signal_id:
                    return self._settle_record(record, settlement_price, settlement_time)
        
        logger.warning(f"未找到信号记录: {signal_id}")
        return False
    
    def _settle_record(self, record: SignalRecord, settlement_price: float, 
                      settlement_time: datetime) -> bool:
        """结算单个记录"""
        record.settlement_price = settlement_price
        record.settlement_time = settlement_time
        
        # 计算PnL百分比
        if record.direction == "UP":
            record.pnl_percentage = (settlement_price - record.signal_price) / record.signal_price
        else:  # DOWN
            record.pnl_percentage = (record.signal_price - settlement_price) / record.signal_price
        
        # 判断结果
        if abs(record.pnl_percentage) < self.config.win_threshold:
            record.result = 'TIE'
        elif record.pnl_percentage > 0:
            record.result = 'WIN'
        else:
            record.result = 'LOSS'
        
        logger.debug(f"信号结算: {record.signal_id}, {record.result}, "
                    f"PnL: {record.pnl_percentage * 100:.2f}%")
        
        return True

    def calculate_all_statistics(self) -> Dict[int, DelayStatistics]:
        """计算所有延迟的统计数据"""
        self.statistics = {}

        for delay_minutes in self.config.delay_minutes_range:
            stats = DelayStatistics(delay_minutes=delay_minutes)

            # 只计算已结算的信号
            settled_signals = [
                record for record in self.signal_records[delay_minutes]
                if record.result != 'PENDING'
            ]

            stats.calculate_metrics(settled_signals)
            self.statistics[delay_minutes] = stats

            logger.info(f"延迟{delay_minutes}分钟: {stats.total_signals}个信号, "
                       f"胜率{stats.win_rate:.1f}%, 平均PnL{stats.avg_pnl*100:.2f}%")

        return self.statistics

    def get_best_timing(self) -> Tuple[int, DelayStatistics]:
        """获取最佳时机"""
        if not self.statistics:
            self.calculate_all_statistics()

        # 过滤掉信号数量不足的延迟
        valid_stats = {
            delay: stats for delay, stats in self.statistics.items()
            if stats.total_signals >= self.config.min_signals_per_delay
        }

        if not valid_stats:
            logger.warning("没有足够的信号数据进行分析")
            return 0, DelayStatistics(delay_minutes=0)

        # 按胜率排序，选择最佳
        best_delay = max(valid_stats.keys(), key=lambda d: valid_stats[d].win_rate)
        best_stats = valid_stats[best_delay]

        logger.info(f"最佳时机: 延迟{best_delay}分钟, 胜率{best_stats.win_rate:.1f}%")
        return best_delay, best_stats

    def generate_comparison_report(self) -> str:
        """生成对比报告"""
        if not self.statistics:
            self.calculate_all_statistics()

        report = []
        report.append("=" * 80)
        report.append("信号时机优化测试报告")
        report.append("=" * 80)
        report.append(f"测试配置:")
        report.append(f"  - 延迟范围: {self.config.delay_minutes_range} 分钟")
        report.append(f"  - 结算时间: {self.config.settlement_minutes} 分钟")
        report.append(f"  - 胜利阈值: {self.config.win_threshold * 100:.1f}%")
        report.append(f"  - 最小信号数: {self.config.min_signals_per_delay}")
        report.append("")

        # 按延迟时间排序
        sorted_delays = sorted(self.statistics.keys())

        # 表头
        report.append("详细结果:")
        report.append("-" * 80)
        report.append(f"{'延迟(分钟)':<8} {'信号数':<8} {'胜率(%)':<10} {'平均PnL(%)':<12} "
                     f"{'平均置信度':<12} {'技术评分':<10}")
        report.append("-" * 80)

        # 数据行
        for delay in sorted_delays:
            stats = self.statistics[delay]
            report.append(f"{delay:<8} {stats.total_signals:<8} {stats.win_rate:<10.1f} "
                         f"{stats.avg_pnl*100:<12.2f} {stats.avg_confidence:<12.1f} "
                         f"{stats.avg_technical_score:<10.1f}")

        report.append("-" * 80)

        # 最佳时机分析
        best_delay, best_stats = self.get_best_timing()
        report.append("")
        report.append("最佳时机分析:")
        report.append(f"  - 最优延迟: {best_delay} 分钟")
        report.append(f"  - 最高胜率: {best_stats.win_rate:.1f}%")
        report.append(f"  - 信号数量: {best_stats.total_signals}")
        report.append(f"  - 平均收益: {best_stats.avg_pnl*100:.2f}%")

        if best_stats.best_signal:
            report.append(f"  - 最佳信号: {best_stats.best_signal.pnl_percentage*100:.2f}% "
                         f"({best_stats.best_signal.timestamp.strftime('%Y-%m-%d %H:%M')})")

        # 改进建议
        report.append("")
        report.append("优化建议:")

        # 分析胜率趋势
        win_rates = [self.statistics[d].win_rate for d in sorted_delays if self.statistics[d].total_signals >= 10]
        if len(win_rates) >= 3:
            if win_rates[-1] > win_rates[0]:
                report.append("  - 延迟时间越长，胜率呈上升趋势，建议使用较长延迟")
            elif win_rates[-1] < win_rates[0]:
                report.append("  - 延迟时间越长，胜率呈下降趋势，建议使用较短延迟")
            else:
                report.append("  - 胜率在不同延迟下相对稳定")

        # 信号质量分析
        high_confidence_delays = [
            d for d in sorted_delays
            if self.statistics[d].avg_confidence > 70 and self.statistics[d].total_signals >= 10
        ]
        if high_confidence_delays:
            report.append(f"  - 高置信度延迟: {high_confidence_delays} 分钟")

        report.append("")
        report.append("=" * 80)

        return "\n".join(report)

    def save_detailed_results(self, filename: Optional[str] = None) -> str:
        """保存详细结果到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.config.output_dir}/timing_optimization_{timestamp}.json"

        # 准备数据
        results = {
            "config": {
                "delay_minutes_range": self.config.delay_minutes_range,
                "settlement_minutes": self.config.settlement_minutes,
                "win_threshold": self.config.win_threshold,
                "min_signals_per_delay": self.config.min_signals_per_delay
            },
            "statistics": {},
            "detailed_signals": {}
        }

        # 统计数据
        for delay, stats in self.statistics.items():
            results["statistics"][str(delay)] = {
                "delay_minutes": stats.delay_minutes,
                "total_signals": stats.total_signals,
                "win_count": stats.win_count,
                "loss_count": stats.loss_count,
                "tie_count": stats.tie_count,
                "win_rate": stats.win_rate,
                "avg_pnl": stats.avg_pnl,
                "avg_confidence": stats.avg_confidence,
                "avg_technical_score": stats.avg_technical_score
            }

        # 详细信号数据（如果启用）
        if self.config.save_detailed_results:
            for delay, records in self.signal_records.items():
                results["detailed_signals"][str(delay)] = [
                    {
                        "signal_id": record.signal_id,
                        "timestamp": record.timestamp.isoformat(),
                        "direction": record.direction,
                        "confidence": record.confidence,
                        "signal_price": record.signal_price,
                        "settlement_price": record.settlement_price,
                        "settlement_time": record.settlement_time.isoformat() if record.settlement_time else None,
                        "result": record.result,
                        "pnl_percentage": record.pnl_percentage,
                        "technical_score": record.technical_score,
                        "risk_level": record.risk_level
                    }
                    for record in records if record.result != 'PENDING'
                ]

        # 保存到文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        logger.info(f"详细结果已保存到: {filename}")
        return filename
