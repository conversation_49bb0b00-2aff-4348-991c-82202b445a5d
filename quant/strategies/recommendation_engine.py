from __future__ import annotations

"""RecommendationEngine
=======================

根据 SignalGenerator 的 SignalResult + FactorFilter 得分，生成最终交易建议。
不直接下单，仅返回建议字典，可供 MainStrategy / Notifier 使用。
"""

from typing import Dict
from datetime import datetime
from decimal import Decimal, ROUND_DOWN

# 假设基础下注 20 USDT，允许外部传入 multiplier
DEFAULT_STAKE = 20.0


class RecommendationEngine:
    """根据信号和因子评估生成 BUY/SELL 建议。"""

    def __init__(self, base_stake: float = DEFAULT_STAKE):
        self.base_stake = base_stake

    def make_recommendation(
        self,
        signal_result,
        factor_eval: Dict[str, object],
        bet_multiplier: float = 1.0,
    ) -> Dict[str, object]:
        """生成建议。

        Args:
            signal_result: SignalResult 对象
            factor_eval: FactorFilter.evaluate_entry() 的返回结果
            bet_multiplier: 资金管理系数
        """
        # 默认无建议
        recommendation = {
            "has_recommendation": False,
            "direction": signal_result.direction,
            "stake": 0.0,
            "confidence": signal_result.confidence,
            "reason": factor_eval.get("reason", "条件不足"),
            "score": factor_eval.get("score", 0),
            "generated_at": datetime.now().isoformat(),
            # 🔧 新增：添加信号相关信息
            "signal_result": signal_result,  # 保存完整的信号结果
            "signal_price": getattr(signal_result, "signal_price", 0.0),  # 信号价格
        }

        if not signal_result.has_signal:
            recommendation["reason"] = "无有效信号"
            return recommendation

        if not factor_eval.get("recommend_entry", False):
            return recommendation

        # 满足条件，生成建议
        stake = Decimal(str(self.base_stake * bet_multiplier)).quantize(Decimal("0.01"), rounding=ROUND_DOWN)
        recommendation.update({
            "has_recommendation": True,
            "stake": float(stake),
            "factors": factor_eval.get("factors", {}),
            "remaining_time": factor_eval.get("remaining_time"),
        })
        return recommendation 