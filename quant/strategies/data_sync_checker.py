#!/usr/bin/env python3
"""
数据同步检查和修复工具
用于检查钉钉通知和数据库记录之间的一致性问题
"""

import sqlite3
import json
import os
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

# 使用现有的日志系统
from quant.utils import logger


@dataclass
class MissingSignalData:
    """缺失的信号数据"""
    signal_id: str
    timestamp: str
    direction: str
    confidence: float
    signal_price: float
    result: str
    settlement_price: float
    settlement_time: str
    pnl: float
    source: str  # 数据来源


class DataSyncChecker:
    """数据同步检查器"""
    
    def __init__(self, db_path: str = "./data/signal_settlement.db"):
        self.db_path = db_path
        self.missing_signals = []
        
    def check_running_strategy_data(self) -> Dict:
        """检查正在运行的策略数据"""
        try:
            print("🔍 检查正在运行的策略数据...")
            
            # 尝试连接到正在运行的策略实例
            # 这里我们需要通过进程间通信或共享内存来获取数据
            # 由于Python的限制，我们采用文件系统方式
            
            result = {
                'running_processes': [],
                'notification_history': [],
                'pending_signals': [],
                'settled_signals': []
            }
            
            # 检查运行进程
            import subprocess
            try:
                ps_output = subprocess.check_output([
                    'ps', 'aux'
                ], text=True)
                
                for line in ps_output.split('\n'):
                    if 'event_contract_strategy' in line and 'python' in line:
                        result['running_processes'].append(line.strip())
                        
            except Exception as e:
                logger.warning(f"检查运行进程失败: {e}")
            
            return result
            
        except Exception as e:
            logger.error(f"检查运行策略数据失败: {e}")
            return {}
    
    def parse_dingtalk_message_for_signals(self, message_text: str) -> Optional[MissingSignalData]:
        """从钉钉消息文本中解析信号数据"""
        try:
            # 解析信号结算通知的格式
            # 例如：📊 信号结算通知 ❌
            # 🆔 信号ID: signal_1752830349875_1583
            # 📈 信号方向: UP 🚀
            # 💰 信号价格: 118671.12 USDT
            # 🎯 结算价格: 118649.99 USDT
            # 📊 价格变化: -0.02% ⬇️
            # 🏆 结算结果: LOSS
            
            # 提取信号ID
            signal_id_match = re.search(r'信号ID:\s*([^\s\n]+)', message_text)
            if not signal_id_match:
                return None
            signal_id = signal_id_match.group(1)
            
            # 提取方向
            direction_match = re.search(r'信号方向:\s*(UP|DOWN)', message_text)
            if not direction_match:
                return None
            direction = direction_match.group(1)
            
            # 提取信号价格
            signal_price_match = re.search(r'信号价格:\s*([\d.]+)', message_text)
            if not signal_price_match:
                return None
            signal_price = float(signal_price_match.group(1))
            
            # 提取结算价格
            settlement_price_match = re.search(r'结算价格:\s*([\d.]+)', message_text)
            if not settlement_price_match:
                return None
            settlement_price = float(settlement_price_match.group(1))
            
            # 提取结果
            result_match = re.search(r'结算结果:\s*(WIN|LOSS|TIE)', message_text)
            if not result_match:
                return None
            result = result_match.group(1)
            
            # 提取置信度（如果有）
            confidence_match = re.search(r'预测置信度:\s*([\d.]+)%', message_text)
            confidence = float(confidence_match.group(1)) if confidence_match else 50.0
            
            # 提取结算时间（如果有）
            time_match = re.search(r'结算时间:\s*([^\n]+)', message_text)
            settlement_time = time_match.group(1).strip() if time_match else datetime.now().isoformat()
            
            # 计算盈亏
            price_change = ((settlement_price - signal_price) / signal_price) * 100
            if direction == 'DOWN':
                price_change = -price_change
            
            return MissingSignalData(
                signal_id=signal_id,
                timestamp=settlement_time,
                direction=direction,
                confidence=confidence,
                signal_price=signal_price,
                result=result,
                settlement_price=settlement_price,
                settlement_time=settlement_time,
                pnl=price_change,
                source="dingtalk_message"
            )
            
        except Exception as e:
            logger.error(f"解析钉钉消息失败: {e}")
            return None
    
    def check_database_completeness(self) -> Dict:
        """检查数据库完整性"""
        try:
            print("🗄️ 检查数据库完整性...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取今日所有信号
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute("""
                SELECT signal_id, timestamp, direction, confidence, signal_price,
                       result, settlement_price, settlement_time, pnl
                FROM signal_records 
                WHERE timestamp LIKE ?
                ORDER BY timestamp DESC
            """, (f"{today}%",))
            
            today_signals = cursor.fetchall()
            
            # 获取待结算信号
            cursor.execute("""
                SELECT signal_id, timestamp, direction, confidence, signal_price, expiry_time
                FROM signal_records 
                WHERE result = 'PENDING'
                ORDER BY timestamp DESC
            """)
            
            pending_signals = cursor.fetchall()
            
            conn.close()
            
            return {
                'today_signals': today_signals,
                'pending_signals': pending_signals,
                'total_today': len(today_signals),
                'total_pending': len(pending_signals)
            }
            
        except Exception as e:
            logger.error(f"检查数据库完整性失败: {e}")
            return {}
    
    def suggest_data_recovery(self, dingtalk_messages: List[str] = None) -> List[MissingSignalData]:
        """建议数据恢复方案"""
        try:
            print("🔧 分析数据恢复方案...")
            
            missing_signals = []
            
            # 如果提供了钉钉消息，尝试解析
            if dingtalk_messages:
                for message in dingtalk_messages:
                    parsed_signal = self.parse_dingtalk_message_for_signals(message)
                    if parsed_signal:
                        missing_signals.append(parsed_signal)
            
            # 检查数据库中是否已存在这些信号
            if missing_signals:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                verified_missing = []
                for signal in missing_signals:
                    cursor.execute("SELECT signal_id FROM signal_records WHERE signal_id = ?", (signal.signal_id,))
                    if not cursor.fetchone():
                        verified_missing.append(signal)
                
                conn.close()
                missing_signals = verified_missing
            
            return missing_signals
            
        except Exception as e:
            logger.error(f"分析数据恢复方案失败: {e}")
            return []
    
    def recover_missing_signals(self, missing_signals: List[MissingSignalData]) -> bool:
        """恢复缺失的信号数据"""
        try:
            if not missing_signals:
                print("✅ 没有需要恢复的信号数据")
                return True
                
            print(f"🔧 开始恢复 {len(missing_signals)} 条缺失的信号数据...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            recovered_count = 0
            for signal in missing_signals:
                try:
                    # 计算到期时间（假设10分钟）
                    signal_time = datetime.fromisoformat(signal.timestamp.replace('Z', '+00:00'))
                    expiry_time = signal_time + timedelta(minutes=10)
                    
                    cursor.execute('''
                        INSERT INTO signal_records
                        (signal_id, timestamp, direction, confidence, signal_price, expiry_time,
                         result, settlement_price, settlement_time, pnl, auto_settled, 
                         signal_strength, supporting_indicators, market_conditions)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        signal.signal_id,
                        signal.timestamp,
                        signal.direction,
                        signal.confidence,
                        signal.signal_price,
                        expiry_time.isoformat(),
                        signal.result,
                        signal.settlement_price,
                        signal.settlement_time,
                        signal.pnl,
                        True,  # auto_settled
                        'MEDIUM',  # 默认强度
                        json.dumps([]),  # 空的支撑指标
                        'RECOVERED'  # 标记为恢复的数据
                    ))
                    
                    recovered_count += 1
                    print(f"   ✅ 恢复信号: {signal.signal_id} - {signal.direction} - {signal.result}")
                    
                except Exception as e:
                    print(f"   ❌ 恢复信号失败: {signal.signal_id} - {e}")
            
            conn.commit()
            conn.close()
            
            print(f"🎉 成功恢复 {recovered_count} 条信号数据")
            return recovered_count > 0
            
        except Exception as e:
            logger.error(f"恢复缺失信号数据失败: {e}")
            return False
    
    def generate_sync_report(self) -> Dict:
        """生成数据同步报告"""
        try:
            print("📊 生成数据同步报告...")
            
            # 检查各种数据源
            running_data = self.check_running_strategy_data()
            db_data = self.check_database_completeness()
            
            report = {
                'check_time': datetime.now().isoformat(),
                'running_strategy': running_data,
                'database_status': db_data,
                'sync_issues': [],
                'recommendations': []
            }
            
            # 分析同步问题
            if len(running_data.get('running_processes', [])) > 1:
                report['sync_issues'].append("检测到多个策略进程同时运行，可能导致数据冲突")
                report['recommendations'].append("建议停止多余的策略进程，只保留一个")
            
            if db_data.get('total_today', 0) < 5:  # 假设今天应该有更多信号
                report['sync_issues'].append("今日信号记录数量偏少，可能存在数据丢失")
                report['recommendations'].append("检查钉钉通知历史，手动恢复缺失的信号数据")
            
            return report
            
        except Exception as e:
            logger.error(f"生成数据同步报告失败: {e}")
            return {}


def main():
    """主函数"""
    print("🔧 数据同步检查和修复工具")
    print("=" * 50)
    
    checker = DataSyncChecker()
    
    # 生成同步报告
    report = checker.generate_sync_report()
    
    print("\n📋 数据同步报告:")
    print("-" * 30)
    print(f"检查时间: {report.get('check_time', 'N/A')}")
    print(f"运行进程数: {len(report.get('running_strategy', {}).get('running_processes', []))}")
    print(f"今日信号数: {report.get('database_status', {}).get('total_today', 0)}")
    print(f"待结算信号: {report.get('database_status', {}).get('total_pending', 0)}")
    
    # 显示同步问题
    issues = report.get('sync_issues', [])
    if issues:
        print(f"\n⚠️ 发现 {len(issues)} 个同步问题:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
    
    # 显示建议
    recommendations = report.get('recommendations', [])
    if recommendations:
        print(f"\n💡 修复建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
    
    print("\n" + "=" * 50)
    print("✅ 数据同步检查完成")


if __name__ == "__main__":
    main()
