"""
延迟信号生成器
支持在15分钟K线周期内不同时间点生成信号，用于时机优化测试
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import logging
from collections import deque
import copy

# 尝试导入自定义logger，如果不存在则使用标准logger
try:
    from quant.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

from .event_contract_signal_generator_simple import EventContractSignalGeneratorSimple, SignalResult, KlineData


@dataclass
class DelayedSignalConfig:
    """延迟信号配置"""
    delay_minutes: int = 0  # 延迟分钟数（0-9）
    base_signal_threshold: float = 55.0  # 基础信号阈值
    confidence_threshold: float = 50.0  # 置信度阈值
    enable_signal_filtering: bool = True  # 启用信号过滤
    
    def __post_init__(self):
        """验证配置参数"""
        if not (0 <= self.delay_minutes <= 9):
            raise ValueError(f"延迟分钟数必须在0-9之间，当前值: {self.delay_minutes}")


class DelayedSignalGenerator:
    """延迟信号生成器"""
    
    def __init__(self, delay_config: DelayedSignalConfig):
        """
        初始化延迟信号生成器
        
        Args:
            delay_config: 延迟配置
        """
        self.delay_config = delay_config
        
        # 创建基础信号生成器
        self.base_generator = EventContractSignalGeneratorSimple(
            signal_threshold=delay_config.base_signal_threshold,
            confidence_threshold=delay_config.confidence_threshold
        )
        
        # 延迟信号缓存
        self.pending_signals: deque = deque(maxlen=100)
        self.last_15m_kline_time: Optional[datetime] = None
        
        logger.info(f"延迟信号生成器初始化完成，延迟: {delay_config.delay_minutes} 分钟")
    
    def add_kline_data(self, timestamp: int, open_price: float, high_price: float,
                      low_price: float, close_price: float, volume: float):
        """
        添加K线数据
        
        Args:
            timestamp: 时间戳（毫秒）
            open_price: 开盘价
            high_price: 最高价
            low_price: 最低价
            close_price: 收盘价
            volume: 成交量
        """
        # 添加到基础生成器
        self.base_generator.add_kline_data(
            timestamp, open_price, high_price, low_price, close_price, volume
        )
        
        # 检查是否是新的15分钟K线
        current_time = datetime.fromtimestamp(timestamp / 1000)
        current_15m_slot = (current_time.hour * 4) + (current_time.minute // 15)
        
        if self.last_15m_kline_time is None:
            self.last_15m_kline_time = current_time
            return
        
        last_15m_slot = (self.last_15m_kline_time.hour * 4) + (self.last_15m_kline_time.minute // 15)
        
        # 如果是新的15分钟K线周期，生成基础信号并加入延迟队列
        if current_15m_slot != last_15m_slot:
            self._generate_and_queue_signal(current_time, close_price)
            self.last_15m_kline_time = current_time
        
        # 检查是否有延迟信号需要触发
        self._check_delayed_signals(current_time)
    
    def _generate_and_queue_signal(self, kline_time: datetime, current_price: float):
        """生成基础信号并加入延迟队列"""
        try:
            # 生成基础信号
            base_signal = self.base_generator.generate_signal()
            
            if not base_signal.has_signal:
                logger.debug(f"15分钟K线 {kline_time.strftime('%H:%M')} 无信号生成")
                return
            
            # 计算触发时间（K线时间 + 延迟分钟）
            trigger_time = kline_time + timedelta(minutes=self.delay_config.delay_minutes)
            
            # 创建延迟信号记录
            delayed_signal = {
                'base_signal': copy.deepcopy(base_signal),
                'kline_time': kline_time,
                'trigger_time': trigger_time,
                'signal_price': current_price,
                'status': 'PENDING'
            }
            
            self.pending_signals.append(delayed_signal)
            
            logger.debug(f"信号已加入延迟队列: {base_signal.direction}, "
                        f"K线时间: {kline_time.strftime('%H:%M')}, "
                        f"触发时间: {trigger_time.strftime('%H:%M')}")
            
        except Exception as e:
            logger.error(f"生成延迟信号异常: {e}")
    
    def _check_delayed_signals(self, current_time: datetime):
        """检查并触发延迟信号"""
        triggered_signals = []
        
        for signal_record in self.pending_signals:
            if (signal_record['status'] == 'PENDING' and 
                current_time >= signal_record['trigger_time']):
                
                signal_record['status'] = 'TRIGGERED'
                signal_record['actual_trigger_time'] = current_time
                triggered_signals.append(signal_record)
        
        # 记录触发的信号
        for signal_record in triggered_signals:
            logger.info(f"延迟信号触发: {signal_record['base_signal'].direction}, "
                       f"延迟 {self.delay_config.delay_minutes} 分钟, "
                       f"置信度: {signal_record['base_signal'].confidence:.1f}%")
    
    def get_latest_triggered_signal(self) -> Optional[Tuple[SignalResult, datetime, float]]:
        """
        获取最新触发的信号
        
        Returns:
            (信号结果, 触发时间, 信号价格) 或 None
        """
        # 查找最新触发的信号
        latest_triggered = None
        latest_time = None
        
        for signal_record in self.pending_signals:
            if signal_record['status'] == 'TRIGGERED':
                trigger_time = signal_record['actual_trigger_time']
                if latest_time is None or trigger_time > latest_time:
                    latest_triggered = signal_record
                    latest_time = trigger_time
        
        if latest_triggered:
            # 标记为已处理
            latest_triggered['status'] = 'PROCESSED'
            
            return (
                latest_triggered['base_signal'],
                latest_triggered['actual_trigger_time'],
                latest_triggered['signal_price']
            )
        
        return None
    
    def get_pending_signals_count(self) -> int:
        """获取待触发信号数量"""
        return len([s for s in self.pending_signals if s['status'] == 'PENDING'])
    
    def get_triggered_signals_count(self) -> int:
        """获取已触发但未处理信号数量"""
        return len([s for s in self.pending_signals if s['status'] == 'TRIGGERED'])
    
    def clear_old_signals(self, hours_ago: int = 24):
        """清理旧信号记录"""
        cutoff_time = datetime.now() - timedelta(hours=hours_ago)
        
        # 过滤掉旧信号
        old_count = len(self.pending_signals)
        self.pending_signals = deque(
            [s for s in self.pending_signals if s['kline_time'] > cutoff_time],
            maxlen=100
        )
        
        cleared_count = old_count - len(self.pending_signals)
        if cleared_count > 0:
            logger.info(f"清理了 {cleared_count} 个旧信号记录")
    
    def get_status(self) -> Dict[str, Any]:
        """获取生成器状态"""
        base_status = self.base_generator.get_status()
        
        return {
            'delay_minutes': self.delay_config.delay_minutes,
            'pending_signals': self.get_pending_signals_count(),
            'triggered_signals': self.get_triggered_signals_count(),
            'total_queued_signals': len(self.pending_signals),
            'base_generator_status': base_status
        }
    
    def force_generate_signal(self) -> Optional[SignalResult]:
        """
        强制生成当前信号（用于测试）
        
        Returns:
            信号结果或None
        """
        return self.base_generator.generate_signal()


class MultiDelaySignalManager:
    """多延迟信号管理器"""
    
    def __init__(self, delay_minutes_list: List[int]):
        """
        初始化多延迟信号管理器
        
        Args:
            delay_minutes_list: 延迟分钟数列表
        """
        self.generators: Dict[int, DelayedSignalGenerator] = {}
        
        # 创建各个延迟的信号生成器
        for delay_minutes in delay_minutes_list:
            config = DelayedSignalConfig(delay_minutes=delay_minutes)
            self.generators[delay_minutes] = DelayedSignalGenerator(config)
        
        logger.info(f"多延迟信号管理器初始化完成，延迟列表: {delay_minutes_list}")
    
    def add_kline_data(self, timestamp: int, open_price: float, high_price: float,
                      low_price: float, close_price: float, volume: float):
        """向所有生成器添加K线数据"""
        for generator in self.generators.values():
            generator.add_kline_data(timestamp, open_price, high_price, low_price, close_price, volume)
    
    def get_all_triggered_signals(self) -> Dict[int, Optional[Tuple[SignalResult, datetime, float]]]:
        """获取所有延迟的最新触发信号"""
        results = {}
        for delay_minutes, generator in self.generators.items():
            results[delay_minutes] = generator.get_latest_triggered_signal()
        return results
    
    def get_overall_status(self) -> Dict[int, Dict[str, Any]]:
        """获取所有生成器状态"""
        return {delay: gen.get_status() for delay, gen in self.generators.items()}
    
    def clear_all_old_signals(self, hours_ago: int = 24):
        """清理所有生成器的旧信号"""
        for generator in self.generators.values():
            generator.clear_old_signals(hours_ago)
