#!/usr/bin/env python3
"""
手动数据恢复脚本
根据钉钉通知截图信息恢复缺失的信号数据
"""

import sqlite3
import json
from datetime import datetime, timedelta
from quant.utils import logger


def recover_signal_from_dingtalk_screenshot():
    """根据钉钉截图恢复信号数据"""
    
    print("🔧 根据钉钉截图恢复缺失的信号数据")
    print("=" * 50)
    
    # 根据截图信息定义缺失的信号数据
    missing_signal = {
        'signal_id': 'signal_1752830349875_1583',
        'direction': 'UP',
        'signal_price': 118671.12,
        'settlement_price': 118649.99,
        'result': 'LOSS',
        'confidence': 53.0,
        'settlement_time': '2025-07-18T17:29:00',
        'price_change': -0.02
    }
    
    print(f"📊 准备恢复信号:")
    print(f"   信号ID: {missing_signal['signal_id']}")
    print(f"   方向: {missing_signal['direction']}")
    print(f"   信号价格: {missing_signal['signal_price']} USDT")
    print(f"   结算价格: {missing_signal['settlement_price']} USDT")
    print(f"   结果: {missing_signal['result']}")
    print(f"   置信度: {missing_signal['confidence']}%")
    print(f"   结算时间: {missing_signal['settlement_time']}")
    
    try:
        db_path = "./data/signal_settlement.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查信号是否已存在
        cursor.execute("SELECT signal_id FROM signal_records WHERE signal_id = ?", 
                      (missing_signal['signal_id'],))
        
        if cursor.fetchone():
            print(f"⚠️ 信号 {missing_signal['signal_id']} 已存在，跳过恢复")
            conn.close()
            return False
        
        # 计算信号生成时间（结算时间往前推10分钟）
        settlement_time = datetime.fromisoformat(missing_signal['settlement_time'])
        signal_time = settlement_time - timedelta(minutes=10)
        expiry_time = settlement_time  # 到期时间就是结算时间
        
        # 计算盈亏（基于价格变化百分比）
        pnl = missing_signal['price_change']
        
        # 插入恢复的信号数据
        cursor.execute('''
            INSERT INTO signal_records
            (signal_id, timestamp, direction, confidence, signal_price, expiry_time,
             result, settlement_price, settlement_time, pnl, auto_settled, 
             signal_strength, supporting_indicators, market_conditions, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            missing_signal['signal_id'],
            signal_time.isoformat(),
            missing_signal['direction'],
            missing_signal['confidence'],
            missing_signal['signal_price'],
            expiry_time.isoformat(),
            missing_signal['result'],
            missing_signal['settlement_price'],
            missing_signal['settlement_time'],
            pnl,
            True,  # auto_settled
            'MEDIUM',  # 默认强度
            json.dumps(["RSI_5m_超买", "MACD_5m_看涨", "布林带_5m_上轨阻力", "EMA_5m_多头排列", "RSI_15m_超买"]),  # 从截图推测的指标
            'RECOVERED_FROM_DINGTALK',  # 标记为从钉钉恢复的数据
            datetime.now().isoformat()
        ))
        
        conn.commit()
        conn.close()
        
        print("✅ 信号数据恢复成功!")
        return True
        
    except Exception as e:
        logger.error(f"恢复信号数据失败: {e}")
        print(f"❌ 恢复失败: {e}")
        return False


def verify_recovery():
    """验证数据恢复结果"""
    print("\n🔍 验证数据恢复结果...")
    
    try:
        db_path = "./data/signal_settlement.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询今日所有信号
        today = datetime.now().strftime('%Y-%m-%d')
        cursor.execute("""
            SELECT signal_id, timestamp, direction, confidence, signal_price,
                   result, settlement_price, settlement_time, pnl, market_conditions
            FROM signal_records 
            WHERE timestamp LIKE ? OR settlement_time LIKE ?
            ORDER BY timestamp DESC
        """, (f"{today}%", f"{today}%"))
        
        today_signals = cursor.fetchall()
        conn.close()
        
        print(f"📊 今日信号记录总数: {len(today_signals)}")
        print("\n📋 详细记录:")
        print("-" * 80)
        print(f"{'时间':<20} {'方向':<6} {'置信度':<8} {'结果':<8} {'盈亏':<10} {'来源':<15}")
        print("-" * 80)
        
        total_pnl = 0
        wins = 0
        losses = 0
        
        for signal in today_signals:
            signal_id, timestamp, direction, confidence, signal_price, result, settlement_price, settlement_time, pnl, market_conditions = signal
            
            # 统计
            if result == 'WIN':
                wins += 1
            elif result == 'LOSS':
                losses += 1
            
            if pnl:
                total_pnl += pnl
            
            # 显示时间
            display_time = timestamp[:19] if timestamp else 'N/A'
            source = market_conditions if market_conditions else 'NORMAL'
            
            print(f"{display_time:<20} {direction:<6} {confidence:<8.1f} {result:<8} {pnl:<10.2f} {source:<15}")
        
        print("-" * 80)
        print(f"📈 统计汇总:")
        print(f"   总信号数: {len(today_signals)}")
        print(f"   胜利: {wins}, 失败: {losses}")
        print(f"   胜率: {(wins / (wins + losses) * 100) if (wins + losses) > 0 else 0:.1f}%")
        print(f"   总盈亏: {total_pnl:.2f}%")
        
        return len(today_signals)
        
    except Exception as e:
        logger.error(f"验证恢复结果失败: {e}")
        return 0


def main():
    """主函数"""
    print("🚀 手动数据恢复工具")
    print("基于钉钉通知截图恢复缺失的信号数据")
    print("=" * 60)
    
    # 1. 恢复缺失的信号数据
    success = recover_signal_from_dingtalk_screenshot()
    
    if success:
        # 2. 验证恢复结果
        signal_count = verify_recovery()
        
        if signal_count > 0:
            print(f"\n🎉 数据恢复完成! 当前共有 {signal_count} 条今日信号记录")
            print("\n💡 建议:")
            print("   1. 重新运行胜率报告系统查看更新后的统计数据")
            print("   2. 检查策略系统的数据记录机制，避免未来数据丢失")
            print("   3. 考虑增加数据备份和同步机制")
        else:
            print("\n⚠️ 验证失败，请检查数据库状态")
    else:
        print("\n❌ 数据恢复失败")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    main()
