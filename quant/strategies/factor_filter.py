from __future__ import annotations

"""FactorFilter
=================

根据 15 分钟 K 线内部 1m/5m 数据，评估多因子打分，用于择时确认。

设计原则：
1. 不依赖外部下单 API，只返回得分与因子明细。
2. 默认权重：价格行为 20、成交量 15、动能 15、K线结构 10，时间衰减 -10~0。
3. If score >= threshold (默认 40) 且 remaining_time >= 600s 则推荐入场。

返回:
    {
        'score': float,
        'factors': Dict[str, float],
        'recommend_entry': bool,
        'reason': str
    }
"""

from dataclasses import dataclass, field
from typing import Dict, List, Tuple
from datetime import datetime, timedelta


@dataclass
class MinuteKline:
    timestamp: int  # 毫秒
    open: float
    high: float
    low: float
    close: float
    volume: float


@dataclass
class FactorFilter:
    threshold: float = 25.0  # 入场阈值 (优化后从40.0降低到25.0)
    weights: Dict[str, float] = field(default_factory=lambda: {
        "price_action": 15,  # 优化后从20降低到15
        "volume": 20,        # 优化后从15增加到20
        "momentum": 20,      # 优化后从15增加到20
        "structure": 10,     # 保持不变
        "time_decay": 0,     # 特殊处理
    })

    def evaluate_entry(
        self,
        pending_created_at: datetime,
        now: datetime,
        minute_klines: List[MinuteKline],
        indicators: Dict[str, float],
    ) -> Dict[str, object]:
        """评估是否可入场。

        Args:
            pending_created_at: pending_signal 创建时间
            now: 当前时间
            minute_klines: 最近若干 1m K 线
            indicators: 预计算的技术指标（如 rsi, macd_hist, boll_pos 等）
        """
        factors: Dict[str, float] = {}
        # 1. 价格行为因子：突破后回踩 / 连续影线等 (简化: 最近3根收盘涨跌幅)
        if len(minute_klines) >= 3:
            pct_changes = [
                (minute_klines[-i].close - minute_klines[-i].open) / minute_klines[-i].open * 100
                for i in range(1, 4)
            ]
            # 🔧 优化：降低阈值从0.2%到0.1%，更容易得分
            price_action_score = sum(1 for p in pct_changes if abs(p) > 0.1) / 3 * self.weights["price_action"]
        else:
            price_action_score = 0.0
        factors["price_action"] = round(price_action_score, 2)

        # 2. 成交量因子
        vols = [k.volume for k in minute_klines[-20:]] if minute_klines else []
        if vols:
            avg_vol = sum(vols) / len(vols)
            current_vol = minute_klines[-1].volume
            vol_ratio = current_vol / avg_vol if avg_vol else 1.0
            # 🔧 优化：降低满分要求从2倍到1.5倍，更容易得分
            volume_score = min(vol_ratio / 1.5, 1.0) * self.weights["volume"]
        else:
            volume_score = 0.0
        factors["volume"] = round(volume_score, 2)

        # 3. 动能因子 (RSI 背离等，简化使用 RSI 值距离 50)
        rsi = indicators.get("rsi", 50)
        # 🔧 优化：降低满分要求从50点偏离到30点偏离，更容易得分
        momentum_score = min(abs(rsi - 50) / 30, 1.0) * self.weights["momentum"]
        factors["momentum"] = round(momentum_score, 2)

        # 4. K 线结构 (实体长度与影线等，简化使用实体% 高于某阈值)
        if minute_klines:
            last = minute_klines[-1]
            body = abs(last.close - last.open)
            rng = last.high - last.low if last.high - last.low else 0.0001
            body_ratio = body / rng
            structure_score = min(body_ratio, 1.0) * self.weights["structure"]
        else:
            structure_score = 0.0
        factors["structure"] = round(structure_score, 2)

        # 5. 时间衰减：距 pending 创建超过 10m 则 -10 分
        remaining = 600 - int((now - pending_created_at).total_seconds())
        if remaining < 0:
            time_decay_score = -10
        elif remaining < 300:
            time_decay_score = -5
        else:
            time_decay_score = 0
        factors["time_decay"] = time_decay_score

        # 计算总分
        total = sum(factors.values())
        # 🔧 使用更宽松的时间限制：从600秒改为300秒（5分钟）
        recommend = total >= self.threshold and remaining >= 300
        reason = (
            "满足入场条件" if recommend else f"得分{total:.1f}<阈值{self.threshold}或剩余{remaining}s<300s"
        )

        return {
            "score": round(total, 2),
            "factors": factors,
            "remaining_time": remaining,
            "recommend_entry": recommend,
            "reason": reason,
        } 