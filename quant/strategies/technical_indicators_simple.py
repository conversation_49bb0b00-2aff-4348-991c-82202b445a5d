#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的技术指标计算器

不依赖talib库，使用pandas和numpy实现基础技术指标

Author: HertelQuant Enhanced
Date: 2025-01-31
"""

import numpy as np
import pandas as pd
from typing import List, <PERSON><PERSON>


def calculate_sma(prices: List[float], period: int) -> List[float]:
    """
    计算简单移动平均线
    
    Args:
        prices: 价格序列
        period: 周期
        
    Returns:
        SMA序列
    """
    if len(prices) < period:
        return [np.nan] * len(prices)
    
    df = pd.Series(prices)
    sma = df.rolling(window=period).mean()
    return sma.tolist()


def calculate_ema(prices: List[float], period: int) -> List[float]:
    """
    计算指数移动平均线
    
    Args:
        prices: 价格序列
        period: 周期
        
    Returns:
        EMA序列
    """
    if len(prices) < period:
        return [np.nan] * len(prices)
    
    df = pd.Series(prices)
    ema = df.ewm(span=period).mean()
    return ema.tolist()


def calculate_rsi(prices: List[float], period: int = 14) -> List[float]:
    """
    计算相对强弱指标
    
    Args:
        prices: 价格序列
        period: 周期
        
    Returns:
        RSI序列
    """
    if len(prices) < period + 1:
        return [np.nan] * len(prices)
    
    df = pd.Series(prices)
    delta = df.diff()
    
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi.tolist()


def calculate_macd(prices: List[float], fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[List[float], List[float], List[float]]:
    """
    计算MACD指标
    
    Args:
        prices: 价格序列
        fast: 快速EMA周期
        slow: 慢速EMA周期
        signal: 信号线周期
        
    Returns:
        (MACD线, 信号线, MACD柱状图)
    """
    if len(prices) < slow:
        return ([np.nan] * len(prices), [np.nan] * len(prices), [np.nan] * len(prices))
    
    df = pd.Series(prices)
    
    # 计算快慢EMA
    ema_fast = df.ewm(span=fast).mean()
    ema_slow = df.ewm(span=slow).mean()
    
    # MACD线
    macd_line = ema_fast - ema_slow
    
    # 信号线
    signal_line = macd_line.ewm(span=signal).mean()
    
    # MACD柱状图
    histogram = macd_line - signal_line
    
    return macd_line.tolist(), signal_line.tolist(), histogram.tolist()


def calculate_bollinger_bands(prices: List[float], period: int = 20, std_dev: float = 2.0) -> Tuple[List[float], List[float], List[float]]:
    """
    计算布林带
    
    Args:
        prices: 价格序列
        period: 周期
        std_dev: 标准差倍数
        
    Returns:
        (上轨, 中轨, 下轨)
    """
    if len(prices) < period:
        return ([np.nan] * len(prices), [np.nan] * len(prices), [np.nan] * len(prices))
    
    df = pd.Series(prices)
    
    # 中轨（SMA）
    middle = df.rolling(window=period).mean()
    
    # 标准差
    std = df.rolling(window=period).std()
    
    # 上下轨
    upper = middle + (std * std_dev)
    lower = middle - (std * std_dev)
    
    return upper.tolist(), middle.tolist(), lower.tolist()


def calculate_stochastic(highs: List[float], lows: List[float], closes: List[float], 
                        k_period: int = 14, d_period: int = 3) -> Tuple[List[float], List[float]]:
    """
    计算随机指标
    
    Args:
        highs: 最高价序列
        lows: 最低价序列
        closes: 收盘价序列
        k_period: K值周期
        d_period: D值周期
        
    Returns:
        (%K, %D)
    """
    if len(closes) < k_period:
        return ([np.nan] * len(closes), [np.nan] * len(closes))
    
    df = pd.DataFrame({
        'high': highs,
        'low': lows,
        'close': closes
    })
    
    # 计算最高价和最低价的滚动窗口
    lowest_low = df['low'].rolling(window=k_period).min()
    highest_high = df['high'].rolling(window=k_period).max()
    
    # 计算%K
    k_percent = 100 * (df['close'] - lowest_low) / (highest_high - lowest_low)
    
    # 计算%D（%K的移动平均）
    d_percent = k_percent.rolling(window=d_period).mean()
    
    return k_percent.tolist(), d_percent.tolist()


def calculate_atr(highs: List[float], lows: List[float], closes: List[float], period: int = 14) -> List[float]:
    """
    计算平均真实波幅
    
    Args:
        highs: 最高价序列
        lows: 最低价序列
        closes: 收盘价序列
        period: 周期
        
    Returns:
        ATR序列
    """
    if len(closes) < 2:
        return [np.nan] * len(closes)
    
    df = pd.DataFrame({
        'high': highs,
        'low': lows,
        'close': closes
    })
    
    # 计算真实波幅
    df['prev_close'] = df['close'].shift(1)
    df['tr1'] = df['high'] - df['low']
    df['tr2'] = abs(df['high'] - df['prev_close'])
    df['tr3'] = abs(df['low'] - df['prev_close'])
    
    df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
    
    # 计算ATR
    atr = df['tr'].rolling(window=period).mean()
    
    return atr.tolist()


def calculate_volatility(prices: List[float], period: int = 20) -> List[float]:
    """
    计算价格波动率
    
    Args:
        prices: 价格序列
        period: 周期
        
    Returns:
        波动率序列
    """
    if len(prices) < period + 1:
        return [np.nan] * len(prices)
    
    df = pd.Series(prices)
    returns = df.pct_change()
    volatility = returns.rolling(window=period).std() * np.sqrt(period)
    
    return volatility.tolist()