"""
币安事件合约自动化决策引擎
实现资金管理、风险控制、市场条件适应、投注额动态调整
"""
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging
from enum import Enum

# 尝试导入自定义logger，如果不存在则使用标准logger
try:
    from quant.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

from .event_contract_signal_generator_simple import SignalResult


class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "low"
    MEDIUM = "medium"  
    HIGH = "high"
    EXTREME = "extreme"


class MarketCondition(Enum):
    """市场条件枚举"""
    TRENDING = "trending"        # 趋势市场
    SIDEWAYS = "sideways"       # 横盘市场
    VOLATILE = "volatile"       # 波动市场
    UNKNOWN = "unknown"         # 未知条件


@dataclass
class TradingDecision:
    """交易决策结果"""
    should_trade: bool              # 是否应该交易
    bet_amount: float              # 投注金额（USDT）
    direction: str                 # 方向（UP/DOWN）
    confidence: float              # 信心度（0-100）
    risk_level: RiskLevel          # 风险等级
    market_condition: MarketCondition  # 市场条件
    reason: str                    # 决策原因
    timestamp: datetime            # 决策时间
    
    # 风险管理相关
    max_loss_allowed: float        # 允许的最大损失
    current_daily_loss: float      # 当日累计损失
    position_size_ratio: float     # 仓位比例
    
    # 附加信息
    market_score: float           # 市场评分（0-100）
    signal_strength: float        # 信号强度（0-100）
    entry_timing: str            # 入场时机评估


@dataclass
class RiskManagementConfig:
    """风险管理配置"""
    base_bet_amount: float = 20.0          # 基础投注金额
    daily_soft_limit: float = 1000.0       # 单日软限制
    daily_hard_limit: float = 10000.0      # 单日硬限制
    max_bet_amount: float = 200.0          # 最大单笔投注
    min_bet_amount: float = 10.0           # 最小单笔投注
    
    # 动态调整参数
    win_rate_threshold: float = 0.6        # 胜率阈值
    bet_increase_factor: float = 1.2       # 投注增加因子
    bet_decrease_factor: float = 0.8       # 投注减少因子
    
    # 市场条件调整
    trending_multiplier: float = 1.2       # 趋势市场倍数
    sideways_multiplier: float = 0.8       # 横盘市场倍数
    volatile_multiplier: float = 0.9       # 波动市场倍数


class EventContractDecisionEngine:
    """事件合约自动化决策引擎"""
    
    def __init__(self, config: Optional[RiskManagementConfig] = None):
        """
        初始化决策引擎
        
        Args:
            config: 风险管理配置，如果为None则使用默认配置
        """
        self.config = config or RiskManagementConfig()
        self.daily_trades: List[Dict] = []        # 当日交易记录
        self.trade_history: List[Dict] = []       # 历史交易记录
        self.current_balance: float = 0.0         # 当前余额
        self.today_start_balance: float = 0.0     # 今日开始余额
        
        logger.info("事件合约决策引擎已初始化")
    
    def make_trading_decision(self, 
                            signal_result: SignalResult,
                            current_balance: float,
                            market_data: Optional[Dict] = None) -> TradingDecision:
        """
        根据信号结果做出交易决策
        
        Args:
            signal_result: 信号生成器的结果
            current_balance: 当前账户余额
            market_data: 市场数据（可选）
            
        Returns:
            TradingDecision: 交易决策结果
        """
        self.current_balance = current_balance
        
        # 1. 基础检查
        if not signal_result.has_signal:
            return self._create_no_trade_decision("无有效交易信号")
        
        # 2. 风险检查
        daily_loss = self._calculate_daily_loss()
        if daily_loss >= self.config.daily_hard_limit:
            return self._create_no_trade_decision(f"达到硬限制：{daily_loss:.2f} USDT")
        
        # 3. 市场条件分析
        market_condition = self._analyze_market_condition(market_data)
        
        # 4. 计算投注金额
        bet_amount = self._calculate_bet_amount(signal_result, daily_loss, market_condition)
        
        # 5. 风险等级评估
        risk_level = self._assess_risk_level(signal_result, bet_amount, daily_loss)
        
        # 6. 最终决策
        should_trade = self._should_execute_trade(signal_result, bet_amount, risk_level, daily_loss)
        
        decision = TradingDecision(
            should_trade=should_trade,
            bet_amount=bet_amount,
            direction=signal_result.direction,
            confidence=signal_result.confidence,
            risk_level=risk_level,
            market_condition=market_condition,
            reason=self._generate_decision_reason(signal_result, should_trade, risk_level),
            timestamp=datetime.now(),
            max_loss_allowed=self.config.daily_soft_limit - daily_loss,
            current_daily_loss=daily_loss,
            position_size_ratio=bet_amount / current_balance if current_balance > 0 else 0,
            market_score=signal_result.technical_score,
            signal_strength=signal_result.confidence,
            entry_timing=getattr(signal_result, 'entry_timing', '良好')
        )
        
        logger.info(f"交易决策: {'执行' if should_trade else '跳过'} - "
                   f"方向:{signal_result.direction}, "
                   f"金额:{bet_amount:.2f} USDT, "
                   f"风险:{risk_level.value}")
        
        return decision
    
    def _create_no_trade_decision(self, reason: str) -> TradingDecision:
        """创建不交易决策"""
        return TradingDecision(
            should_trade=False,
            bet_amount=0.0,
            direction="NONE",
            confidence=0.0,
            risk_level=RiskLevel.LOW,
            market_condition=MarketCondition.UNKNOWN,
            reason=reason,
            timestamp=datetime.now(),
            max_loss_allowed=0.0,
            current_daily_loss=self._calculate_daily_loss(),
            position_size_ratio=0.0,
            market_score=0.0,
            signal_strength=0.0,
            entry_timing="无"
        )
    
    def _calculate_daily_loss(self) -> float:
        """计算当日损失"""
        today = datetime.now().date()
        daily_loss = 0.0
        
        for trade in self.daily_trades:
            if trade.get('date', datetime.now().date()) == today:
                pnl = trade.get('pnl', 0.0)
                if pnl < 0:
                    daily_loss += abs(pnl)
        
        return daily_loss
    
    def _analyze_market_condition(self, market_data: Optional[Dict]) -> MarketCondition:
        """分析市场条件"""
        if not market_data:
            return MarketCondition.UNKNOWN
        
        # 获取市场指标
        volatility = market_data.get('volatility', 0.0)
        trend_strength = market_data.get('trend_strength', 0.0)
        price_change = market_data.get('price_change_24h', 0.0)
        
        # 根据指标判断市场条件
        if trend_strength > 0.7:
            return MarketCondition.TRENDING
        elif volatility > 0.05:
            return MarketCondition.VOLATILE
        elif abs(price_change) < 0.02:
            return MarketCondition.SIDEWAYS
        else:
            return MarketCondition.UNKNOWN
    
    def _calculate_bet_amount(self, 
                            signal_result: SignalResult,
                            daily_loss: float,
                            market_condition: MarketCondition) -> float:
        """计算投注金额"""
        base_amount = self.config.base_bet_amount
        
        # 1. 根据胜率调整
        win_rate = self._calculate_current_win_rate()
        if win_rate > self.config.win_rate_threshold:
            base_amount *= self.config.bet_increase_factor
        elif win_rate < (self.config.win_rate_threshold - 0.1):
            base_amount *= self.config.bet_decrease_factor
        
        # 2. 根据信号强度调整
        confidence_multiplier = min(signal_result.confidence / 100.0, 1.0)
        base_amount *= (0.8 + 0.4 * confidence_multiplier)  # 0.8-1.2倍调整
        
        # 3. 根据市场条件调整
        if market_condition == MarketCondition.TRENDING:
            base_amount *= self.config.trending_multiplier
        elif market_condition == MarketCondition.SIDEWAYS:
            base_amount *= self.config.sideways_multiplier
        elif market_condition == MarketCondition.VOLATILE:
            base_amount *= self.config.volatile_multiplier
        
        # 4. 根据当日损失调整
        if daily_loss > self.config.daily_soft_limit * 0.5:
            base_amount *= 0.5  # 损失过半时减半
        elif daily_loss > self.config.daily_soft_limit * 0.8:
            base_amount *= 0.3  # 接近限制时大幅降低
        
        # 5. 限制在最大最小范围内
        bet_amount = max(self.config.min_bet_amount, 
                        min(base_amount, self.config.max_bet_amount))
        
        # 6. 确保不超过可用余额的5%
        if self.current_balance > 0:
            max_position = self.current_balance * 0.05
            bet_amount = min(bet_amount, max_position)
        
        return round(bet_amount, 2)
    
    def _calculate_current_win_rate(self) -> float:
        """计算当前胜率"""
        if len(self.trade_history) < 5:
            return 0.5  # 默认50%胜率
        
        recent_trades = self.trade_history[-20:]  # 最近20笔交易
        wins = sum(1 for trade in recent_trades if trade.get('pnl', 0) > 0)
        
        return wins / len(recent_trades)
    
    def _assess_risk_level(self, 
                          signal_result: SignalResult,
                          bet_amount: float,
                          daily_loss: float) -> RiskLevel:
        """评估风险等级"""
        risk_score = 0
        
        # 信号强度风险
        if signal_result.confidence < 70:
            risk_score += 2
        elif signal_result.confidence < 80:
            risk_score += 1
        
        # 投注金额风险
        if bet_amount > self.config.base_bet_amount * 1.5:
            risk_score += 2
        elif bet_amount > self.config.base_bet_amount * 1.2:
            risk_score += 1
        
        # 当日损失风险
        loss_ratio = daily_loss / self.config.daily_soft_limit
        if loss_ratio > 0.8:
            risk_score += 3
        elif loss_ratio > 0.5:
            risk_score += 2
        elif loss_ratio > 0.3:
            risk_score += 1
        
        # 技术分析风险
        if signal_result.technical_score < 60:
            risk_score += 2
        elif signal_result.technical_score < 70:
            risk_score += 1
        
        # 根据风险分数确定等级
        if risk_score >= 6:
            return RiskLevel.EXTREME
        elif risk_score >= 4:
            return RiskLevel.HIGH
        elif risk_score >= 2:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _should_execute_trade(self, 
                        signal_result: SignalResult,
                        bet_amount: float,
                        risk_level: RiskLevel,
                        daily_loss: float) -> bool:
        """判断是否应该执行交易"""
        # 极端风险不交易
        if risk_level == RiskLevel.EXTREME:
            return False
        
        # 接近软限制时谨慎交易
        if daily_loss > self.config.daily_soft_limit * 0.9:
            return signal_result.confidence > 80  # 从90降到80
        
        # 高风险需要中等信心
        if risk_level == RiskLevel.HIGH:
            return signal_result.confidence > 55  # 从85降到55
        
        # 中等风险需要较低信心
        if risk_level == RiskLevel.MEDIUM:
            return signal_result.confidence > 45  # 从75降到45
        
        # 低风险需要基础信心
        return signal_result.confidence > 35  # 从70降到35
    
    def _generate_decision_reason(self, 
                                signal_result: SignalResult,
                                should_trade: bool,
                                risk_level: RiskLevel) -> str:
        """生成决策原因"""
        if not should_trade:
            if risk_level == RiskLevel.EXTREME:
                return "风险过高，暂停交易"
            elif signal_result.confidence < 70:
                return "信号强度不足"
            else:
                return "风险控制，跳过交易"
        else:
            return f"满足交易条件：信心度{signal_result.confidence:.1f}%，风险{risk_level.value}"
    
    def record_trade(self, 
                    decision: TradingDecision,
                    order_id: str,
                    executed_price: float,
                    executed_amount: float) -> None:
        """记录交易"""
        trade_record = {
            'timestamp': datetime.now(),
            'date': datetime.now().date(),
            'order_id': order_id,
            'direction': decision.direction,
            'bet_amount': executed_amount,
            'executed_price': executed_price,
            'confidence': decision.confidence,
            'risk_level': decision.risk_level.value,
            'market_condition': decision.market_condition.value,
            'reason': decision.reason,
            'pnl': None,  # 将在结算时更新
            'status': 'pending'
        }
        
        self.daily_trades.append(trade_record)
        self.trade_history.append(trade_record)
        
        logger.info(f"记录交易：{order_id} - {decision.direction} - {executed_amount:.2f} USDT")
    
    def update_trade_result(self, 
                          order_id: str,
                          result: str,
                          pnl: float) -> None:
        """更新交易结果"""
        for trade in self.daily_trades:
            if trade.get('order_id') == order_id:
                trade['pnl'] = pnl
                trade['status'] = result
                trade['settlement_time'] = datetime.now()
                break
        
        for trade in self.trade_history:
            if trade.get('order_id') == order_id:
                trade['pnl'] = pnl
                trade['status'] = result
                trade['settlement_time'] = datetime.now()
                break
        
        logger.info(f"更新交易结果：{order_id} - {result} - PnL: {pnl:.2f} USDT")
    
    def get_daily_stats(self) -> Dict:
        """获取当日统计"""
        today = datetime.now().date()
        today_trades = [t for t in self.daily_trades if t.get('date') == today]
        
        if not today_trades:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'total_pnl': 0.0,
                'total_bet': 0.0,
                'avg_bet': 0.0,
                'max_loss': 0.0,
                'current_streak': 0
            }
        
        completed_trades = [t for t in today_trades if t.get('pnl') is not None]
        
        if not completed_trades:
            return {
                'total_trades': len(today_trades),
                'win_rate': 0.0,
                'total_pnl': 0.0,
                'total_bet': sum(t.get('bet_amount', 0) for t in today_trades),
                'avg_bet': sum(t.get('bet_amount', 0) for t in today_trades) / len(today_trades),
                'max_loss': 0.0,
                'current_streak': 0
            }
        
        wins = sum(1 for t in completed_trades if t.get('pnl', 0) > 0)
        win_rate = wins / len(completed_trades)
        total_pnl = sum(t.get('pnl', 0) for t in completed_trades)
        total_bet = sum(t.get('bet_amount', 0) for t in completed_trades)
        losses = [t.get('pnl', 0) for t in completed_trades if t.get('pnl', 0) < 0]
        max_loss = min(losses) if losses else 0.0
        
        # 计算连胜/连败
        current_streak = 0
        for trade in reversed(completed_trades):
            pnl = trade.get('pnl', 0)
            if (pnl > 0 and current_streak >= 0) or (pnl < 0 and current_streak <= 0):
                current_streak += 1 if pnl > 0 else -1
            else:
                break
        
        return {
            'total_trades': len(completed_trades),
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'total_bet': total_bet,
            'avg_bet': total_bet / len(completed_trades),
            'max_loss': max_loss,
            'current_streak': current_streak
        }
    
    def should_stop_trading(self) -> Tuple[bool, str]:
        """判断是否应该停止交易"""
        daily_loss = self._calculate_daily_loss()
        
        # 硬限制
        if daily_loss >= self.config.daily_hard_limit:
            return True, f"达到硬限制：{daily_loss:.2f} USDT"
        
        # 软限制
        if daily_loss >= self.config.daily_soft_limit:
            return True, f"达到软限制：{daily_loss:.2f} USDT"
        
        # 连续亏损检查
        stats = self.get_daily_stats()
        if stats['current_streak'] <= -5:  # 连续5次亏损
            return True, f"连续亏损{abs(stats['current_streak'])}次，暂停交易"
        
        return False, ""
    
    def reset_daily_data(self) -> None:
        """重置当日数据"""
        self.daily_trades = []
        self.today_start_balance = self.current_balance
        logger.info("重置当日交易数据")
    
    def get_risk_summary(self) -> Dict:
        """获取风险摘要"""
        daily_loss = self._calculate_daily_loss()
        stats = self.get_daily_stats()
        
        return {
            'daily_loss': daily_loss,
            'daily_loss_ratio': daily_loss / self.config.daily_soft_limit,
            'remaining_soft_limit': self.config.daily_soft_limit - daily_loss,
            'remaining_hard_limit': self.config.daily_hard_limit - daily_loss,
            'current_win_rate': self._calculate_current_win_rate(),
            'today_win_rate': stats['win_rate'],
            'current_streak': stats['current_streak'],
            'total_trades_today': stats['total_trades'],
            'should_stop': self.should_stop_trading()[0]
        }