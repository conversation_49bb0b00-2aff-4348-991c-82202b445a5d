"""
币安事件合约钉钉通知集成模块
发送交易信号、结算通知、风险提醒到钉钉群组
"""
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import logging

# 尝试导入自定义logger，如果不存在则使用标准logger
try:
    from quant.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

from quant.utils.dingtalk import Dingtalk
from quant.config import config
from .event_contract_decision_engine import TradingDecision, RiskLevel
from .event_contract_signal_generator_simple import SignalResult


@dataclass
class NotificationConfig:
    """通知配置"""
    enable_signal_notification: bool = True      # 启用交易信号通知
    enable_settlement_notification: bool = True  # 启用结算通知
    enable_risk_notification: bool = True        # 启用风险提醒
    enable_daily_summary: bool = True            # 启用日报
    
    # 钉钉Webhook URL (如果为None，则使用全局配置)
    dingtalk_token: Optional[str] = None
    
    # 通知频率控制
    min_signal_interval: int = 300  # 最小信号通知间隔（秒）
    max_daily_notifications: int = 50  # 每日最大通知数
    
    # 风险提醒阈值
    risk_notification_threshold: float = 0.5  # 损失比例阈值
    consecutive_loss_threshold: int = 3       # 连续亏损阈值


class EventContractDingtalkNotifier:
    """事件合约钉钉通知器"""
    
    def __init__(self, config_obj: Optional[NotificationConfig] = None):
        """
        初始化通知器
        
        Args:
            config_obj: 通知配置，如果为None则使用默认配置
        """
        self.config = config_obj or NotificationConfig()
        self.last_notification_time = None
        self.daily_notification_count = 0
        self.notification_history = []
        
        # 如果配置中没有指定钉钉token，则使用全局配置
        if not self.config.dingtalk_token:
            self.config.dingtalk_token = config.dingtalk
        
        logger.info(f"事件合约钉钉通知器已初始化，Token: {self.config.dingtalk_token is not None}")
    
    def send_trading_signal(self, 
                          decision: TradingDecision,
                          signal_result: SignalResult,
                          market_data: Optional[Dict] = None) -> Tuple[bool, Optional[str]]:
        """
        发送交易信号通知（包含关键词：交易、小火箭）
        
        Args:
            decision: 交易决策结果
            signal_result: 信号生成结果
            market_data: 市场数据
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_signal_notification:
            return True, None
        
        # 检查通知频率限制
        if not self._check_notification_limit():
            return False, "通知频率超限"
        
        # 构建消息内容
        message = self._build_signal_message(decision, signal_result, market_data)
        
        # 发送通知
        success, error = self._send_dingtalk_message(message)
        
        if success:
            self._record_notification("trading_signal", decision.direction)
            logger.info(f"交易信号通知已发送: {decision.direction}")
        else:
            logger.error(f"交易信号通知发送失败: {error}")
        
        return success, error
    
    def send_settlement_notification(self, 
                                   order_id: str,
                                   result: str,
                                   pnl: float,
                                   decision: TradingDecision) -> Tuple[bool, Optional[str]]:
        """
        发送结算通知
        
        Args:
            order_id: 订单ID
            result: 结算结果（win/loss）
            pnl: 盈亏金额
            decision: 原交易决策
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_settlement_notification:
            return True, None
        
        # 构建结算消息
        message = self._build_settlement_message(order_id, result, pnl, decision)
        
        # 发送通知
        success, error = self._send_dingtalk_message(message)
        
        if success:
            self._record_notification("settlement", result)
            logger.info(f"结算通知已发送: {order_id} - {result}")
        else:
            logger.error(f"结算通知发送失败: {error}")
        
        return success, error
    
    def send_risk_alert(self, 
                       risk_type: str,
                       message: str,
                       current_loss: float,
                       loss_ratio: float) -> Tuple[bool, Optional[str]]:
        """
        发送风险提醒
        
        Args:
            risk_type: 风险类型
            message: 风险消息
            current_loss: 当前损失
            loss_ratio: 损失比例
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_risk_notification:
            return True, None
        
        # 构建风险提醒消息
        alert_message = self._build_risk_alert_message(risk_type, message, current_loss, loss_ratio)
        
        # 发送通知
        success, error = self._send_dingtalk_message(alert_message)
        
        if success:
            self._record_notification("risk_alert", risk_type)
            logger.info(f"风险提醒已发送: {risk_type}")
        else:
            logger.error(f"风险提醒发送失败: {error}")
        
        return success, error
    
    def send_daily_summary(self, 
                         stats: Dict,
                         risk_summary: Dict) -> Tuple[bool, Optional[str]]:
        """
        发送每日交易总结
        
        Args:
            stats: 交易统计数据
            risk_summary: 风险摘要
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_daily_summary:
            return True, None
        
        # 构建日报消息
        message = self._build_daily_summary_message(stats, risk_summary)
        
        # 发送通知
        success, error = self._send_dingtalk_message(message)
        
        if success:
            self._record_notification("daily_summary", "report")
            logger.info("每日交易总结已发送")
        else:
            logger.error(f"每日交易总结发送失败: {error}")
        
        return success, error

    # === 新增: 发送推荐 ===
    def send_recommendation(self, recommendation: Dict) -> Tuple[bool, Optional[str]]:
        """发送最终交易推荐（无需 TradingDecision）。"""

        if not self.config.enable_signal_notification:
            return True, None

        # 频率检查
        if not self._check_notification_limit():
            return False, "通知频率超限"

        message = self._build_recommendation_message(recommendation)
        success, error = self._send_dingtalk_message(message)
        if success:
            self._record_notification("recommendation", recommendation.get("direction", ""))
        return success, error

    def send_pending_signal(self, signal_result: SignalResult, market_data: Optional[Dict] = None) -> Tuple[bool, Optional[str]]:
        """
        发送pending信号提醒（第一阶段）
        
        Args:
            signal_result: 信号生成结果
            market_data: 市场数据
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        if not self.config.enable_signal_notification:
            return True, None
        
        # 检查通知频率限制
        if not self._check_notification_limit():
            return False, "通知频率超限"
        
        # 构建pending信号消息
        message = self._build_pending_signal_message(signal_result, market_data)
        
        # 发送通知
        success, error = self._send_dingtalk_message(message)
        
        if success:
            self._record_notification("pending_signal", signal_result.direction)
            logger.info(f"Pending信号通知已发送: {signal_result.direction}")
        else:
            logger.error(f"Pending信号通知发送失败: {error}")
        
        return success, error

    def _build_recommendation_message(self, rec: Dict) -> str:
        """构造推荐消息文本。"""
        direction_emoji = "🚀" if rec.get("direction") == "UP" else "📉"
        stake = rec.get("stake", 0)
        confidence = rec.get("confidence", 0)
        score = rec.get("score", 0)
        remaining = rec.get("remaining_time", "-")
        
        # 获取信号ID和价格信息
        signal_id = getattr(rec.get("signal_result", None), "signal_id", None) or "未找到"
        signal_price = getattr(rec.get("signal_result", None), "signal_price", 0.0)
        
        # 如果没有从signal_result获取到价格，尝试从recommendation中获取
        if signal_price <= 0:
            signal_price = rec.get("signal_price", 0.0)
        
        message = f"🎯 **事件合约交易推荐** {direction_emoji}\n\n"
        
        # 基础信息
        message += f"🆔 信号ID: {signal_id}\n"
        message += f"💰 信号价格: {signal_price:.2f} USDT\n\n"
        
        # 交易详情
        message += f"📈 方向: **{rec.get('direction')}**\n"
        message += f"💵 建议投入: **{stake} USDT**\n"
        message += f"🎯 置信度: **{confidence:.1f}%**\n"
        message += f"📊 因子得分: **{score:.1f}/100**\n"
        message += f"⏰ 剩余时间: {remaining}s\n"
        message += f"🕒 生成时间: {rec.get('generated_at')}\n\n"
        
        # 提示信息
        message += f"💡 该推荐仅供参考，实际操作请自行评估风控。"
        
        return message
    def _build_pending_signal_message(self, signal_result: SignalResult, market_data: Optional[Dict] = None) -> str:
    """构建pending信号消息（第一阶段）"""
    # 根据方向选择图标
    direction_icon = "🚀" if signal_result.direction == "UP" else "📉"
    
    # 获取准确的K线序号信息
    kline_sequence = market_data.get('kline_sequence', 0) if market_data else 0
    signal_count = market_data.get('signal_count', 0) if market_data else 0
    kline_time = market_data.get('kline_time', '未知') if market_data else '未知'
    
    # 获取信号ID和价格信息
    signal_id = market_data.get('signal_id', '未分配') if market_data else '未分配'
    signal_price = market_data.get('signal_price', 0.0) if market_data else 0.0
    
    # 构建清晰的消息格式
    message = f"🔔 **潜在信号检测** {direction_icon}\n\n"
    
    # 信号ID和价格信息
    message += f"🆔 信号ID: {signal_id}\n"
    message += f"💰 信号价格: {signal_price:.2f} USDT\n\n"
    
    # K线和信号序号信息
    message += f"📊 K线序号: 第{kline_sequence}根15分钟K线 ({kline_time})\n"
    message += f"🔢 信号序号: 今日第{signal_count}个信号\n\n"
    
    # 交易信息
    message += f"📈 交易方向: {signal_result.direction} {direction_icon}\n"
    message += f"🎯 置信度: {signal_result.confidence:.1f}%\n"
    message += f"📊 技术分: {signal_result.technical_score:.1f}分\n"
    message += f"⚠️ 风险等级: {signal_result.risk_level}\n\n"
    
    # 市场条件
    market_status = getattr(signal_result, 'market_status', None)
    if market_status and market_status != "":
        message += f"🌟 市场条件: {market_status}\n"
    else:
        # 根据置信度推断市场条件
        if signal_result.confidence >= 70:
            market_condition = "强势信号"
        elif signal_result.confidence >= 60:
            market_condition = "中等信号"
        elif signal_result.confidence >= 50:
            market_condition = "弱势信号"
        else:
            market_condition = "信号待确认"
        message += f"🌟 市场条件: {market_condition}\n"
    
    # 决策原因
    reason = f"满足交易条件: 信心度{signal_result.confidence:.1f}%, 风险{signal_result.risk_level}"
    message += f"💡 决策原因: {reason}\n\n"
    
    # 市场提醒
    if hasattr(signal_result, 'user_reminder') and signal_result.user_reminder:
        message += f"🔍 市场提醒: 【交易信号提醒】📈 检测到信号！建议关注交易机会\n\n"
    
    # 时间戳
    from datetime import datetime
    message += f"⏰ 信号时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    
    # K线进度信息
    message += f"📈 今日进度: {kline_sequence}/96 (15分钟K线)\n\n"
    
    # 等待提醒
    message += f"⏳ 等待入场时机评估... 系统将在接下来的15分钟内寻找最佳入场点\n\n"
    
    # 跟踪提醒
    message += f"🏷️ 跟踪提醒: 请记住信号ID [{signal_id}], 用于结算通知对应\n\n"
    
    # 小火箭祝福
    message += f"🎉 祝**交易**顺利！{direction_icon}小火箭{direction_icon}起飞！"
    
    return message
    
    def _build_signal_message(self, 
                            decision: TradingDecision,
                            signal_result: SignalResult,
                            market_data: Optional[Dict] = None) -> str:
        """构建交易信号消息"""
        # 根据方向选择小火箭
        rocket = "🚀" if decision.direction == "UP" else "📉"
        
        # 🆕 获取K线序号信息
        kline_sequence = market_data.get('kline_sequence', 0) if market_data else 0
        daily_kline_count = market_data.get('daily_kline_count', 0) if market_data else 0
        signal_count = market_data.get('signal_count', 0) if market_data else 0
        kline_time = market_data.get('kline_time', '未知') if market_data else '未知'
        
        # 🆕 构建序号标识
        sequence_info = f"📊 **K线序号:** 第{kline_sequence}根15分钟K线 ({kline_time})"
        signal_info = f"🔢 **信号序号:** 今日第{signal_count}个信号"
        
        # 构建消息
        message = f"### 🎯 **交易**信号通知 {rocket}\n\n"
        
        # 🆕 添加序号信息（置于顶部）
        message += f"{sequence_info}\n\n"
        message += f"{signal_info}\n\n"
        message += f"{'='*30}\n\n"
        
        # 基本信息
        message += f"> **交易方向:** {decision.direction} {rocket}\n\n"
        message += f"> **投注金额:** {decision.bet_amount:.2f} USDT\n\n"
        message += f"> **信心度:** {decision.confidence:.1f}%\n\n"
        message += f"> **技术分析:** {signal_result.technical_score:.1f}分\n\n"
        
        # 处理风险等级（兼容枚举和字符串）
        risk_level_str = decision.risk_level.value if hasattr(decision.risk_level, 'value') else str(decision.risk_level)
        message += f"> **风险等级:** {risk_level_str.upper()}\n\n"
        
        # 处理市场条件（兼容枚举和字符串）
        market_condition_str = decision.market_condition.value if hasattr(decision.market_condition, 'value') else str(decision.market_condition)
        message += f"> **市场条件:** {market_condition_str}\n\n"
        
        # 市场数据
        if market_data:
            current_price = market_data.get('current_price', market_data.get('price', 'N/A'))
            if current_price and current_price != 'N/A':
                message += f"> **当前价格:** {current_price:.2f} USDT\n\n"
            
            volume = market_data.get('volume', 0)
            if volume > 0:
                message += f"> **成交量:** {volume:,.0f}\n\n"
            
            if 'volatility' in market_data:
                message += f"> **波动率:** {market_data.get('volatility', 0):.2%}\n\n"
        
        # 决策原因
        message += f"> **决策原因:** {decision.reason}\n\n"
        
        # 用户提醒
        if signal_result.user_reminder:
            message += f"> **市场提醒:** {signal_result.user_reminder}\n\n"
        
        # 时间戳
        message += f"> **信号时间:** {decision.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 🆕 添加K线进度信息
        progress_info = f"📈 **今日进度:** {kline_sequence}/96 (15分钟K线)"
        message += f"{progress_info}\n\n"
        
        # 风险提示
        if decision.risk_level in [RiskLevel.HIGH, RiskLevel.EXTREME] or str(decision.risk_level).upper() in ['HIGH', 'EXTREME']:
            message += f"⚠️ **风险提示:** 当前风险等级较高，请谨慎操作\n\n"
        
        # 小火箭祝福
        message += f"🎉 祝**交易**顺利！{rocket}小火箭{rocket}起飞！"
        
        return message
    
    def _build_settlement_message(self, 
                                order_id: str,
                                result: str,
                                pnl: float,
                                decision: TradingDecision) -> str:
        """构建结算消息"""
        # 结果图标和文本
        if result == "win":
            result_icon = "🎉"
            result_text = "预测正确·盈利"
            pnl_icon = "💰"
        elif result == "tie":
            result_icon = "🤝"
            result_text = "价格相等·回本"
            pnl_icon = "💳"
        else:
            result_icon = "😔"
            result_text = "预测错误·亏损"
            pnl_icon = "💸"
        
        message = f"### 📊 **交易**结算通知 {result_icon}\n\n"
        
        # 基本信息
        message += f"> **订单编号:** {order_id}\n\n"
        message += f"> **交易方向:** {decision.direction}\n\n"
        message += f"> **投注金额:** {decision.bet_amount:.2f} USDT\n\n"
        message += f"> **结算结果:** {result_text} {result_icon}\n\n"
        message += f"> **盈亏金额:** {pnl:+.2f} USDT {pnl_icon}\n\n"
        
        # 收益率
        if decision.bet_amount > 0:
            return_rate = (pnl / decision.bet_amount) * 100
            message += f"> **收益率:** {return_rate:+.1f}%\n\n"
        
        # 盈亏规则说明
        if result == "win":
            message += f"> **盈利说明:** 预测正确，获得80%收益\n\n"
        elif result == "tie":
            message += f"> **回本说明:** 价格相等，回本处理\n\n"
        else:
            message += f"> **亏损说明:** 预测错误，损失全部投入\n\n"
        
        # 原始信心度
        message += f"> **原始信心度:** {decision.confidence:.1f}%\n\n"
        
        # 结算时间
        message += f"> **结算时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 鼓励或安慰
        if result == "win":
            message += f"🎊 恭喜！**交易**成功！继续保持！🚀"
        else:
            message += f"💪 虽然这次亏损，但经验很宝贵！下次**交易**会更好！"
        
        return message
    
    def _build_risk_alert_message(self, 
                                risk_type: str,
                                alert_message: str,
                                current_loss: float,
                                loss_ratio: float) -> str:
        """构建风险提醒消息"""
        message = f"### ⚠️ **交易**风险提醒\n\n"
        
        # 风险类型
        message += f"> **风险类型:** {risk_type}\n\n"
        
        # 风险描述
        message += f"> **风险描述:** {alert_message}\n\n"
        
        # 当前损失
        message += f"> **当前损失:** {current_loss:.2f} USDT\n\n"
        
        # 损失比例
        message += f"> **损失比例:** {loss_ratio:.1%}\n\n"
        
        # 提醒时间
        message += f"> **提醒时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 建议
        if loss_ratio > 0.8:
            message += f"🛑 **建议:** 立即停止**交易**，检查策略！\n\n"
        elif loss_ratio > 0.5:
            message += f"⚠️ **建议:** 谨慎**交易**，降低仓位！\n\n"
        else:
            message += f"💡 **建议:** 关注风险，适当调整**交易**策略！\n\n"
        
        message += f"🔒 风险控制是**交易**成功的关键！"
        
        return message
    
    def _build_daily_summary_message(self, 
                                   stats: Dict,
                                   risk_summary: Dict) -> str:
        """构建每日总结消息"""
        message = f"### 📈 **交易**每日总结报告\n\n"
        
        # 日期
        message += f"> **报告日期:** {datetime.now().strftime('%Y-%m-%d')}\n\n"
        
        # 交易统计
        message += f"#### 📊 **交易**绩效\n"
        message += f"> **总交易数:** {stats.get('total_trades', 0)}\n\n"
        message += f"> **胜/负/平:** {stats.get('wins', 0)}/{stats.get('losses', 0)}/{stats.get('ties', 0)}\n\n"
        message += f"> **胜率:** {stats.get('win_rate', 0):.1%}\n\n"
        message += f"> **总盈亏:** {stats.get('total_pnl', 0):+.2f} USDT\n\n"
        message += f"> **平均投注:** {stats.get('avg_bet', 0):.2f} USDT\n\n"
        
        # 连胜连败
        streak = stats.get('current_streak', 0)
        if streak > 0:
            message += f"> **连胜:** {streak} 次 🎉\n\n"
        elif streak < 0:
            message += f"> **连败:** {abs(streak)} 次 😔\n\n"
        else:
            message += f"> **连胜连败:** 0 次\n\n"
        
        # 风险控制
        message += f"#### ⚠️ 风险控制\n"
        message += f"> **当日损失:** {risk_summary.get('daily_loss', 0):.2f} USDT\n\n"
        message += f"> **损失比例:** {risk_summary.get('daily_loss_ratio', 0):.1%}\n\n"
        message += f"> **剩余限额:** {risk_summary.get('remaining_soft_limit', 0):.2f} USDT\n\n"
        
        # 总结
        if stats.get('win_rate', 0) > 0.6:
            message += f"🎊 今日**交易**表现优秀！继续保持！🚀"
        elif stats.get('win_rate', 0) > 0.4:
            message += f"💪 今日**交易**表现平稳，继续优化策略！"
        else:
            message += f"🔍 今日**交易**需要总结经验，明天会更好！"
        
        return message
    
    def _send_dingtalk_message(self, message: str) -> Tuple[bool, Optional[str]]:
        """发送钉钉消息"""
        try:
            # 使用配置的token，如果没有则使用全局配置
            token = self.config.dingtalk_token or config.dingtalk
            
            success, error = Dingtalk.markdown(
                content=message,
                token=token
            )
            return success is not None, error
        except Exception as e:
            logger.error(f"钉钉消息发送异常: {e}")
            return False, str(e)
    
    def _check_notification_limit(self) -> bool:
        """检查通知频率限制"""
        now = datetime.now()
        
        # 检查每日通知数量限制
        if self.daily_notification_count >= self.config.max_daily_notifications:
            return False
        
        # 检查最小间隔
        if self.last_notification_time:
            time_diff = (now - self.last_notification_time).total_seconds()
            if time_diff < self.config.min_signal_interval:
                return False
        
        return True
    
    def _record_notification(self, notification_type: str, content: str) -> None:
        """记录通知"""
        self.last_notification_time = datetime.now()
        self.daily_notification_count += 1
        
        self.notification_history.append({
            'timestamp': datetime.now(),
            'type': notification_type,
            'content': content
        })
        
        # 保留最近100条记录
        if len(self.notification_history) > 100:
            self.notification_history = self.notification_history[-100:]
    
    def reset_daily_counters(self) -> None:
        """重置每日计数器"""
        self.daily_notification_count = 0
        self.last_notification_time = None
        logger.info("每日通知计数器已重置")
    
    def get_notification_stats(self) -> Dict:
        """获取通知统计"""
        today = datetime.now().date()
        today_notifications = [
            n for n in self.notification_history 
            if n['timestamp'].date() == today
        ]
        
        return {
            'total_today': len(today_notifications),
            'remaining_quota': self.config.max_daily_notifications - self.daily_notification_count,
            'last_notification': self.last_notification_time.isoformat() if self.last_notification_time else None,
            'types_today': {
                'trading_signal': len([n for n in today_notifications if n['type'] == 'trading_signal']),
                'settlement': len([n for n in today_notifications if n['type'] == 'settlement']),
                'risk_alert': len([n for n in today_notifications if n['type'] == 'risk_alert']),
                'daily_summary': len([n for n in today_notifications if n['type'] == 'daily_summary'])
            }
        }
    
    async def send_message(self, message: str) -> Tuple[bool, Optional[str]]:
        """
        发送通用消息
        
        Args:
            message: 消息内容
            
        Returns:
            Tuple[bool, Optional[str]]: 成功标志和错误信息
        """
        return self._send_dingtalk_message(message)