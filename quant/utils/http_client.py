"""Http Client."""


from urllib.parse import urlparse
from requests import sessions
import threading

from quant.config import config
from quant.utils import logger


class HttpRequests:

    _SESSIONS = {}
    _LOGGING_LOCK = threading.Lock()
    _IN_ERROR_LOGGING = threading.local()

    @classmethod
    def request(cls, method, url, **kwargs):
        session = cls._get_session(url)
        if config.proxy:
            kwargs["proxies"] = {"http": "{}".format(config.proxy), "https": "{}".format(config.proxy)}
        try:
            response = session.request(method=method, url=url, **kwargs)
        except Exception as e:
            cls._safe_log_error("method:", method, "url:", url, "kwargs:", kwargs, "Error:", e)
            return None, e
        code = response.status_code
        if code not in (200, 201, 202, 203, 204, 205, 206):
            text = response.text
            cls._safe_log_error("method:", method, "url:", url, "kwargs:", kwargs, "Error:", text)
            return None, text
        try:
            result = response.json()
        except:
            result = response.text
        return result, None

    @classmethod
    def _safe_log_error(cls, *args, **kwargs):
        """安全的错误日志记录，防止递归调用"""
        # 检查是否已经在错误日志记录中，防止递归
        if hasattr(cls._IN_ERROR_LOGGING, 'logging') and cls._IN_ERROR_LOGGING.logging:
            # 如果已经在记录错误日志，直接打印到控制台避免递归
            print(f"[HttpRequests.ERROR] {' '.join(str(arg) for arg in args)}")
            return

        try:
            cls._IN_ERROR_LOGGING.logging = True
            # 简化kwargs以避免复杂对象序列化问题
            safe_kwargs = {}
            for k, v in kwargs.items():
                if k == 'kwargs' and isinstance(v, dict):
                    # 简化复杂的kwargs对象
                    safe_kwargs[k] = {key: str(val)[:100] if val else None for key, val in v.items()}
                else:
                    safe_kwargs[k] = str(v)[:100] if v else None

            logger.error(*args, caller=cls, **safe_kwargs)
        except Exception as log_error:
            # 如果日志记录失败，直接打印到控制台
            print(f"[HttpRequests.CRITICAL] Logging failed: {log_error}")
            print(f"[HttpRequests.ORIGINAL] {' '.join(str(arg) for arg in args)}")
        finally:
            cls._IN_ERROR_LOGGING.logging = False

    @classmethod
    def get(cls, url, params=None, **kwargs):
        kwargs.setdefault("allow_redirects", True)
        return cls.request("get", url, params=params, **kwargs)

    @classmethod
    def options(cls, url, **kwargs):
        kwargs.setdefault("allow_redirects", True)
        return cls.request("options", url, **kwargs)

    @classmethod
    def head(cls, url, **kwargs):
        kwargs.setdefault("allow_redirects", False)
        return cls.request("head", url, **kwargs)

    @classmethod
    def post(cls, url, data=None, json=None, **kwargs):
        return cls.request("post", url, data=data, json=json, **kwargs)

    @classmethod
    def put(cls, url, data=None, **kwargs):
        return cls.request("put", url, data=data, **kwargs)

    @classmethod
    def patch(cls, url, data=None, **kwargs):
        return cls.request("patch", url, data=data, **kwargs)

    @classmethod
    def delete(cls, url, **kwargs):
        return cls.request("delete", url, **kwargs)

    @classmethod
    def fetch(cls, method, url, body=None, headers=None, timeout=10, **kwargs):
        """向后兼容的 fetch 接口，内部调用 request。"""
        if body is not None:
            # 如果 body 是 dict 默认作为 json，否则作为 data
            if isinstance(body, dict):
                kwargs['json'] = body
            else:
                kwargs['data'] = body
        if headers:
            kwargs['headers'] = headers
        kwargs['timeout'] = timeout
        return cls.request(method, url, **kwargs)

    @classmethod
    def _get_session(cls, url):
        """Get the connection session for url's domain, if no session, create a new.

        Args:
            url: HTTP request url.

        Returns:
            session: HTTP request session.
        """
        parsed_url = urlparse(url)
        key = parsed_url.netloc or parsed_url.hostname
        if key not in cls._SESSIONS:
            session = sessions.Session()
            cls._SESSIONS[key] = session
        return cls._SESSIONS[key]
