"""
网络健康检查工具
用于监控和诊断网络连接状态
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import traceback

from quant.utils.http_client import HttpRequests
from quant.utils import logger


class NetworkHealthChecker:
    """网络健康检查器"""
    
    def __init__(self):
        self.check_history = []
        self.last_check_time = None
        self.consecutive_failures = 0
        self.total_checks = 0
        self.total_failures = 0
        
        # 测试端点列表
        self.test_endpoints = [
            {
                'name': '币安API时间',
                'url': 'https://api.binance.com/api/v3/time',
                'timeout': 10,
                'critical': True
            },
            {
                'name': '币安K线API',
                'url': 'https://api.binance.com/api/v3/klines',
                'params': {'symbol': 'BTCUSDT', 'interval': '1m', 'limit': 1},
                'timeout': 10,
                'critical': True
            },
            {
                'name': 'Google DNS',
                'url': 'https://dns.google/resolve',
                'params': {'name': 'api.binance.com', 'type': 'A'},
                'timeout': 5,
                'critical': False
            }
        ]
    
    async def check_network_health(self) -> Dict:
        """执行网络健康检查"""
        check_start_time = datetime.now()
        results = {
            'timestamp': check_start_time.isoformat(),
            'overall_status': 'HEALTHY',
            'endpoints': [],
            'summary': {
                'total_endpoints': len(self.test_endpoints),
                'successful': 0,
                'failed': 0,
                'critical_failed': 0
            }
        }
        
        logger.info("🔍 开始网络健康检查...")
        
        for endpoint in self.test_endpoints:
            endpoint_result = await self._check_endpoint(endpoint)
            results['endpoints'].append(endpoint_result)
            
            if endpoint_result['status'] == 'SUCCESS':
                results['summary']['successful'] += 1
            else:
                results['summary']['failed'] += 1
                if endpoint['critical']:
                    results['summary']['critical_failed'] += 1
        
        # 判断整体状态
        if results['summary']['critical_failed'] > 0:
            results['overall_status'] = 'CRITICAL'
        elif results['summary']['failed'] > 0:
            results['overall_status'] = 'WARNING'
        
        # 更新统计
        self.total_checks += 1
        if results['overall_status'] != 'HEALTHY':
            self.total_failures += 1
            self.consecutive_failures += 1
        else:
            self.consecutive_failures = 0
        
        self.last_check_time = check_start_time
        self.check_history.append(results)
        
        # 保留最近100次检查记录
        if len(self.check_history) > 100:
            self.check_history = self.check_history[-100:]
        
        check_duration = (datetime.now() - check_start_time).total_seconds()
        logger.info(f"✅ 网络健康检查完成，耗时 {check_duration:.2f}s，状态: {results['overall_status']}")
        
        return results
    
    async def _check_endpoint(self, endpoint: Dict) -> Dict:
        """检查单个端点"""
        start_time = time.time()
        result = {
            'name': endpoint['name'],
            'url': endpoint['url'],
            'status': 'SUCCESS',
            'response_time': 0,
            'error': None,
            'details': {}
        }
        
        try:
            logger.debug(f"检查端点: {endpoint['name']}")
            
            # 发起请求
            if 'params' in endpoint:
                response, error = HttpRequests.get(
                    endpoint['url'], 
                    params=endpoint['params'],
                    timeout=endpoint['timeout']
                )
            else:
                response, error = HttpRequests.get(
                    endpoint['url'],
                    timeout=endpoint['timeout']
                )
            
            result['response_time'] = round((time.time() - start_time) * 1000, 2)  # 毫秒
            
            if error:
                result['status'] = 'FAILED'
                result['error'] = str(error)
                logger.warning(f"端点 {endpoint['name']} 检查失败: {error}")
            else:
                result['details'] = {
                    'response_size': len(str(response)) if response else 0,
                    'has_data': bool(response)
                }
                logger.debug(f"端点 {endpoint['name']} 检查成功，响应时间: {result['response_time']}ms")
                
        except Exception as e:
            result['status'] = 'FAILED'
            result['error'] = str(e)
            result['response_time'] = round((time.time() - start_time) * 1000, 2)
            logger.error(f"端点 {endpoint['name']} 检查异常: {e}")
        
        return result
    
    def get_health_summary(self) -> Dict:
        """获取健康状态摘要"""
        if not self.check_history:
            return {
                'status': 'UNKNOWN',
                'message': '尚未进行健康检查'
            }
        
        latest_check = self.check_history[-1]
        success_rate = ((self.total_checks - self.total_failures) / self.total_checks * 100) if self.total_checks > 0 else 0
        
        # 计算最近10次检查的成功率
        recent_checks = self.check_history[-10:]
        recent_success = sum(1 for check in recent_checks if check['overall_status'] == 'HEALTHY')
        recent_success_rate = (recent_success / len(recent_checks) * 100) if recent_checks else 0
        
        return {
            'current_status': latest_check['overall_status'],
            'last_check_time': latest_check['timestamp'],
            'consecutive_failures': self.consecutive_failures,
            'total_checks': self.total_checks,
            'total_failures': self.total_failures,
            'overall_success_rate': round(success_rate, 1),
            'recent_success_rate': round(recent_success_rate, 1),
            'last_check_summary': {
                'successful_endpoints': latest_check['summary']['successful'],
                'failed_endpoints': latest_check['summary']['failed'],
                'critical_failures': latest_check['summary']['critical_failed']
            }
        }
    
    def format_health_report(self) -> str:
        """格式化健康报告"""
        summary = self.get_health_summary()
        
        if summary['status'] == 'UNKNOWN':
            return "📊 网络健康状态: 未知（尚未检查）"
        
        status_emoji = {
            'HEALTHY': '✅',
            'WARNING': '⚠️',
            'CRITICAL': '🔴'
        }
        
        emoji = status_emoji.get(summary['current_status'], '❓')
        
        report = f"""📊 **网络健康报告** {emoji}

🔍 **当前状态**: {summary['current_status']}
⏰ **最后检查**: {summary['last_check_time'][:19]}
📈 **总体成功率**: {summary['overall_success_rate']}%
📊 **近期成功率**: {summary['recent_success_rate']}% (最近10次)

📋 **统计信息**:
• 总检查次数: {summary['total_checks']}
• 总失败次数: {summary['total_failures']}
• 连续失败: {summary['consecutive_failures']}次

🎯 **最近检查结果**:
• 成功端点: {summary['last_check_summary']['successful_endpoints']}
• 失败端点: {summary['last_check_summary']['failed_endpoints']}
• 关键失败: {summary['last_check_summary']['critical_failures']}"""

        return report
    
    async def continuous_monitoring(self, interval_seconds: int = 300):
        """持续监控模式"""
        logger.info(f"🔄 启动网络健康持续监控，检查间隔: {interval_seconds}秒")
        
        while True:
            try:
                await self.check_network_health()
                await asyncio.sleep(interval_seconds)
            except Exception as e:
                logger.error(f"持续监控异常: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟后继续


# 全局实例
network_health_checker = NetworkHealthChecker()


async def quick_network_check() -> bool:
    """快速网络检查，返回是否健康"""
    try:
        result = await network_health_checker.check_network_health()
        return result['overall_status'] == 'HEALTHY'
    except Exception as e:
        logger.error(f"快速网络检查失败: {e}")
        return False


def get_network_status() -> str:
    """获取网络状态字符串"""
    try:
        summary = network_health_checker.get_health_summary()
        return summary.get('current_status', 'UNKNOWN')
    except Exception:
        return 'UNKNOWN'
