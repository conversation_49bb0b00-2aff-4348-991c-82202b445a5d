"""
DingTalk Message Tool.

Author: <PERSON><PERSON>Hertel
Email:  <EMAIL>
Date:   2022-02-19
"""

from quant.config import config
from quant.order import Order
from quant.asset import Asset
from quant.position import Position
from quant.utils import tools
from quant.utils.http_client import HttpRequests


class Dingtalk:
	
	@classmethod
	def text(cls, text, token: str = None):
		"""Send text message."""
		params = {
			"msgtype": "text",
			"at": {"atMobiles": [""], "isAtAll": False},
			"text": {"content": "交易提醒:" + text},
		}
		headers = {"Content-Type": "application/json;charset=utf-8"}
		url = token or config.dingtalk
		try:
			result, error = HttpRequests.post(url, json=params, headers=headers)
			if error:
				return None, str(error)

			# result 已经是解析后的JSON数据
			errcode = result.get("errcode", -1)
			errmsg = result.get("errmsg", "未知错误")
			if errcode != 0:
				return None, errmsg
			return result, None
		except Exception as e:
			return None, str(e)
	
	@classmethod
	def markdown(cls, content, token: str = None):
		"""Send markdown message."""
		url = token or config.dingtalk
		headers = {"Content-Type": "application/json"}
		params = {"msgtype": "markdown", "markdown": {"title": "交易提醒", "text": content}}
		try:
			result, error = HttpRequests.post(url, json=params, headers=headers)
			if error:
				return None, str(error)

			# result 已经是解析后的JSON数据
			errcode = result.get("errcode", -1)
			errmsg = result.get("errmsg", "未知错误")
			if errcode != 0:
				return None, errmsg
			return result, None
		except Exception as e:
			return None, str(e)
	
	@classmethod
	def send_order_message(cls, order: Order, token: str = None):
		"""Send order message."""
		
		content = "### 订单更新推送\n\n" \
		          "> **交易平台:** {platform}\n\n" \
		          "> **交易币对:** {symbol}\n\n" \
		          "> **订单方向:** {action}\n\n" \
		          "> **订单类型:** {order_type}\n\n" \
		          "> **订单编号:** {order_id}\n\n" \
		          "> **订单状态:** {status}\n\n" \
		          "> **剩余数量:** {remain}\n\n" \
		          "> **委托数量:** {quantity}\n\n" \
		          "> **委托均价:** {price}\n\n" \
		          "> **成交数量:** {filled_quantity}\n\n" \
		          "> **成交均价:** {avg_price}\n\n" \
		          "> **手续费用:** {fee}\n\n" \
		          "> **创建时间:** {timestamp}\n\n" \
		          "> **更新时间:** {utime}".format(
					platform=order.platform,
					symbol=order.symbol,
					action=order.action,
					order_type=order.order_type,
					order_id=order.order_no,
					status=order.status,
					remain=order.remain,
					quantity=order.quantity,
					price=order.price,
					filled_quantity=order.filled_qty,
					avg_price=order.avg_price,
					fee=order.fee,
					timestamp=tools.ts_to_datetime_str(order.timestamp / 1000),
					utime=tools.ts_to_datetime_str(order.utime / 1000)
		)
		
		success, error = cls.markdown(content=content, token=token)
		return success, error
	
	@classmethod
	def send_asset_message(cls, asset: Asset, token: str = None):
		"""Send asset message."""
		
		content = "### 资产更新推送\n\n" \
		          "> **交易平台:** {platform}\n\n" \
		          "> **资产名称:** {currency}\n\n" \
		          "> **账户总额:** {total}\n\n" \
		          "> **冻结金额:** {locked}\n\n" \
		          "> **可用余额:** {free}\n\n" \
		          "> **更新时间:** {timestamp}".format(
					platform=asset.platform,
					currency=asset.currency,
					total=asset.total,
					locked=asset.locked,
					free=asset.free,
					timestamp=tools.ts_to_datetime_str(asset.timestamp / 1000)
		)
		
		success, error = cls.markdown(content=content, token=token)
		return success, error
	
	@classmethod
	def send_position_message(cls, position: Position, token: str = None):
		"""Send position message."""
		
		content = "### 持仓更新推送\n\n" \
		          "> **交易平台:** {platform}\n\n" \
		          "> **交易币对:** {symbol}\n\n" \
		          "> **多头数量:** {long_quantity}\n\n" \
		          "> **多头均价:** {long_avg_price}\n\n" \
		          "> **空头数量:** {short_quantity}\n\n" \
		          "> **空头均价:** {short_avg_price}\n\n" \
		          "> **更新时间:** {timestamp}".format(
					platform=position.platform,
					symbol=position.symbol,
					long_quantity=position.long_quantity,
					long_avg_price=position.long_avg_price,
					short_quantity=position.short_quantity,
					short_avg_price=position.short_avg_price,
					timestamp=tools.ts_to_datetime_str(position.utime / 1000)
		)
		
		success, error = cls.markdown(content=content, token=token)
		return success, error