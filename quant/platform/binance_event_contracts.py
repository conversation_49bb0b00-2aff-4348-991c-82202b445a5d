"""
Binance Event Contracts API Implementation

事件合约是币安推出的创新衍生品，允许用户对特定事件结果进行预测交易。
每个合约代表对事件结果的"是"或"否"的预测。

Author: HertelQuant Enhanced
Date: 2025-01-21
"""

import base64
import hashlib
import hmac
import json
import time
from typing import List, Tuple, Union, Dict, Any
from urllib.parse import urljoin

from quant import const
from quant.asset import Asset
from quant.config import config
from quant.market import Kline, Orderbook, Trade, Ticker
from quant.position import Position
from quant.order import Order
from quant.quant import Quant
from quant.utils import logger, tools
from quant.utils.decorator import method_locker
from quant.utils.http_client import HttpRequests
from quant.utils.web import Websockets


class BinanceEventContractsRestApi:
    """Binance Event Contracts REST API client.
    
    事件合约API客户端，用于查询和交易事件合约。
    
    Attributes:
        access_key: Account's ACCESS KEY.
        secret_key: Account's SECRET KEY.
        host: HTTP request host, default is `https://eapi.binance.com`.
    """

    def __init__(self, access_key: str = None, secret_key: str = None, host: str = None):
        """initialize REST API client."""
        # 事件合约使用专门的API域名
        self._host = host or "https://eapi.binance.com"
        self._access_key = access_key or config.platforms["binance"]["access_key"]
        self._secret_key = secret_key or config.platforms["binance"]["secret_key"]

    def request(self, method, uri, params=None, body=None, headers=None, auth=False):
        """Do HTTP request.
        
        Args:
            method: HTTP request method. `GET` / `POST` / `DELETE` / `PUT`.
            uri: HTTP request uri.
            params: HTTP query params.
            body: HTTP request body.
            headers: HTTP request headers.
            auth: If this request requires authentication.
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        if params:
            query = "&".join(["{}={}".format(k, params[k]) for k in sorted(params.keys())])
            uri += "?" + query
        url = urljoin(self._host, uri)

        if auth:
            ts = str(int(time.time() * 1000))
            if params:
                query += "&timestamp={}".format(ts)
            else:
                query = "timestamp={}".format(ts)
            signature = hmac.new(self._secret_key.encode(), query.encode(), hashlib.sha256).hexdigest()
            if params:
                uri += "&timestamp={}&signature={}".format(ts, signature)
            else:
                uri += "?timestamp={}&signature={}".format(ts, signature)
            url = urljoin(self._host, uri)
            if not headers:
                headers = {}
            headers["X-MBX-APIKEY"] = self._access_key
        _, success, error = HttpRequests.fetch(method, url, body=body, headers=headers, timeout=10)
        return success, error

    def get_exchange_info(self):
        """获取事件合约交易规则和合约信息.
        
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        success, error = self.request("GET", "/eapi/v1/exchangeInfo")
        return success, error

    def get_event_contracts(self, status: str = None):
        """获取事件合约列表.
        
        Args:
            status: 合约状态过滤 ('TRADING', 'SETTLING', 'SETTLED')
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {}
        if status:
            params["status"] = status
        success, error = self.request("GET", "/eapi/v1/events", params=params)
        return success, error

    def get_event_contract_detail(self, event_id: str):
        """获取特定事件合约详情.
        
        Args:
            event_id: 事件合约ID
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {"eventId": event_id}
        success, error = self.request("GET", "/eapi/v1/event", params=params)
        return success, error

    def get_event_orderbook(self, symbol: str, limit: int = 100):
        """获取事件合约订单簿.
        
        Args:
            symbol: 事件合约符号, e.g. `BTCUSD_250131_45000_C`.
            limit: 订单簿深度限制, 默认100.
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {"symbol": symbol, "limit": limit}
        success, error = self.request("GET", "/eapi/v1/depth", params=params)
        return success, error

    def get_event_ticker(self, symbol: str = None):
        """获取事件合约24小时价格变动统计.
        
        Args:
            symbol: 事件合约符号, 如果为None则返回所有合约.
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {}
        if symbol:
            params["symbol"] = symbol
        success, error = self.request("GET", "/eapi/v1/ticker/24hr", params=params)
        return success, error

    def get_event_trades(self, symbol: str, limit: int = 500):
        """获取事件合约最近成交记录.
        
        Args:
            symbol: 事件合约符号.
            limit: 返回记录数量限制, 默认500.
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {"symbol": symbol, "limit": limit}
        success, error = self.request("GET", "/eapi/v1/trades", params=params)
        return success, error

    def get_account_info(self):
        """获取事件合约账户信息.
        
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        success, error = self.request("GET", "/eapi/v1/account", auth=True)
        return success, error

    def get_account_balance(self):
        """获取事件合约账户余额.
        
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        success, error = self.request("GET", "/eapi/v1/balance", auth=True)
        return success, error

    def get_positions(self, symbol: str = None):
        """获取事件合约持仓信息.
        
        Args:
            symbol: 事件合约符号, 如果为None则返回所有持仓.
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {}
        if symbol:
            params["symbol"] = symbol
        success, error = self.request("GET", "/eapi/v1/position", params=params, auth=True)
        return success, error

    # ====== ❌ 下单接口已禁用，保留占位防误用 ======
    def create_order(self, *args, **kwargs):  # noqa
        """(Disabled) Create Event Contract order.

        由于币安事件合约当前不允许 API 下单，本方法已被禁用。调用将抛出
        NotImplementedError 以防止误用。
        """
        logger.error("[BinanceEventContractsRestApi] create_order 已禁用 -> 手动下单")
        raise NotImplementedError("Binance Event Contracts API 下单已禁用，请改为手动交易")

    def cancel_order(self, *args, **kwargs):  # noqa
        """(Disabled) Cancel Event Contract order.

        同上，取消订单接口也被禁用。
        """
        logger.error("[BinanceEventContractsRestApi] cancel_order 已禁用 -> 手动取消")
        raise NotImplementedError("Binance Event Contracts API 取消订单已禁用，请手动管理")

    def get_order_history(self, symbol: str = None, limit: int = 500):
        """获取事件合约订单历史.
        
        Args:
            symbol: 事件合约符号, 如果为None则返回所有订单.
            limit: 返回记录数量限制, 默认500.
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {"limit": limit}
        if symbol:
            params["symbol"] = symbol
        success, error = self.request("GET", "/eapi/v1/allOrders", params=params, auth=True)
        return success, error

    def get_trade_history(self, symbol: str = None, limit: int = 500):
        """获取事件合约成交历史.
        
        Args:
            symbol: 事件合约符号, 如果为None则返回所有成交.
            limit: 返回记录数量限制, 默认500.
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {"limit": limit}
        if symbol:
            params["symbol"] = symbol
        success, error = self.request("GET", "/eapi/v1/myTrades", params=params, auth=True)
        return success, error

    def parse_event_contract_symbol(self, symbol: str) -> Dict[str, Any]:
        """解析事件合约符号.
        
        事件合约符号格式: BTCUSD_250131_45000_C
        - BTCUSD: 标的资产
        - 250131: 到期日期 (2025年1月31日)
        - 45000: 执行价格
        - C: 合约类型 (C=看涨, P=看跌)
        
        Args:
            symbol: 事件合约符号.
            
        Returns:
            解析后的合约信息字典.
        """
        try:
            parts = symbol.split('_')
            if len(parts) >= 4:
                return {
                    "underlying": parts[0],      # 标的资产
                    "expiry_date": parts[1],     # 到期日期
                    "strike_price": float(parts[2]),  # 执行价格
                    "contract_type": parts[3],   # 合约类型
                    "full_symbol": symbol
                }
            else:
                return {"full_symbol": symbol, "parsed": False}
        except Exception as e:
            logger.error(f"解析事件合约符号失败: {symbol}, 错误: {e}", caller=self)
            return {"full_symbol": symbol, "parsed": False, "error": str(e)}
