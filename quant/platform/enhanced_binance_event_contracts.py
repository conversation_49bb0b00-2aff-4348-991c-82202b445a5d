#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Enhanced Binance Event Contracts API Client

Author: HertelQuant Enhanced
Date: 2025-01-31
"""

import asyncio
import json
import threading
import time
import websocket
import pandas as pd
from datetime import datetime, timedelta
from collections import deque
from typing import Dict, List, Optional, Callable, Any
from urllib.parse import urljoin

from quant.config import config
from quant.utils import logger
from quant.utils.http_client import HttpRequests
from quant.platform.binance_event_contracts import BinanceEventContractsRestApi


class EventContractDataManager:
    """事件合约实时数据管理器"""
    
    def __init__(self, symbols: List[str] = None):
        """
        初始化数据管理器
        
        Args:
            symbols: 订阅的事件合约符号列表
        """
        self.symbols = symbols or []
        self.is_running = False
        self.ws = None
        self.thread = None
        self.lock = threading.Lock()
        
        # 数据存储
        self.price_data = {}  # 实时价格数据
        self.orderbook_data = {}  # 订单簿数据
        self.trade_data = deque(maxlen=1000)  # 交易数据
        self.ticker_data = {}  # 24小时统计数据
        
        # 回调函数
        self.on_price_update: Optional[Callable] = None
        self.on_orderbook_update: Optional[Callable] = None
        self.on_trade_update: Optional[Callable] = None
        self.on_ticker_update: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
        
        # WebSocket配置
        self.ws_base_url = "wss://nbstream.binance.com/eoptions/ws"
        self.reconnect_interval = 5  # 重连间隔（秒）
        self.max_reconnect_attempts = 10  # 最大重连次数
        self.reconnect_count = 0
        
        logger.info("EventContractDataManager initialized", caller=self)

    def start(self):
        """启动实时数据订阅"""
        if self.is_running:
            logger.warning("Data manager already running", caller=self)
            return
        
        self.is_running = True
        self.thread = threading.Thread(target=self._run_websocket, daemon=True)
        self.thread.start()
        logger.info("Event contract data manager started", caller=self)

    def stop(self):
        """停止实时数据订阅"""
        self.is_running = False
        if self.ws:
            self.ws.close()
        if self.thread:
            self.thread.join(timeout=5)
        logger.info("Event contract data manager stopped", caller=self)

    def add_symbol(self, symbol: str):
        """添加订阅符号"""
        with self.lock:
            if symbol not in self.symbols:
                self.symbols.append(symbol)
                logger.info(f"Added symbol: {symbol}", caller=self)

    def remove_symbol(self, symbol: str):
        """移除订阅符号"""
        with self.lock:
            if symbol in self.symbols:
                self.symbols.remove(symbol)
                logger.info(f"Removed symbol: {symbol}", caller=self)

    def get_price(self, symbol: str) -> Optional[float]:
        """获取指定符号的当前价格"""
        with self.lock:
            return self.price_data.get(symbol, {}).get('price')

    def get_orderbook(self, symbol: str) -> Optional[Dict]:
        """获取指定符号的订单簿"""
        with self.lock:
            return self.orderbook_data.get(symbol)

    def get_ticker(self, symbol: str) -> Optional[Dict]:
        """获取指定符号的24小时统计"""
        with self.lock:
            return self.ticker_data.get(symbol)

    def get_recent_trades(self, symbol: str = None, limit: int = 100) -> List[Dict]:
        """获取最近的交易记录"""
        with self.lock:
            trades = list(self.trade_data)
            if symbol:
                trades = [t for t in trades if t.get('symbol') == symbol]
            return trades[-limit:]

    def _run_websocket(self):
        """运行WebSocket连接"""
        while self.is_running:
            try:
                self._connect_websocket()
                self.reconnect_count = 0  # 重置重连计数
            except Exception as e:
                logger.error(f"WebSocket connection failed: {e}", caller=self)
                self._handle_reconnect()

    def _connect_websocket(self):
        """连接WebSocket"""
        if not self.symbols:
            logger.warning("No symbols to subscribe", caller=self)
            time.sleep(5)
            return

        # 构建订阅URL
        streams = []
        for symbol in self.symbols:
            symbol_lower = symbol.lower()
            streams.extend([
                f"{symbol_lower}@ticker",       # 24小时统计
                f"{symbol_lower}@depth10@100ms", # 订单簿
                f"{symbol_lower}@trade"         # 实时交易
            ])
        
        stream_names = "/".join(streams)
        ws_url = f"{self.ws_base_url}/{stream_names}"
        
        logger.info(f"Connecting to WebSocket: {ws_url}", caller=self)
        
        self.ws = websocket.WebSocketApp(
            ws_url,
            on_message=self._on_message,
            on_error=self._on_error,
            on_close=self._on_close,
            on_open=self._on_open
        )
        
        self.ws.run_forever()

    def _on_open(self, ws):
        """WebSocket连接打开回调"""
        logger.info("WebSocket connection opened", caller=self)

    def _on_message(self, ws, message):
        """WebSocket消息回调"""
        try:
            data = json.loads(message)
            self._process_message(data)
        except Exception as e:
            logger.error(f"Failed to process WebSocket message: {e}", caller=self)

    def _on_error(self, ws, error):
        """WebSocket错误回调"""
        logger.error(f"WebSocket error: {error}", caller=self)
        if self.on_error:
            self.on_error(error)

    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket连接关闭回调"""
        logger.info(f"WebSocket connection closed: {close_status_code} - {close_msg}", caller=self)

    def _process_message(self, data: Dict):
        """处理WebSocket消息"""
        if 'stream' not in data or 'data' not in data:
            return
        
        stream = data['stream']
        msg_data = data['data']
        
        # 解析流类型和符号
        if '@ticker' in stream:
            self._process_ticker_update(msg_data)
        elif '@depth' in stream:
            self._process_orderbook_update(msg_data)
        elif '@trade' in stream:
            self._process_trade_update(msg_data)

    def _process_ticker_update(self, data: Dict):
        """处理24小时统计更新"""
        symbol = data.get('s')
        if symbol:
            with self.lock:
                self.ticker_data[symbol] = {
                    'symbol': symbol,
                    'price_change': float(data.get('p', 0)),
                    'price_change_percent': float(data.get('P', 0)),
                    'weighted_avg_price': float(data.get('w', 0)),
                    'prev_close_price': float(data.get('x', 0)),
                    'last_price': float(data.get('c', 0)),
                    'last_qty': float(data.get('Q', 0)),
                    'bid_price': float(data.get('b', 0)),
                    'ask_price': float(data.get('a', 0)),
                    'open_price': float(data.get('o', 0)),
                    'high_price': float(data.get('h', 0)),
                    'low_price': float(data.get('l', 0)),
                    'volume': float(data.get('v', 0)),
                    'quote_volume': float(data.get('q', 0)),
                    'open_time': int(data.get('O', 0)),
                    'close_time': int(data.get('C', 0)),
                    'count': int(data.get('n', 0))
                }
                
                # 更新价格数据
                self.price_data[symbol] = {
                    'price': float(data.get('c', 0)),
                    'timestamp': int(data.get('C', 0))
                }
            
            if self.on_ticker_update:
                self.on_ticker_update(symbol, self.ticker_data[symbol])
            
            if self.on_price_update:
                self.on_price_update(symbol, self.price_data[symbol])

    def _process_orderbook_update(self, data: Dict):
        """处理订单簿更新"""
        symbol = data.get('s')
        if symbol:
            with self.lock:
                self.orderbook_data[symbol] = {
                    'symbol': symbol,
                    'bids': [[float(bid[0]), float(bid[1])] for bid in data.get('b', [])],
                    'asks': [[float(ask[0]), float(ask[1])] for ask in data.get('a', [])],
                    'timestamp': int(data.get('T', 0))
                }
            
            if self.on_orderbook_update:
                self.on_orderbook_update(symbol, self.orderbook_data[symbol])

    def _process_trade_update(self, data: Dict):
        """处理交易更新"""
        trade_info = {
            'symbol': data.get('s'),
            'trade_id': int(data.get('t', 0)),
            'price': float(data.get('p', 0)),
            'quantity': float(data.get('q', 0)),
            'buyer_order_id': int(data.get('b', 0)),
            'seller_order_id': int(data.get('a', 0)),
            'trade_time': int(data.get('T', 0)),
            'is_buyer_maker': bool(data.get('m', False)),
            'timestamp': datetime.now().isoformat()
        }
        
        with self.lock:
            self.trade_data.append(trade_info)
        
        if self.on_trade_update:
            self.on_trade_update(trade_info)

    def _handle_reconnect(self):
        """处理重连逻辑"""
        if not self.is_running:
            return
        
        self.reconnect_count += 1
        
        if self.reconnect_count <= self.max_reconnect_attempts:
            wait_time = min(self.reconnect_interval * self.reconnect_count, 60)
            logger.info(f"Reconnecting in {wait_time} seconds (attempt {self.reconnect_count})", caller=self)
            time.sleep(wait_time)
        else:
            logger.error("Max reconnect attempts reached, stopping", caller=self)
            self.is_running = False


class EnhancedBinanceEventContractsAPI:
    """增强版币安事件合约API客户端"""
    
    def __init__(self, access_key: str = None, secret_key: str = None, host: str = None):
        """
        初始化增强版API客户端
        
        Args:
            access_key: API访问密钥
            secret_key: API密钥
            host: API主机地址
        """
        # 初始化REST API客户端
        self.rest_api = BinanceEventContractsRestApi(access_key, secret_key, host)
        
        # 初始化实时数据管理器
        self.data_manager = EventContractDataManager()
        
        # 合约状态缓存
        self.contract_cache = {}
        self.cache_expiry = {}  # 缓存过期时间
        self.cache_duration = 300  # 缓存持续时间（秒）
        
        # 价格监控
        self.price_monitors = {}  # 价格监控器
        self.price_alerts = []   # 价格警报
        
        logger.info("Enhanced Binance Event Contracts API initialized", caller=self)

    def start_real_time_data(self, symbols: List[str] = None):
        """
        启动实时数据订阅
        
        Args:
            symbols: 要订阅的事件合约符号列表
        """
        if symbols:
            for symbol in symbols:
                self.data_manager.add_symbol(symbol)
        
        self.data_manager.start()
        logger.info(f"Real-time data started for symbols: {symbols}", caller=self)

    def stop_real_time_data(self):
        """停止实时数据订阅"""
        self.data_manager.stop()
        logger.info("Real-time data stopped", caller=self)

    def set_callbacks(self, 
                     on_price_update: Callable = None,
                     on_orderbook_update: Callable = None,
                     on_trade_update: Callable = None,
                     on_ticker_update: Callable = None,
                     on_error: Callable = None):
        """
        设置回调函数
        
        Args:
            on_price_update: 价格更新回调
            on_orderbook_update: 订单簿更新回调
            on_trade_update: 交易更新回调
            on_ticker_update: 统计数据更新回调
            on_error: 错误回调
        """
        self.data_manager.on_price_update = on_price_update
        self.data_manager.on_orderbook_update = on_orderbook_update
        self.data_manager.on_trade_update = on_trade_update
        self.data_manager.on_ticker_update = on_ticker_update
        self.data_manager.on_error = on_error

    def get_active_contracts(self, refresh_cache: bool = False) -> List[Dict]:
        """
        获取活跃的事件合约列表（带缓存）
        
        Args:
            refresh_cache: 是否刷新缓存
            
        Returns:
            活跃合约列表
        """
        cache_key = "active_contracts"
        
        # 检查缓存
        if not refresh_cache and self._is_cache_valid(cache_key):
            return self.contract_cache[cache_key]
        
        # 获取新数据
        success, error = self.rest_api.get_event_contracts(status="TRADING")
        
        if success:
            active_contracts = []
            for contract in success:
                # 解析合约信息
                parsed = self.rest_api.parse_event_contract_symbol(contract.get('symbol', ''))
                contract['parsed_info'] = parsed
                
                # 添加额外的分析信息
                contract['time_to_expiry'] = self._calculate_time_to_expiry(contract)
                contract['is_near_expiry'] = contract['time_to_expiry'] < 3600  # 1小时内到期
                
                active_contracts.append(contract)
            
            # 缓存结果
            self.contract_cache[cache_key] = active_contracts
            self.cache_expiry[cache_key] = datetime.now() + timedelta(seconds=self.cache_duration)
            
            logger.info(f"Retrieved {len(active_contracts)} active contracts", caller=self)
            return active_contracts
        else:
            logger.error(f"Failed to get active contracts: {error}", caller=self)
            return []

    def get_btc_contracts(self, refresh_cache: bool = False) -> List[Dict]:
        """
        获取BTC相关的事件合约
        
        Args:
            refresh_cache: 是否刷新缓存
            
        Returns:
            BTC合约列表
        """
        active_contracts = self.get_active_contracts(refresh_cache)
        btc_contracts = [
            contract for contract in active_contracts 
            if contract.get('parsed_info', {}).get('underlying', '').startswith('BTC')
        ]
        
        # 按到期时间排序
        btc_contracts.sort(key=lambda x: x.get('time_to_expiry', float('inf')))
        
        logger.info(f"Found {len(btc_contracts)} BTC contracts", caller=self)
        return btc_contracts

    def find_suitable_contracts(self, underlying: str = "BTC", 
                              min_time_to_expiry: int = 600,  # 10分钟
                              max_time_to_expiry: int = 1800,  # 30分钟
                              price_range: tuple = None) -> List[Dict]:
        """
        查找适合交易的合约
        
        Args:
            underlying: 标的资产 (如 "BTC")
            min_time_to_expiry: 最小到期时间（秒）
            max_time_to_expiry: 最大到期时间（秒）
            price_range: 价格范围 (min_price, max_price)
            
        Returns:
            符合条件的合约列表
        """
        active_contracts = self.get_active_contracts()
        suitable_contracts = []
        
        for contract in active_contracts:
            parsed_info = contract.get('parsed_info', {})
            
            # 检查标的资产
            if underlying and not parsed_info.get('underlying', '').startswith(underlying):
                continue
            
            # 检查到期时间
            time_to_expiry = contract.get('time_to_expiry', 0)
            if time_to_expiry < min_time_to_expiry or time_to_expiry > max_time_to_expiry:
                continue
            
            # 检查价格范围
            if price_range:
                strike_price = parsed_info.get('strike_price', 0)
                if strike_price < price_range[0] or strike_price > price_range[1]:
                    continue
            
            suitable_contracts.append(contract)
        
        # 按到期时间排序
        suitable_contracts.sort(key=lambda x: x.get('time_to_expiry', float('inf')))
        
        logger.info(f"Found {len(suitable_contracts)} suitable contracts", caller=self)
        return suitable_contracts

    def get_contract_market_data(self, symbol: str) -> Dict:
        """
        获取合约的市场数据
        
        Args:
            symbol: 合约符号
            
        Returns:
            市场数据字典
        """
        market_data = {
            'symbol': symbol,
            'current_price': self.data_manager.get_price(symbol),
            'orderbook': self.data_manager.get_orderbook(symbol),
            'ticker': self.data_manager.get_ticker(symbol),
            'recent_trades': self.data_manager.get_recent_trades(symbol, limit=10),
            'timestamp': datetime.now().isoformat()
        }
        
        # 如果没有实时数据，尝试从REST API获取
        if not market_data['current_price']:
            success, error = self.rest_api.get_event_ticker(symbol)
            if success:
                market_data['rest_ticker'] = success
                if isinstance(success, list) and len(success) > 0:
                    market_data['current_price'] = float(success[0].get('lastPrice', 0))
        
        return market_data

    def add_price_monitor(self, symbol: str, target_price: float, 
                         callback: Callable, condition: str = "above"):
        """
        添加价格监控
        
        Args:
            symbol: 合约符号
            target_price: 目标价格
            callback: 触发回调函数
            condition: 触发条件 ("above", "below", "equal")
        """
        monitor_id = f"{symbol}_{target_price}_{condition}"
        
        self.price_monitors[monitor_id] = {
            'symbol': symbol,
            'target_price': target_price,
            'callback': callback,
            'condition': condition,
            'created_at': datetime.now(),
            'triggered': False
        }
        
        logger.info(f"Added price monitor: {monitor_id}", caller=self)
        return monitor_id

    def remove_price_monitor(self, monitor_id: str):
        """移除价格监控"""
        if monitor_id in self.price_monitors:
            del self.price_monitors[monitor_id]
            logger.info(f"Removed price monitor: {monitor_id}", caller=self)

    def check_price_monitors(self):
        """检查价格监控触发条件"""
        for monitor_id, monitor in self.price_monitors.items():
            if monitor['triggered']:
                continue
            
            current_price = self.data_manager.get_price(monitor['symbol'])
            if not current_price:
                continue
            
            target_price = monitor['target_price']
            condition = monitor['condition']
            
            triggered = False
            if condition == "above" and current_price > target_price:
                triggered = True
            elif condition == "below" and current_price < target_price:
                triggered = True
            elif condition == "equal" and abs(current_price - target_price) < 0.01:
                triggered = True
            
            if triggered:
                monitor['triggered'] = True
                monitor['triggered_at'] = datetime.now()
                monitor['triggered_price'] = current_price
                
                try:
                    monitor['callback'](monitor_id, monitor)
                except Exception as e:
                    logger.error(f"Price monitor callback error: {e}", caller=self)

    def get_contract_analytics(self, symbol: str) -> Dict:
        """
        获取合约分析数据
        
        Args:
            symbol: 合约符号
            
        Returns:
            分析数据字典
        """
        market_data = self.get_contract_market_data(symbol)
        orderbook = market_data.get('orderbook', {})
        ticker = market_data.get('ticker', {})
        
        analytics = {
            'symbol': symbol,
            'current_price': market_data.get('current_price', 0),
            'bid_ask_spread': 0,
            'volume_24h': ticker.get('volume', 0),
            'price_change_24h': ticker.get('price_change', 0),
            'price_change_percent_24h': ticker.get('price_change_percent', 0),
            'liquidity_score': 0,
            'volatility': 0,
            'timestamp': datetime.now().isoformat()
        }
        
        # 计算买卖价差
        if orderbook and orderbook.get('bids') and orderbook.get('asks'):
            best_bid = orderbook['bids'][0][0] if orderbook['bids'] else 0
            best_ask = orderbook['asks'][0][0] if orderbook['asks'] else 0
            if best_bid and best_ask:
                analytics['bid_ask_spread'] = best_ask - best_bid
                analytics['bid_ask_spread_percent'] = (analytics['bid_ask_spread'] / best_ask) * 100

        # 计算流动性评分
        if orderbook:
            bid_volume = sum([bid[1] for bid in orderbook.get('bids', [])[:5]])  # 前5档买单量
            ask_volume = sum([ask[1] for ask in orderbook.get('asks', [])[:5]])  # 前5档卖单量
            analytics['liquidity_score'] = min(100, (bid_volume + ask_volume) * 10)

        return analytics

    def _calculate_time_to_expiry(self, contract: Dict) -> int:
        """
        计算合约到期时间（秒）
        
        Args:
            contract: 合约信息
            
        Returns:
            到期剩余时间（秒）
        """
        try:
            # 从合约信息中获取到期时间
            end_date = contract.get('endDate')
            if end_date:
                expiry_time = datetime.fromtimestamp(end_date / 1000)
                now = datetime.now()
                return max(0, int((expiry_time - now).total_seconds()))
        except Exception as e:
            logger.error(f"Failed to calculate time to expiry: {e}", caller=self)
        
        return 0

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.contract_cache:
            return False
        
        if cache_key not in self.cache_expiry:
            return False
        
        return datetime.now() < self.cache_expiry[cache_key]

    # 代理REST API方法
    def get_exchange_info(self):
        """获取交易所信息"""
        return self.rest_api.get_exchange_info()

    def get_account_info(self):
        """获取账户信息"""
        return self.rest_api.get_account_info()

    def get_account_balance(self):
        """获取账户余额"""
        return self.rest_api.get_account_balance()

    def create_order(self, symbol: str, side: str, quantity: float, 
                    price: float = None, order_type: str = "LIMIT"):
        """创建订单"""
        return self.rest_api.create_order(symbol, side, quantity, price, order_type)

    def cancel_order(self, symbol: str, order_id: str = None, 
                    orig_client_order_id: str = None):
        """取消订单"""
        return self.rest_api.cancel_order(symbol, order_id, orig_client_order_id)

    def get_order_history(self, symbol: str = None, limit: int = 500):
        """获取订单历史"""
        return self.rest_api.get_order_history(symbol, limit)

    def get_trade_history(self, symbol: str = None, limit: int = 500):
        """获取交易历史"""
        return self.rest_api.get_trade_history(symbol, limit)